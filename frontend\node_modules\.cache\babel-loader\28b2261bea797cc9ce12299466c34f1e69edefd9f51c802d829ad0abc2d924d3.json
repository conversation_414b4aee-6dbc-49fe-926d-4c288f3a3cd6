{"ast": null, "code": "import { getDbTables, getDbTableColumns, getDbTableRows } from '@/api/dbAdmin';\nimport { mapState } from 'vuex';\nimport { formatDate } from '@/utils/date';\nexport default {\n  name: 'DatabaseViewer',\n  data() {\n    return {\n      tables: [],\n      selectedTable: '',\n      columns: [],\n      rows: [],\n      total: 0,\n      page: 1,\n      pageSize: 20,\n      loading: false,\n      inited: false,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n      // 表字段注释对照表\n      fieldComments: {\n        // admin表：系统管理员\n        'admin.id': '管理员唯一ID',\n        'admin.username': '管理员登录账号',\n        'admin.password_hash': '加密后的登录密码',\n        'admin.name': '管理员姓名',\n        'admin.role': '管理员角色（如superadmin）',\n        'admin.is_active': '账号是否激活（1激活，0禁用）',\n        'admin.created_at': '账号创建时间',\n        // announcements表：公告表\n        'announcements.id': '公告ID',\n        'announcements.title': '公告标题',\n        'announcements.content': '公告内容',\n        'announcements.created_at': '公告创建（发布）时间',\n        'announcements.is_active': '公告是否启用',\n        // email_logs表：系统邮件发送日志\n        'email_logs.id': '日志唯一ID',\n        'email_logs.recipient': '收件人邮箱',\n        'email_logs.subject': '邮件主题',\n        'email_logs.template_key': '邮件模板标识',\n        'email_logs.event_type': '触发邮件的事件类型',\n        'email_logs.status': '发送状态（如success/failed）',\n        'email_logs.error_message': '发送失败时的错误信息',\n        'email_logs.reservation_code': '关联预约的编码',\n        'email_logs.reservation_number': '关联预约的编号',\n        'email_logs.created_at': '日志创建时间',\n        'email_logs.content_html': '邮件HTML内容',\n        // email_settings表：邮件服务器配置\n        'email_settings.id': '配置唯一ID',\n        'email_settings.smtp_server': 'SMTP服务器地址',\n        'email_settings.smtp_port': 'SMTP服务器端口',\n        'email_settings.sender_email': '发件人邮箱',\n        'email_settings.sender_name': '发件人显示名称',\n        'email_settings.smtp_username': 'SMTP登录用户名',\n        'email_settings.smtp_password': 'SMTP登录密码',\n        'email_settings.use_ssl': '是否使用SSL加密（1是，0否）',\n        'email_settings.enabled': '配置是否启用（1启用，0禁用）',\n        'email_settings.created_at': '配置创建时间',\n        'email_settings.updated_at': '配置更新时间',\n        'email_settings.cc_list': '抄送人列表，多个邮箱用逗号分隔',\n        'email_settings.bcc_list': '密送人列表，多个邮箱用逗号分隔',\n        // email_templates表：邮件模板\n        'email_templates.id': '模板唯一ID',\n        'email_templates.name': '模板名称',\n        'email_templates.template_key': '模板标识（代码）',\n        'email_templates.subject': '邮件主题模板',\n        'email_templates.content_html': 'HTML格式邮件内容',\n        'email_templates.content_text': '纯文本邮件内容',\n        'email_templates.variables': '可用变量说明',\n        'email_templates.language': '模板语言',\n        'email_templates.created_at': '模板创建时间',\n        'email_templates.updated_at': '模板更新时间',\n        // equipment表：可预约设备\n        'equipment.id': '设备唯一ID',\n        'equipment.name': '设备名称',\n        'equipment.category': '设备类别名称',\n        'equipment.model': '设备型号',\n        'equipment.location': '设备存放位置',\n        'equipment.status': '设备状态（如可用/维修/借出）',\n        'equipment.description': '设备详细描述',\n        'equipment.image_path': '设备图片路径',\n        'equipment.user_guide': '设备使用说明',\n        'equipment.created_at': '设备信息创建时间',\n        'equipment.updated_at': '设备信息更新时间',\n        'equipment.video_tutorial': '设备视频教程地址',\n        'equipment.category_id': '设备类别ID',\n        'equipment.allow_simultaneous': '是否允许同时多人预约（1允许，0不允许）',\n        'equipment.max_simultaneous': '最大同时预约人数',\n        // equipment_category表：设备类别\n        'equipment_category.id': '类别唯一ID',\n        'equipment_category.name': '类别名称',\n        'equipment_category.description': '类别描述',\n        // equipment_time_slots表：设备时间段\n        'equipment_time_slots.id': '时间段唯一ID',\n        'equipment_time_slots.equipment_id': '关联的设备ID',\n        'equipment_time_slots.start_datetime': '时间段开始时间',\n        'equipment_time_slots.end_datetime': '时间段结束时间',\n        'equipment_time_slots.current_count': '当前时间段内的预约数量',\n        // recurring_reservation表：周期性预约\n        'recurring_reservation.id': '周期预约唯一ID',\n        'recurring_reservation.equipment_id': '设备ID',\n        'recurring_reservation.pattern_type': '重复模式类型（如每周/每月）',\n        'recurring_reservation.days_of_week': '每周重复的星期（如1,3,5）',\n        'recurring_reservation.days_of_month': '每月重复的日期（如5,15,25）',\n        'recurring_reservation.start_date': '预约周期开始日期',\n        'recurring_reservation.end_date': '预约周期结束日期',\n        'recurring_reservation.start_time': '每天预约开始时间',\n        'recurring_reservation.end_time': '每天预约结束时间',\n        'recurring_reservation.user_name': '预约人姓名',\n        'recurring_reservation.user_department': '预约人部门',\n        'recurring_reservation.user_contact': '预约人联系方式',\n        'recurring_reservation.user_email': '预约人邮箱',\n        'recurring_reservation.purpose': '预约用途',\n        'recurring_reservation.status': '周期预约状态',\n        'recurring_reservation.created_at': '创建时间',\n        'recurring_reservation.reservation_code': '周期预约编码',\n        'recurring_reservation.conflicts': '冲突日期列表，逗号分隔的YYYY-MM-DD格式',\n        'recurring_reservation.total_planned': '计划创建的子预约总数',\n        'recurring_reservation.created_count': '成功创建的子预约数量',\n        // reservation表：单次预约\n        'reservation.id': '预约唯一ID',\n        'reservation.equipment_id': '设备ID',\n        'reservation.reservation_code': '预约编码',\n        'reservation.user_name': '预约人姓名',\n        'reservation.user_department': '预约人部门',\n        'reservation.user_contact': '预约人联系方式',\n        'reservation.user_email': '预约人邮箱',\n        'reservation.start_datetime': '预约开始时间',\n        'reservation.end_datetime': '预约结束时间',\n        'reservation.purpose': '预约用途',\n        'reservation.status': '预约状态',\n        'reservation.created_at': '预约创建时间',\n        'reservation.recurring_reservation_id': '关联的周期预约ID',\n        'reservation.is_exception': '是否为周期预约的特例',\n        'reservation.reservation_number': '预约编号',\n        'reservation.time_slot_id': '关联的设备时间段ID(整数)',\n        // system_settings表：系统设置\n        'system_settings.id': '设置唯一ID',\n        'system_settings.site_name': '系统站点名称',\n        'system_settings.maintenance_mode': '维护模式开关（1开启，0关闭）',\n        'system_settings.reservation_limit_per_day': '单日预约上限',\n        'system_settings.allow_equipment_conflict': '是否允许设备冲突预约（1允许，0不允许）',\n        'system_settings.advance_reservation_days': '可提前预约天数',\n        'system_settings.created_at': '设置创建时间',\n        'system_settings.updated_at': '设置更新时间'\n      }\n    };\n  },\n  computed: {\n    ...mapState({\n      user: state => state.user\n    }),\n    isSuperAdmin() {\n      // 允许所有管理员访问数据库表查看功能\n      return this.user && (this.user.role === 'superadmin' || this.user.role === 'admin');\n    },\n    // 判断是否为小表格（列少的表格）\n    isSmallTable() {\n      const smallTables = ['admin', 'equipment_category', 'system_settings', 'announcements', 'equipment_time_slots', 'reservation_history'];\n      return !smallTables.includes(this.selectedTable);\n    },\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile ? 'prev, next' : 'total, sizes, prev, pager, next, jumper';\n    }\n  },\n  created() {\n    console.log(\"DatabaseViewer 组件 created\");\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  mounted() {\n    console.log(\"DatabaseViewer 组件 mounted, 调用 initIfNeeded\");\n    this.initIfNeeded();\n  },\n  methods: {\n    async initIfNeeded() {\n      console.log(\"initIfNeeded 被调用，inited=\", this.inited, \"isSuperAdmin=\", this.isSuperAdmin);\n      if (!this.inited && this.isSuperAdmin) {\n        this.inited = true;\n        await this.fetchTables();\n      }\n    },\n    async fetchTables() {\n      console.log(\"开始获取数据库表名...\");\n      try {\n        console.log(\"调用 getDbTables()\");\n        const res = await getDbTables();\n        console.log(\"获取表名结果:\", res);\n        this.tables = res.data.tables || [];\n        if (this.tables.length > 0) {\n          this.handleTableSelect(this.tables[0]);\n        }\n      } catch (e) {\n        console.error(\"获取表名失败:\", e);\n        this.$message.error('获取表名失败: ' + (e.message || e));\n      }\n    },\n    async handleTableSelect(table) {\n      this.selectedTable = table;\n      this.page = 1;\n      await this.fetchTableColumns();\n      await this.fetchTableRows();\n    },\n    async fetchTableColumns() {\n      try {\n        const res = await getDbTableColumns(this.selectedTable);\n        // 兼容不同数据库字段名\n        this.columns = (res.data.columns || []).map(col => ({\n          name: col.name || col.column_name,\n          type: col.type || col.type_name,\n          nullable: col.nullable !== undefined ? col.nullable : col.nullable_,\n          default: col.default,\n          comment: col.comment || ''\n        }));\n      } catch (e) {\n        this.columns = [];\n        this.$message.error('获取字段信息失败');\n      }\n    },\n    async fetchTableRows() {\n      this.loading = true;\n      try {\n        const res = await getDbTableRows(this.selectedTable, {\n          skip: (this.page - 1) * this.pageSize,\n          limit: this.pageSize\n        });\n        this.rows = res.data.rows || [];\n\n        // 使用后端返回的总行数\n        if (res.data.total !== undefined) {\n          this.total = res.data.total;\n        } else {\n          // 兼容旧版API，如果后端没有返回总行数，则使用简单估算\n          if (this.rows.length < this.pageSize) {\n            // 当前页不满，可能是最后一页\n            this.total = (this.page - 1) * this.pageSize + this.rows.length;\n          } else {\n            // 当前页是满的，假设至少还有一页\n            this.total = this.page * this.pageSize + this.pageSize;\n          }\n        }\n      } catch (e) {\n        this.rows = [];\n        this.$message.error('获取表数据失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    handlePageChange(page) {\n      this.page = page;\n      this.fetchTableRows();\n    },\n    handleSizeChange(size) {\n      this.pageSize = size;\n      this.page = 1;\n      this.fetchTableRows();\n    },\n    refreshTable() {\n      this.fetchTableColumns();\n      this.fetchTableRows();\n    },\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    // 获取字段注释 - 简化版本\n    getFieldComment(fieldName) {\n      const key = `${this.selectedTable}.${fieldName}`;\n      return this.fieldComments[key] || '暂无注释';\n    },\n    // 获取最小列宽（用于自适应模式的表格）\n    getColumnMinWidth(columnName) {\n      const lowerColumnName = columnName.toLowerCase();\n\n      // 针对特定表格中的特定列进行特殊处理\n      if (this.selectedTable === 'admin') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'username') return 150;\n        if (lowerColumnName === 'password_hash') return 300;\n        if (lowerColumnName === 'name') return 150;\n        if (lowerColumnName === 'role') return 120;\n        if (lowerColumnName === 'is_active') return 100;\n        if (lowerColumnName === 'created_at') return 180;\n        return 150;\n      }\n      if (this.selectedTable === 'equipment_category') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'name') return 200;\n        if (lowerColumnName === 'description') return 300;\n        if (lowerColumnName === 'created_at') return 180;\n        return 200;\n      }\n      if (this.selectedTable === 'system_settings') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'key') return 200;\n        if (lowerColumnName === 'value') return 400;\n        if (lowerColumnName === 'description') return 300;\n        return 200;\n      }\n\n      // 添加针对announcements表的最小列宽设置\n      if (this.selectedTable === 'announcements') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'title') return 250;\n        if (lowerColumnName === 'content') return 450;\n        if (lowerColumnName === 'created_at') return 180;\n        if (lowerColumnName === 'is_active') return 120;\n        return 150;\n      }\n\n      // 默认最小列宽\n      if (lowerColumnName.includes('content_html') || lowerColumnName.includes('html')) {\n        return 300;\n      } else if (lowerColumnName === 'id') {\n        return 80;\n      } else if (lowerColumnName.includes('date') || lowerColumnName.includes('time')) {\n        return 160;\n      } else {\n        return 120;\n      }\n    },\n    // 标准列宽（用于固定模式的表格）\n    getColumnWidth(columnName) {\n      const lowerColumnName = columnName.toLowerCase();\n\n      // 特殊表格特殊处理\n      if (this.selectedTable === 'email_logs' && lowerColumnName === 'content_html') {\n        return 500;\n      }\n\n      // 专门为announcements表添加列宽处理\n      if (this.selectedTable === 'announcements') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'title') return 200;\n        if (lowerColumnName === 'content') return 350;\n        if (lowerColumnName === 'created_at') return 180;\n        if (lowerColumnName === 'is_active') return 120;\n        return 150; // 其他可能的列\n      }\n\n      // 根据列名类型分配宽度\n      if (lowerColumnName.includes('content_html') || lowerColumnName.includes('html') || lowerColumnName.includes('content')) {\n        return 300;\n      } else if (lowerColumnName === 'id') {\n        return 80;\n      } else if (lowerColumnName.includes('date') || lowerColumnName.includes('time')) {\n        return 160;\n      } else if (lowerColumnName.includes('name')) {\n        return 120;\n      } else if (lowerColumnName.includes('code') || lowerColumnName.includes('number')) {\n        return 140;\n      } else if (lowerColumnName.includes('title')) {\n        return 120;\n      } else if (lowerColumnName.includes('email')) {\n        return 180;\n      } else if (lowerColumnName.includes('description') || lowerColumnName.includes('comment')) {\n        return 200;\n      } else if (lowerColumnName.includes('status')) {\n        return 120;\n      } else if (lowerColumnName.includes('password') || lowerColumnName.includes('hash')) {\n        return 250;\n      } else {\n        return 120;\n      }\n    },\n    // 格式化单元格内容\n    formatCell(row, column, cellValue) {\n      if (cellValue === null || cellValue === undefined) {\n        return '';\n      }\n\n      // 如果是HTML内容，只显示部分文本并添加提示\n      if (column.property.toLowerCase().includes('content_html') || column.property.toLowerCase().includes('html')) {\n        if (typeof cellValue === 'string' && cellValue.length > 100) {\n          return cellValue.substring(0, 100) + '...';\n        }\n      }\n      const tableName = this.selectedTable;\n      const columnName = column.property;\n\n      // 特殊处理recurring_reservation表的start_date和end_date，只显示日期部分\n      if (tableName === 'recurring_reservation' && (columnName === 'start_date' || columnName === 'end_date')) {\n        if (typeof cellValue === 'string' && cellValue.length >= 10) {\n          return cellValue.substring(0, 10); // 只保留YYYY-MM-DD部分\n        }\n      }\n\n      // 特殊处理recurring_reservation表的start_time和end_time，移除微秒\n      if (tableName === 'recurring_reservation' && (columnName === 'start_time' || columnName === 'end_time')) {\n        if (typeof cellValue === 'string') {\n          // 如果包含空格（例如\"09:00:00 000000\"）\n          if (cellValue.includes(' ')) {\n            return cellValue.split(' ')[0]; // 只保留时间部分\n          }\n          // 如果只有时间部分\n          return cellValue;\n        }\n      }\n\n      // 对日期时间格式化，不进行时区转换\n      // 排除明确是ID字段的列，比如time_slot_id\n      if (column.property !== 'time_slot_id' && column.property !== 'timeslot_number' && (column.property.toLowerCase().includes('date') || column.property.toLowerCase().includes('time')) && !isNaN(Date.parse(cellValue))) {\n        try {\n          // 以下字段已经是北京时间，不需要再转换\n          const skipTimeZoneConversion = ['updated_at', 'created_at', 'start_datetime', 'end_datetime'];\n\n          // 不进行时区转换，直接显示数据库中的原始时间\n          const toBeijingTime = false;\n\n          // 根据字段类型选择显示格式\n          let format = 'YYYY-MM-DD HH:mm:ss';\n\n          // 只显示日期部分\n          if (columnName === 'start_date' || columnName === 'end_date') {\n            format = 'YYYY-MM-DD';\n          }\n\n          // 只显示时间部分\n          if (columnName === 'start_time' || columnName === 'end_time') {\n            format = 'HH:mm:ss';\n          }\n          return formatDate(cellValue, format, toBeijingTime);\n        } catch (e) {\n          console.error('日期格式化错误:', e);\n          return cellValue;\n        }\n      }\n      return cellValue;\n    }\n  }\n};", "map": {"version": 3, "names": ["getDbTables", "getDbTableColumns", "getDbTableRows", "mapState", "formatDate", "name", "data", "tables", "selectedTable", "columns", "rows", "total", "page", "pageSize", "loading", "inited", "isMobile", "window", "innerWidth", "fieldComments", "computed", "user", "state", "isSuperAdmin", "role", "isSmallTable", "smallTables", "includes", "paginationLayout", "created", "console", "log", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "mounted", "initIfNeeded", "methods", "fetchTables", "res", "length", "handleTableSelect", "e", "error", "$message", "message", "table", "fetchTableColumns", "fetchTableRows", "map", "col", "column_name", "type", "type_name", "nullable", "undefined", "nullable_", "default", "comment", "skip", "limit", "handlePageChange", "handleSizeChange", "size", "refreshTable", "getFieldComment", "fieldName", "key", "getColumnMinWidth", "columnName", "lowerColumnName", "toLowerCase", "getColumnWidth", "formatCell", "row", "column", "cellValue", "property", "substring", "tableName", "split", "isNaN", "Date", "parse", "skipTimeZoneConversion", "toBeijingTime", "format"], "sources": ["src/views/admin/DatabaseViewer.vue"], "sourcesContent": ["<template>\n  <div class=\"db-viewer\" v-if=\"isSuperAdmin\">\n    <!-- 移动端提示 -->\n    <div v-if=\"isMobile\" class=\"mobile-notice\">\n      <el-card shadow=\"hover\">\n        <div style=\"text-align: center; padding: 40px 20px;\">\n          <i class=\"el-icon-monitor\" style=\"font-size: 48px; color: #409EFF; margin-bottom: 20px;\"></i>\n          <h3>数据库查看器</h3>\n          <p style=\"color: #666; margin: 20px 0;\">\n            数据库查看功能需要在桌面端使用，以获得更好的表格显示效果。\n          </p>\n          <p style=\"color: #666; font-size: 14px;\">\n            请使用电脑或平板设备访问此功能。\n          </p>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 桌面端完整功能 -->\n    <el-row v-else>\n      <el-col :span=\"4\" class=\"db-tables-list\">\n        <el-card shadow=\"never\" style=\"height: 100%\">\n          <div slot=\"header\"><b>数据库表</b></div>\n          <el-scrollbar style=\"height: 70vh\">\n            <el-menu :default-active=\"selectedTable\" @select=\"handleTableSelect\">\n              <el-menu-item v-for=\"table in tables\" :key=\"table\" :index=\"table\">\n                {{ table }}\n              </el-menu-item>\n            </el-menu>\n          </el-scrollbar>\n        </el-card>\n      </el-col>\n      <el-col :span=\"20\" class=\"db-table-content\">\n        <el-card shadow=\"never\" style=\"min-height: 70vh\">\n          <div slot=\"header\" class=\"db-table-header\">\n            <span v-if=\"selectedTable\"><b>{{ selectedTable }}</b>（共 {{ total }} 条）</span>\n            <el-button v-if=\"selectedTable\" size=\"mini\" icon=\"el-icon-refresh\" @click=\"refreshTable\" style=\"float: right;\">刷新</el-button>\n          </div>\n          <div v-if=\"selectedTable\" class=\"table-container\">\n            <!-- 主表格区域：保留水平滚动容器 -->\n            <div class=\"horizontal-scroll-container data-table-container\">\n              <el-table\n                :data=\"rows\"\n                border\n                size=\"small\"\n                :table-layout=\"isSmallTable ? 'fixed' : 'auto'\"\n                style=\"width: 100%\"\n                :height=\"540\"\n                class=\"custom-table\"\n                highlight-current-row\n              >\n                <el-table-column\n                  v-for=\"col in columns\"\n                  :key=\"col.name\"\n                  :prop=\"col.name\"\n                  :label=\"col.name\"\n                  :width=\"isSmallTable ? getColumnWidth(col.name) : ''\"\n                  :min-width=\"isSmallTable ? '' : getColumnMinWidth(col.name)\"\n                  :formatter=\"formatCell\"\n                  show-overflow-tooltip\n                  header-align=\"center\"\n                  align=\"center\"\n                />\n              </el-table>\n            </div>\n            <el-pagination\n              v-if=\"total > 0\"\n              background\n              :layout=\"paginationLayout\"\n              :current-page.sync=\"page\"\n              :page-size=\"pageSize\"\n              :page-sizes=\"[10, 20, 50, 100]\"\n              :pager-count=\"7\"\n              :total=\"total\"\n              @current-change=\"handlePageChange\"\n              @size-change=\"handleSizeChange\"\n              style=\"margin-top: 16px; text-align: right;\"\n            />\n            <div class=\"db-table-columns-info\" style=\"margin-top: 16px;\">\n              <b>字段信息：</b>\n              <!-- 字段信息区域：使用普通容器，无水平滚动提示 -->\n              <div class=\"columns-info-container\">\n                <el-table :data=\"columns\" border size=\"mini\" table-layout=\"fixed\" style=\"width: 100%; margin-top: 8px;\">\n                  <el-table-column prop=\"name\" label=\"字段名\" width=\"150\" />\n                  <el-table-column prop=\"type\" label=\"类型\" width=\"150\" />\n                  <el-table-column prop=\"nullable\" label=\"可空\" width=\"80\">\n                    <template slot-scope=\"scope\">\n                      <el-tag :type=\"scope.row.nullable ? 'info' : 'success'\">{{ scope.row.nullable ? '是' : '否' }}</el-tag>\n                    </template>\n                  </el-table-column>\n                  <el-table-column prop=\"comment\" label=\"注释\" min-width=\"250\">\n                    <template slot-scope=\"scope\">\n                      {{ getFieldComment(scope.row.name) }}\n                    </template>\n                  </el-table-column>\n                </el-table>\n              </div>\n            </div>\n          </div>\n          <div v-else style=\"text-align:center; color:#888; padding: 60px 0;\">请选择左侧表名</div>\n        </el-card>\n      </el-col>\n    </el-row>\n  </div>\n  <div v-else style=\"text-align:center; color:#888; padding: 60px 0;\">正在加载数据库表...</div>\n</template>\n\n<script>\nimport { getDbTables, getDbTableColumns, getDbTableRows } from '@/api/dbAdmin'\nimport { mapState } from 'vuex'\nimport { formatDate } from '@/utils/date'\n\nexport default {\n  name: 'DatabaseViewer',\n  data() {\n    return {\n      tables: [],\n      selectedTable: '',\n      columns: [],\n      rows: [],\n      total: 0,\n      page: 1,\n      pageSize: 20,\n      loading: false,\n      inited: false,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n      // 表字段注释对照表\n      fieldComments: {\n        // admin表：系统管理员\n        'admin.id': '管理员唯一ID',\n        'admin.username': '管理员登录账号',\n        'admin.password_hash': '加密后的登录密码',\n        'admin.name': '管理员姓名',\n        'admin.role': '管理员角色（如superadmin）',\n        'admin.is_active': '账号是否激活（1激活，0禁用）',\n        'admin.created_at': '账号创建时间',\n\n        // announcements表：公告表\n        'announcements.id': '公告ID',\n        'announcements.title': '公告标题',\n        'announcements.content': '公告内容',\n        'announcements.created_at': '公告创建（发布）时间',\n        'announcements.is_active': '公告是否启用',\n\n        // email_logs表：系统邮件发送日志\n        'email_logs.id': '日志唯一ID',\n        'email_logs.recipient': '收件人邮箱',\n        'email_logs.subject': '邮件主题',\n        'email_logs.template_key': '邮件模板标识',\n        'email_logs.event_type': '触发邮件的事件类型',\n        'email_logs.status': '发送状态（如success/failed）',\n        'email_logs.error_message': '发送失败时的错误信息',\n        'email_logs.reservation_code': '关联预约的编码',\n        'email_logs.reservation_number': '关联预约的编号',\n        'email_logs.created_at': '日志创建时间',\n        'email_logs.content_html': '邮件HTML内容',\n\n        // email_settings表：邮件服务器配置\n        'email_settings.id': '配置唯一ID',\n        'email_settings.smtp_server': 'SMTP服务器地址',\n        'email_settings.smtp_port': 'SMTP服务器端口',\n        'email_settings.sender_email': '发件人邮箱',\n        'email_settings.sender_name': '发件人显示名称',\n        'email_settings.smtp_username': 'SMTP登录用户名',\n        'email_settings.smtp_password': 'SMTP登录密码',\n        'email_settings.use_ssl': '是否使用SSL加密（1是，0否）',\n        'email_settings.enabled': '配置是否启用（1启用，0禁用）',\n        'email_settings.created_at': '配置创建时间',\n        'email_settings.updated_at': '配置更新时间',\n        'email_settings.cc_list': '抄送人列表，多个邮箱用逗号分隔',\n        'email_settings.bcc_list':'密送人列表，多个邮箱用逗号分隔',\n\n        // email_templates表：邮件模板\n        'email_templates.id': '模板唯一ID',\n        'email_templates.name': '模板名称',\n        'email_templates.template_key': '模板标识（代码）',\n        'email_templates.subject': '邮件主题模板',\n        'email_templates.content_html': 'HTML格式邮件内容',\n        'email_templates.content_text': '纯文本邮件内容',\n        'email_templates.variables': '可用变量说明',\n        'email_templates.language': '模板语言',\n        'email_templates.created_at': '模板创建时间',\n        'email_templates.updated_at': '模板更新时间',\n\n        // equipment表：可预约设备\n        'equipment.id': '设备唯一ID',\n        'equipment.name': '设备名称',\n        'equipment.category': '设备类别名称',\n        'equipment.model': '设备型号',\n        'equipment.location': '设备存放位置',\n        'equipment.status': '设备状态（如可用/维修/借出）',\n        'equipment.description': '设备详细描述',\n        'equipment.image_path': '设备图片路径',\n        'equipment.user_guide': '设备使用说明',\n        'equipment.created_at': '设备信息创建时间',\n        'equipment.updated_at': '设备信息更新时间',\n        'equipment.video_tutorial': '设备视频教程地址',\n        'equipment.category_id': '设备类别ID',\n        'equipment.allow_simultaneous': '是否允许同时多人预约（1允许，0不允许）',\n        'equipment.max_simultaneous': '最大同时预约人数',\n\n\n        // equipment_category表：设备类别\n        'equipment_category.id': '类别唯一ID',\n        'equipment_category.name': '类别名称',\n        'equipment_category.description': '类别描述',\n\n        // equipment_time_slots表：设备时间段\n        'equipment_time_slots.id': '时间段唯一ID',\n        'equipment_time_slots.equipment_id': '关联的设备ID',\n        'equipment_time_slots.start_datetime': '时间段开始时间',\n        'equipment_time_slots.end_datetime': '时间段结束时间',\n        'equipment_time_slots.current_count': '当前时间段内的预约数量',\n\n        // recurring_reservation表：周期性预约\n        'recurring_reservation.id': '周期预约唯一ID',\n        'recurring_reservation.equipment_id': '设备ID',\n        'recurring_reservation.pattern_type': '重复模式类型（如每周/每月）',\n        'recurring_reservation.days_of_week': '每周重复的星期（如1,3,5）',\n        'recurring_reservation.days_of_month': '每月重复的日期（如5,15,25）',\n        'recurring_reservation.start_date': '预约周期开始日期',\n        'recurring_reservation.end_date': '预约周期结束日期',\n        'recurring_reservation.start_time': '每天预约开始时间',\n        'recurring_reservation.end_time': '每天预约结束时间',\n        'recurring_reservation.user_name': '预约人姓名',\n        'recurring_reservation.user_department': '预约人部门',\n        'recurring_reservation.user_contact': '预约人联系方式',\n        'recurring_reservation.user_email': '预约人邮箱',\n        'recurring_reservation.purpose': '预约用途',\n        'recurring_reservation.status': '周期预约状态',\n        'recurring_reservation.created_at': '创建时间',\n        'recurring_reservation.reservation_code': '周期预约编码',\n        'recurring_reservation.conflicts': '冲突日期列表，逗号分隔的YYYY-MM-DD格式',\n        'recurring_reservation.total_planned': '计划创建的子预约总数',\n        'recurring_reservation.created_count': '成功创建的子预约数量',\n\n\n\n\n        // reservation表：单次预约\n        'reservation.id': '预约唯一ID',\n        'reservation.equipment_id': '设备ID',\n        'reservation.reservation_code': '预约编码',\n        'reservation.user_name': '预约人姓名',\n        'reservation.user_department': '预约人部门',\n        'reservation.user_contact': '预约人联系方式',\n        'reservation.user_email': '预约人邮箱',\n        'reservation.start_datetime': '预约开始时间',\n        'reservation.end_datetime': '预约结束时间',\n        'reservation.purpose': '预约用途',\n        'reservation.status': '预约状态',\n        'reservation.created_at': '预约创建时间',\n        'reservation.recurring_reservation_id': '关联的周期预约ID',\n        'reservation.is_exception': '是否为周期预约的特例',\n        'reservation.reservation_number': '预约编号',\n        'reservation.time_slot_id': '关联的设备时间段ID(整数)',\n\n        // system_settings表：系统设置\n        'system_settings.id': '设置唯一ID',\n        'system_settings.site_name': '系统站点名称',\n        'system_settings.maintenance_mode': '维护模式开关（1开启，0关闭）',\n        'system_settings.reservation_limit_per_day': '单日预约上限',\n        'system_settings.allow_equipment_conflict': '是否允许设备冲突预约（1允许，0不允许）',\n        'system_settings.advance_reservation_days': '可提前预约天数',\n        'system_settings.created_at': '设置创建时间',\n        'system_settings.updated_at': '设置更新时间'\n      }\n    }\n  },\n  computed: {\n    ...mapState({\n      user: state => state.user,\n    }),\n    isSuperAdmin() {\n      // 允许所有管理员访问数据库表查看功能\n      return this.user && (this.user.role === 'superadmin' || this.user.role === 'admin')\n    },\n    // 判断是否为小表格（列少的表格）\n    isSmallTable() {\n      const smallTables = ['admin', 'equipment_category', 'system_settings', 'announcements','equipment_time_slots','reservation_history'];\n      return !smallTables.includes(this.selectedTable);\n    },\n\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile\n        ? 'prev, next'\n        : 'total, sizes, prev, pager, next, jumper';\n    }\n  },\n  created() {\n    console.log(\"DatabaseViewer 组件 created\")\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize)\n  },\n\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize)\n  },\n  mounted() {\n    console.log(\"DatabaseViewer 组件 mounted, 调用 initIfNeeded\")\n    this.initIfNeeded()\n  },\n  methods: {\n    async initIfNeeded() {\n      console.log(\"initIfNeeded 被调用，inited=\", this.inited, \"isSuperAdmin=\", this.isSuperAdmin)\n      if (!this.inited && this.isSuperAdmin) {\n        this.inited = true\n        await this.fetchTables()\n      }\n    },\n    async fetchTables() {\n      console.log(\"开始获取数据库表名...\")\n      try {\n        console.log(\"调用 getDbTables()\")\n        const res = await getDbTables()\n        console.log(\"获取表名结果:\", res)\n        this.tables = res.data.tables || []\n        if (this.tables.length > 0) {\n          this.handleTableSelect(this.tables[0])\n        }\n      } catch (e) {\n        console.error(\"获取表名失败:\", e)\n        this.$message.error('获取表名失败: ' + (e.message || e))\n      }\n    },\n    async handleTableSelect(table) {\n      this.selectedTable = table\n      this.page = 1\n      await this.fetchTableColumns()\n      await this.fetchTableRows()\n    },\n    async fetchTableColumns() {\n      try {\n        const res = await getDbTableColumns(this.selectedTable)\n        // 兼容不同数据库字段名\n        this.columns = (res.data.columns || []).map(col => ({\n          name: col.name || col.column_name,\n          type: col.type || col.type_name,\n          nullable: col.nullable !== undefined ? col.nullable : col.nullable_,\n          default: col.default,\n          comment: col.comment || ''\n        }))\n      } catch (e) {\n        this.columns = []\n        this.$message.error('获取字段信息失败')\n      }\n    },\n    async fetchTableRows() {\n      this.loading = true\n      try {\n        const res = await getDbTableRows(this.selectedTable, {\n          skip: (this.page - 1) * this.pageSize,\n          limit: this.pageSize,\n        })\n        this.rows = res.data.rows || []\n\n        // 使用后端返回的总行数\n        if (res.data.total !== undefined) {\n          this.total = res.data.total;\n        } else {\n          // 兼容旧版API，如果后端没有返回总行数，则使用简单估算\n          if (this.rows.length < this.pageSize) {\n            // 当前页不满，可能是最后一页\n            this.total = (this.page - 1) * this.pageSize + this.rows.length;\n          } else {\n            // 当前页是满的，假设至少还有一页\n            this.total = this.page * this.pageSize + this.pageSize;\n          }\n        }\n      } catch (e) {\n        this.rows = []\n        this.$message.error('获取表数据失败')\n      } finally {\n        this.loading = false\n      }\n    },\n    handlePageChange(page) {\n      this.page = page\n      this.fetchTableRows()\n    },\n    handleSizeChange(size) {\n      this.pageSize = size\n      this.page = 1\n      this.fetchTableRows()\n    },\n    refreshTable() {\n      this.fetchTableColumns()\n      this.fetchTableRows()\n    },\n\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768\n    },\n    // 获取字段注释 - 简化版本\n    getFieldComment(fieldName) {\n      const key = `${this.selectedTable}.${fieldName}`;\n      return this.fieldComments[key] || '暂无注释';\n    },\n    // 获取最小列宽（用于自适应模式的表格）\n    getColumnMinWidth(columnName) {\n      const lowerColumnName = columnName.toLowerCase();\n\n      // 针对特定表格中的特定列进行特殊处理\n      if (this.selectedTable === 'admin') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'username') return 150;\n        if (lowerColumnName === 'password_hash') return 300;\n        if (lowerColumnName === 'name') return 150;\n        if (lowerColumnName === 'role') return 120;\n        if (lowerColumnName === 'is_active') return 100;\n        if (lowerColumnName === 'created_at') return 180;\n        return 150;\n      }\n\n      if (this.selectedTable === 'equipment_category') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'name') return 200;\n        if (lowerColumnName === 'description') return 300;\n        if (lowerColumnName === 'created_at') return 180;\n        return 200;\n      }\n\n      if (this.selectedTable === 'system_settings') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'key') return 200;\n        if (lowerColumnName === 'value') return 400;\n        if (lowerColumnName === 'description') return 300;\n        return 200;\n      }\n\n      // 添加针对announcements表的最小列宽设置\n      if (this.selectedTable === 'announcements') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'title') return 250;\n        if (lowerColumnName === 'content') return 450;\n        if (lowerColumnName === 'created_at') return 180;\n        if (lowerColumnName === 'is_active') return 120;\n        return 150;\n      }\n\n      // 默认最小列宽\n      if (lowerColumnName.includes('content_html') || lowerColumnName.includes('html')) {\n        return 300;\n      } else if (lowerColumnName === 'id') {\n        return 80;\n      } else if (lowerColumnName.includes('date') || lowerColumnName.includes('time')) {\n        return 160;\n      } else {\n        return 120;\n      }\n    },\n\n    // 标准列宽（用于固定模式的表格）\n    getColumnWidth(columnName) {\n      const lowerColumnName = columnName.toLowerCase();\n\n      // 特殊表格特殊处理\n      if (this.selectedTable === 'email_logs' && lowerColumnName === 'content_html') {\n        return 500;\n      }\n\n      // 专门为announcements表添加列宽处理\n      if (this.selectedTable === 'announcements') {\n        if (lowerColumnName === 'id') return 80;\n        if (lowerColumnName === 'title') return 200;\n        if (lowerColumnName === 'content') return 350;\n        if (lowerColumnName === 'created_at') return 180;\n        if (lowerColumnName === 'is_active') return 120;\n        return 150; // 其他可能的列\n      }\n\n      // 根据列名类型分配宽度\n      if (lowerColumnName.includes('content_html') || lowerColumnName.includes('html') || lowerColumnName.includes('content')) {\n        return 300;\n      } else if (lowerColumnName === 'id') {\n        return 80;\n      } else if (lowerColumnName.includes('date') || lowerColumnName.includes('time')) {\n        return 160;\n      } else if (lowerColumnName.includes('name')) {\n        return 120;\n      } else if (lowerColumnName.includes('code') || lowerColumnName.includes('number')) {\n        return 140;\n      } else if (lowerColumnName.includes('title')) {\n        return 120;\n      } else if (lowerColumnName.includes('email')) {\n        return 180;\n      } else if (lowerColumnName.includes('description') || lowerColumnName.includes('comment')) {\n        return 200;\n      } else if (lowerColumnName.includes('status')) {\n        return 120;\n      } else if (lowerColumnName.includes('password') || lowerColumnName.includes('hash')) {\n        return 250;\n      } else {\n        return 120;\n      }\n    },\n\n    // 格式化单元格内容\n    formatCell(row, column, cellValue) {\n      if (cellValue === null || cellValue === undefined) {\n        return '';\n      }\n\n      // 如果是HTML内容，只显示部分文本并添加提示\n      if (column.property.toLowerCase().includes('content_html') || column.property.toLowerCase().includes('html')) {\n        if (typeof cellValue === 'string' && cellValue.length > 100) {\n          return cellValue.substring(0, 100) + '...';\n        }\n      }\n\n      const tableName = this.selectedTable;\n      const columnName = column.property;\n\n      // 特殊处理recurring_reservation表的start_date和end_date，只显示日期部分\n      if (tableName === 'recurring_reservation' &&\n          (columnName === 'start_date' || columnName === 'end_date')) {\n        if (typeof cellValue === 'string' && cellValue.length >= 10) {\n          return cellValue.substring(0, 10); // 只保留YYYY-MM-DD部分\n        }\n      }\n\n      // 特殊处理recurring_reservation表的start_time和end_time，移除微秒\n      if (tableName === 'recurring_reservation' &&\n          (columnName === 'start_time' || columnName === 'end_time')) {\n        if (typeof cellValue === 'string') {\n          // 如果包含空格（例如\"09:00:00 000000\"）\n          if (cellValue.includes(' ')) {\n            return cellValue.split(' ')[0]; // 只保留时间部分\n          }\n          // 如果只有时间部分\n          return cellValue;\n        }\n      }\n\n      // 对日期时间格式化，不进行时区转换\n      // 排除明确是ID字段的列，比如time_slot_id\n      if (column.property !== 'time_slot_id' && column.property !== 'timeslot_number' &&\n          (column.property.toLowerCase().includes('date') || column.property.toLowerCase().includes('time')) &&\n          !isNaN(Date.parse(cellValue))) {\n        try {\n          // 以下字段已经是北京时间，不需要再转换\n          const skipTimeZoneConversion = [\n            'updated_at', 'created_at', 'start_datetime', 'end_datetime'\n          ];\n\n          // 不进行时区转换，直接显示数据库中的原始时间\n          const toBeijingTime = false;\n\n          // 根据字段类型选择显示格式\n          let format = 'YYYY-MM-DD HH:mm:ss';\n\n          // 只显示日期部分\n          if (columnName === 'start_date' || columnName === 'end_date') {\n            format = 'YYYY-MM-DD';\n          }\n\n          // 只显示时间部分\n          if (columnName === 'start_time' || columnName === 'end_time') {\n            format = 'HH:mm:ss';\n          }\n\n          return formatDate(cellValue, format, toBeijingTime);\n        } catch (e) {\n          console.error('日期格式化错误:', e);\n          return cellValue;\n        }\n      }\n\n      return cellValue;\n    }\n  },\n}\n</script>\n\n<style>\n.db-viewer {\n  padding: 24px;\n}\n.db-tables-list {\n  border-right: 1px solid #eee;\n  min-height: 70vh;\n}\n.db-table-content {\n  padding-left: 24px;\n}\n.db-table-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.table-container {\n  position: relative;\n  margin-bottom: 16px;\n}\n\n/* 修改这里：优化水平滚动容器样式 */\n.horizontal-scroll-container {\n  width: 100%;\n  overflow-x: auto !important;\n  overflow-y: hidden;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\n  position: relative;\n  margin-bottom: 5px;\n  /* 确保滚动条始终可见 */\n  scrollbar-width: auto;\n  scrollbar-color: #909399 #f5f7fa;\n}\n\n/* 只为数据表格容器添加滚动提示文字 */\n.data-table-container::after {\n  content: '← 左右滑动可查看更多数据 →';\n  position: absolute;\n  bottom: 5px;\n  right: 10px;\n  font-size: 12px;\n  color: #909399;\n  background: rgba(255,255,255,0.8);\n  padding: 2px 8px;\n  border-radius: 4px;\n  opacity: 0.7;\n  z-index: 1;\n}\n\n/* 为字段信息容器移除滚动提示 */\n.columns-info-container {\n  width: 100%;\n  overflow: auto;\n  border: 1px solid #ebeef5;\n  border-radius: 4px;\n}\n\n/* 修改滚动条样式使其更明显 - 灰色滚动条 */\n::-webkit-scrollbar {\n  width: 12px !important;\n  height: 12px !important;\n  background-color: #f5f7fa !important;\n}\n::-webkit-scrollbar-thumb {\n  background-color: #909399 !important;\n  border-radius: 6px !important;\n  border: 2px solid #f5f7fa !important;\n}\n::-webkit-scrollbar-thumb:hover {\n  background-color: #606266 !important;\n}\n::-webkit-scrollbar-corner {\n  background-color: #f5f7fa !important;\n}\n\n/* 强制表格显示滚动条 */\n.el-table__body-wrapper {\n  overflow: auto !important;\n}\n\n/* 表格样式优化 */\n.el-table__header th {\n  background-color: #f5f7fa !important;\n  color: #606266 !important;\n  font-weight: bold !important;\n  padding: 8px 0 !important;\n  white-space: nowrap;\n}\n\n/* 确保单元格内容不换行，允许水平滚动 */\n.el-table .cell {\n  white-space: nowrap !important;\n}\n\n/* 修复行高问题 */\n.el-table__row {\n  height: auto !important;\n}\n\n/* 修改tooltip样式 */\n.el-tooltip__popper {\n  max-width: 400px !important;\n  white-space: pre-wrap !important;\n  word-break: break-word !important;\n}\n\n/* 改进分页器外观 */\n.el-pagination {\n  padding: 16px 6px !important;\n  background-color: #f9f9f9 !important;\n  border-radius: 4px !important;\n}\n\n/* 字段信息表样式 */\n.db-table-columns-info .el-table {\n  margin-top: 10px !important;\n}\n.db-table-columns-info .el-table__header th {\n  background-color: #f5f7fa !important;\n  color: #606266 !important;\n  padding: 8px !important;\n}\n.db-table-columns-info .el-table__cell {\n  padding: 8px !important;\n}\n\n/* 移动端提示样式 */\n.mobile-notice {\n  padding: 20px;\n}\n\n.mobile-notice h3 {\n  margin: 0 0 10px 0;\n  color: #303133;\n}\n</style>"], "mappings": "AA4GA,SAAAA,WAAA,EAAAC,iBAAA,EAAAC,cAAA;AACA,SAAAC,QAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,MAAA;MACAC,aAAA;MACAC,OAAA;MACAC,IAAA;MACAC,KAAA;MACAC,IAAA;MACAC,QAAA;MACAC,OAAA;MACAC,MAAA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;MACA;MACAC,aAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAGA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAKA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC,QAAA;IACA,GAAAjB,QAAA;MACAkB,IAAA,EAAAC,KAAA,IAAAA,KAAA,CAAAD;IACA;IACAE,aAAA;MACA;MACA,YAAAF,IAAA,UAAAA,IAAA,CAAAG,IAAA,0BAAAH,IAAA,CAAAG,IAAA;IACA;IACA;IACAC,aAAA;MACA,MAAAC,WAAA;MACA,QAAAA,WAAA,CAAAC,QAAA,MAAAnB,aAAA;IACA;IAEA;IACAoB,iBAAA;MACA,YAAAZ,QAAA,GACA,eACA;IACA;EACA;EACAa,QAAA;IACAC,OAAA,CAAAC,GAAA;IACA;IACAd,MAAA,CAAAe,gBAAA,gBAAAC,YAAA;EACA;EAEAC,cAAA;IACA;IACAjB,MAAA,CAAAkB,mBAAA,gBAAAF,YAAA;EACA;EACAG,QAAA;IACAN,OAAA,CAAAC,GAAA;IACA,KAAAM,YAAA;EACA;EACAC,OAAA;IACA,MAAAD,aAAA;MACAP,OAAA,CAAAC,GAAA,kCAAAhB,MAAA,wBAAAQ,YAAA;MACA,UAAAR,MAAA,SAAAQ,YAAA;QACA,KAAAR,MAAA;QACA,WAAAwB,WAAA;MACA;IACA;IACA,MAAAA,YAAA;MACAT,OAAA,CAAAC,GAAA;MACA;QACAD,OAAA,CAAAC,GAAA;QACA,MAAAS,GAAA,SAAAxC,WAAA;QACA8B,OAAA,CAAAC,GAAA,YAAAS,GAAA;QACA,KAAAjC,MAAA,GAAAiC,GAAA,CAAAlC,IAAA,CAAAC,MAAA;QACA,SAAAA,MAAA,CAAAkC,MAAA;UACA,KAAAC,iBAAA,MAAAnC,MAAA;QACA;MACA,SAAAoC,CAAA;QACAb,OAAA,CAAAc,KAAA,YAAAD,CAAA;QACA,KAAAE,QAAA,CAAAD,KAAA,eAAAD,CAAA,CAAAG,OAAA,IAAAH,CAAA;MACA;IACA;IACA,MAAAD,kBAAAK,KAAA;MACA,KAAAvC,aAAA,GAAAuC,KAAA;MACA,KAAAnC,IAAA;MACA,WAAAoC,iBAAA;MACA,WAAAC,cAAA;IACA;IACA,MAAAD,kBAAA;MACA;QACA,MAAAR,GAAA,SAAAvC,iBAAA,MAAAO,aAAA;QACA;QACA,KAAAC,OAAA,IAAA+B,GAAA,CAAAlC,IAAA,CAAAG,OAAA,QAAAyC,GAAA,CAAAC,GAAA;UACA9C,IAAA,EAAA8C,GAAA,CAAA9C,IAAA,IAAA8C,GAAA,CAAAC,WAAA;UACAC,IAAA,EAAAF,GAAA,CAAAE,IAAA,IAAAF,GAAA,CAAAG,SAAA;UACAC,QAAA,EAAAJ,GAAA,CAAAI,QAAA,KAAAC,SAAA,GAAAL,GAAA,CAAAI,QAAA,GAAAJ,GAAA,CAAAM,SAAA;UACAC,OAAA,EAAAP,GAAA,CAAAO,OAAA;UACAC,OAAA,EAAAR,GAAA,CAAAQ,OAAA;QACA;MACA,SAAAhB,CAAA;QACA,KAAAlC,OAAA;QACA,KAAAoC,QAAA,CAAAD,KAAA;MACA;IACA;IACA,MAAAK,eAAA;MACA,KAAAnC,OAAA;MACA;QACA,MAAA0B,GAAA,SAAAtC,cAAA,MAAAM,aAAA;UACAoD,IAAA,QAAAhD,IAAA,aAAAC,QAAA;UACAgD,KAAA,OAAAhD;QACA;QACA,KAAAH,IAAA,GAAA8B,GAAA,CAAAlC,IAAA,CAAAI,IAAA;;QAEA;QACA,IAAA8B,GAAA,CAAAlC,IAAA,CAAAK,KAAA,KAAA6C,SAAA;UACA,KAAA7C,KAAA,GAAA6B,GAAA,CAAAlC,IAAA,CAAAK,KAAA;QACA;UACA;UACA,SAAAD,IAAA,CAAA+B,MAAA,QAAA5B,QAAA;YACA;YACA,KAAAF,KAAA,SAAAC,IAAA,aAAAC,QAAA,QAAAH,IAAA,CAAA+B,MAAA;UACA;YACA;YACA,KAAA9B,KAAA,QAAAC,IAAA,QAAAC,QAAA,QAAAA,QAAA;UACA;QACA;MACA,SAAA8B,CAAA;QACA,KAAAjC,IAAA;QACA,KAAAmC,QAAA,CAAAD,KAAA;MACA;QACA,KAAA9B,OAAA;MACA;IACA;IACAgD,iBAAAlD,IAAA;MACA,KAAAA,IAAA,GAAAA,IAAA;MACA,KAAAqC,cAAA;IACA;IACAc,iBAAAC,IAAA;MACA,KAAAnD,QAAA,GAAAmD,IAAA;MACA,KAAApD,IAAA;MACA,KAAAqC,cAAA;IACA;IACAgB,aAAA;MACA,KAAAjB,iBAAA;MACA,KAAAC,cAAA;IACA;IAEA;IACAhB,aAAA;MACA,KAAAjB,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IACA;IACAgD,gBAAAC,SAAA;MACA,MAAAC,GAAA,WAAA5D,aAAA,IAAA2D,SAAA;MACA,YAAAhD,aAAA,CAAAiD,GAAA;IACA;IACA;IACAC,kBAAAC,UAAA;MACA,MAAAC,eAAA,GAAAD,UAAA,CAAAE,WAAA;;MAEA;MACA,SAAAhE,aAAA;QACA,IAAA+D,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA;MACA;MAEA,SAAA/D,aAAA;QACA,IAAA+D,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA;MACA;MAEA,SAAA/D,aAAA;QACA,IAAA+D,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA;MACA;;MAEA;MACA,SAAA/D,aAAA;QACA,IAAA+D,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA;MACA;;MAEA;MACA,IAAAA,eAAA,CAAA5C,QAAA,oBAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA;QACA;MACA,WAAAA,eAAA,CAAA5C,QAAA,YAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACA8C,eAAAH,UAAA;MACA,MAAAC,eAAA,GAAAD,UAAA,CAAAE,WAAA;;MAEA;MACA,SAAAhE,aAAA,qBAAA+D,eAAA;QACA;MACA;;MAEA;MACA,SAAA/D,aAAA;QACA,IAAA+D,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA,IAAAA,eAAA;QACA;MACA;;MAEA;MACA,IAAAA,eAAA,CAAA5C,QAAA,oBAAA4C,eAAA,CAAA5C,QAAA,YAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA;QACA;MACA,WAAAA,eAAA,CAAA5C,QAAA,YAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA,YAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA,mBAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA,WAAA4C,eAAA,CAAA5C,QAAA,gBAAA4C,eAAA,CAAA5C,QAAA;QACA;MACA;QACA;MACA;IACA;IAEA;IACA+C,WAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,IAAAA,SAAA,aAAAA,SAAA,KAAArB,SAAA;QACA;MACA;;MAEA;MACA,IAAAoB,MAAA,CAAAE,QAAA,CAAAN,WAAA,GAAA7C,QAAA,oBAAAiD,MAAA,CAAAE,QAAA,CAAAN,WAAA,GAAA7C,QAAA;QACA,WAAAkD,SAAA,iBAAAA,SAAA,CAAApC,MAAA;UACA,OAAAoC,SAAA,CAAAE,SAAA;QACA;MACA;MAEA,MAAAC,SAAA,QAAAxE,aAAA;MACA,MAAA8D,UAAA,GAAAM,MAAA,CAAAE,QAAA;;MAEA;MACA,IAAAE,SAAA,iCACAV,UAAA,qBAAAA,UAAA;QACA,WAAAO,SAAA,iBAAAA,SAAA,CAAApC,MAAA;UACA,OAAAoC,SAAA,CAAAE,SAAA;QACA;MACA;;MAEA;MACA,IAAAC,SAAA,iCACAV,UAAA,qBAAAA,UAAA;QACA,WAAAO,SAAA;UACA;UACA,IAAAA,SAAA,CAAAlD,QAAA;YACA,OAAAkD,SAAA,CAAAI,KAAA;UACA;UACA;UACA,OAAAJ,SAAA;QACA;MACA;;MAEA;MACA;MACA,IAAAD,MAAA,CAAAE,QAAA,uBAAAF,MAAA,CAAAE,QAAA,2BACAF,MAAA,CAAAE,QAAA,CAAAN,WAAA,GAAA7C,QAAA,YAAAiD,MAAA,CAAAE,QAAA,CAAAN,WAAA,GAAA7C,QAAA,aACA,CAAAuD,KAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAP,SAAA;QACA;UACA;UACA,MAAAQ,sBAAA,IACA,6DACA;;UAEA;UACA,MAAAC,aAAA;;UAEA;UACA,IAAAC,MAAA;;UAEA;UACA,IAAAjB,UAAA,qBAAAA,UAAA;YACAiB,MAAA;UACA;;UAEA;UACA,IAAAjB,UAAA,qBAAAA,UAAA;YACAiB,MAAA;UACA;UAEA,OAAAnF,UAAA,CAAAyE,SAAA,EAAAU,MAAA,EAAAD,aAAA;QACA,SAAA3C,CAAA;UACAb,OAAA,CAAAc,KAAA,aAAAD,CAAA;UACA,OAAAkC,SAAA;QACA;MACA;MAEA,OAAAA,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}