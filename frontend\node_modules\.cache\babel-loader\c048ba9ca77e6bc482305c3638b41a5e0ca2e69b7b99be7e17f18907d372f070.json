{"ast": null, "code": "import { reservationApi } from '@/api';\nimport { isReservationExpired } from '@/utils/date';\nexport default {\n  name: 'AdminReservation',\n  data() {\n    return {\n      loading: false,\n      reservations: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 10,\n      filter: {\n        code: '',\n        userName: '',\n        status: '',\n        dateRange: []\n      },\n      // 添加一个保存页面状态的变量\n      savedState: null,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n      // 导出相关\n      exportDialogVisible: false,\n      exportLoading: false,\n      exportForm: {\n        format: 'csv',\n        scope: 'current',\n        selectedFields: []\n      },\n      selectAllFields: true,\n      availableFields: [{\n        key: 'id',\n        label: 'ID'\n      }, {\n        key: 'reservation_number',\n        label: '预约编号'\n      }, {\n        key: 'reservation_code',\n        label: '预约码'\n      }, {\n        key: 'equipment_name',\n        label: '设备名称'\n      }, {\n        key: 'equipment_category',\n        label: '设备类别'\n      }, {\n        key: 'equipment_location',\n        label: '设备位置'\n      }, {\n        key: 'user_name',\n        label: '预约人姓名'\n      }, {\n        key: 'user_department',\n        label: '预约人部门'\n      }, {\n        key: 'user_contact',\n        label: '联系方式'\n      }, {\n        key: 'user_email',\n        label: '邮箱地址'\n      }, {\n        key: 'start_datetime',\n        label: '开始时间'\n      }, {\n        key: 'end_datetime',\n        label: '结束时间'\n      }, {\n        key: 'purpose',\n        label: '使用目的'\n      }, {\n        key: 'status',\n        label: '预约状态'\n      }, {\n        key: 'created_at',\n        label: '创建时间'\n      }]\n    };\n  },\n  computed: {\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile ? 'prev, next' : 'prev, pager, next';\n    }\n  },\n  created() {\n    // 检查是否有保存的状态并恢复它\n    this.restoreState();\n\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  // 添加activated钩子函数，在组件被激活时调用（如从预定详情页面返回）\n  activated() {\n    // 检查是否需要强制刷新\n    const forceRefresh = localStorage.getItem('force_refresh_reservation_list');\n    if (forceRefresh === 'true') {\n      console.log('检测到强制刷新标记，重新获取数据');\n      localStorage.removeItem('force_refresh_reservation_list');\n      this.fetchData();\n      return;\n    }\n\n    // 当从其他页面返回时，尝试恢复状态\n    this.restoreState();\n    // 检查预约状态更新\n    this.checkReservationUpdates();\n  },\n  // 添加deactivated钩子函数，在组件被停用时调用（如进入预定详情页面）\n  deactivated() {\n    // 保存当前页面状态\n    this.saveState();\n  },\n  // 添加beforeRouteLeave导航守卫，在离开组件时调用\n  beforeRouteLeave(to, from, next) {\n    // 如果是跳转到预定详情页面，保存状态\n    if (to.path.includes('/admin/reservation/') && to.path !== '/admin/reservation') {\n      this.saveState();\n    }\n    next();\n  },\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    async fetchData() {\n      this.loading = true;\n      console.log('Fetching data with filter:', this.filter);\n      console.log('Current page:', this.currentPage);\n      try {\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime();\n        console.log('添加时间戳参数:', timestamp);\n        const params = {\n          skip: (this.currentPage - 1) * this.pageSize,\n          // 将页码转换为skip参数\n          limit: this.pageSize,\n          reservation_code: this.filter.code || undefined,\n          user_name: this.filter.userName || undefined,\n          _t: timestamp,\n          // 添加时间戳，防止缓存\n          sort_by: 'id',\n          // 按ID排序\n          sort_order: 'desc' // 降序排序\n        };\n\n        // 添加日期范围过滤\n        if (this.filter.dateRange && this.filter.dateRange.length === 2) {\n          params.from_date = this.filter.dateRange[0];\n          params.to_date = this.filter.dateRange[1];\n        }\n\n        // 处理不同的状态筛选\n        if (this.filter.status) {\n          console.log('Filtering by status:', this.filter.status);\n\n          // 直接使用选择的状态值，因为后端现在支持所有状态\n          params.status = this.filter.status;\n          console.log(`Setting status parameter to \"${this.filter.status}\"`);\n        }\n        console.log('Fetching reservations with params:', params);\n        const response = await reservationApi.getReservations(params);\n        console.log('API Response:', response);\n        let reservations = response.data.items || [];\n        console.log('Received reservations:', reservations);\n\n        // 不再需要在前端进行筛选，因为后端已经返回了正确的状态\n        // 只记录日志，帮助调试\n        if (this.filter.status) {\n          console.log(`获取到状态为 ${this.filter.status} 的预约数量: ${reservations.length}`);\n\n          // 记录每个预约的状态，帮助调试\n          reservations.forEach(reservation => {\n            console.log(`预约ID=${reservation.id}, 状态=${reservation.status}, 开始时间=${reservation.start_datetime}, 结束时间=${reservation.end_datetime}`);\n          });\n        }\n        console.log('Filtered reservations:', reservations);\n        this.reservations = reservations;\n\n        // 如果是特殊状态，总数需要重新计算\n        if (this.filter.status === 'in_use' || this.filter.status === 'expired' || this.filter.status === 'confirmed' || this.filter.status === 'cancelled') {\n          // 对于特殊状态，我们需要获取所有页的数据来计算总数\n          // 这里我们先使用当前页的数据计算一个临时总数\n          this.total = reservations.length;\n          console.log(`Temporary total based on current page: ${this.total}`);\n\n          // 无论当前页是否有数据，都获取所有数据来计算真实总数\n          this.fetchTotalForSpecialStatus();\n        } else {\n          this.total = response.data.total;\n          console.log(`Total from API response: ${this.total}`);\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservations:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDateTime(_row, _column, cellValue) {\n      if (!cellValue) return '';\n      const date = new Date(cellValue);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n    getStatusType(reservation) {\n      // 直接根据后端返回的状态返回对应的类型\n      switch (reservation.status) {\n        case 'cancelled':\n          return 'danger';\n        // 已取消 - 红色\n        case 'expired':\n          return 'warning';\n        // 已过期 - 橙色\n        case 'in_use':\n          return 'primary';\n        // 使用中 - 蓝色\n        case 'confirmed':\n          return 'success';\n        // 已确认 - 绿色\n        default:\n          return 'info';\n        // 其他状态 - 灰色\n      }\n    },\n    getStatusText(reservation) {\n      // 直接根据后端返回的状态返回对应的文本\n      switch (reservation.status) {\n        case 'cancelled':\n          return this.$t('reservation.statusCancelled');\n        // 已取消\n        case 'expired':\n          return this.$t('reservation.statusExpired');\n        // 已过期\n        case 'in_use':\n          return this.$t('reservation.statusInUse');\n        // 使用中\n        case 'confirmed':\n          return this.$t('reservation.statusConfirmed');\n        // 已确认\n        default:\n          return reservation.status;\n        // 其他状态直接显示\n      }\n    },\n    handleFilterChange() {\n      this.currentPage = 1;\n      this.fetchData();\n    },\n    resetFilter() {\n      this.filter = {\n        code: '',\n        userName: '',\n        status: '',\n        dateRange: []\n      };\n      this.handleFilterChange();\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchData();\n    },\n    viewReservation(reservation) {\n      // 计算当前预约的实际状态文本和类型\n      const statusText = this.getStatusText(reservation);\n      const statusType = this.getStatusType(reservation);\n      console.log('计算的状态信息:', {\n        statusText,\n        statusType,\n        dbStatus: reservation.status,\n        startTime: reservation.start_datetime,\n        endTime: reservation.end_datetime,\n        reservationNumber: reservation.reservation_number\n      });\n\n      // 构建URL，添加预约码、时间参数、预约序号和计算好的状态信息\n      // 使用预约序号作为路径参数，而不是预约码\n      const url = {\n        path: `/admin/reservation/${reservation.reservation_code}`,\n        query: {\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime,\n          displayStatus: statusText,\n          displayStatusType: statusType,\n          reservationNumber: reservation.reservation_number // 添加预约序号参数\n        }\n      };\n\n      // 每次查看预约时，都重新设置一个标记，表示需要显示预约序号通知\n      localStorage.setItem('show_reservation_number_notification', 'true');\n\n      // 清除之前的预约序号，确保每次都使用新的预约序号\n      localStorage.removeItem('current_reservation_number');\n\n      // 将预约序号保存到localStorage，以便在页面刷新后仍然可以使用\n      if (reservation.reservation_number) {\n        localStorage.setItem('current_reservation_number', reservation.reservation_number);\n        console.log('保存预约序号到localStorage:', reservation.reservation_number);\n\n        // 强制使用预约序号查询，而不是预约码\n        localStorage.setItem('force_use_reservation_number', 'true');\n      }\n      this.$router.push(url);\n    },\n    // 获取特殊状态的总记录数并更新当前页面的预约列表\n    async fetchTotalForSpecialStatus() {\n      try {\n        console.log('Fetching total for special status:', this.filter.status);\n        console.log('Current page before fetchTotal:', this.currentPage);\n\n        // 保存当前页码，以便后续恢复\n        const savedCurrentPage = this.currentPage;\n\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime();\n        console.log('添加时间戳参数:', timestamp);\n\n        // 构建查询参数，不包含分页参数\n        const params = {\n          // 不设置limit，获取所有记录\n          limit: 1000,\n          // 设置一个较大的值以获取尽可能多的记录\n          skip: 0,\n          reservation_code: this.filter.code || undefined,\n          // 使用reservation_code而不是code\n          user_name: this.filter.userName || undefined,\n          _t: timestamp,\n          // 添加时间戳，防止缓存\n          sort_by: 'id',\n          // 按ID排序\n          sort_order: 'desc' // 降序排序\n        };\n\n        // 添加日期范围过滤\n        if (this.filter.dateRange && this.filter.dateRange.length === 2) {\n          params.from_date = this.filter.dateRange[0];\n          params.to_date = this.filter.dateRange[1];\n        }\n\n        // 直接获取指定状态的预约\n        const statusParams = {\n          ...params,\n          status: this.filter.status\n        };\n        console.log(`直接获取状态为 ${this.filter.status} 的预约`);\n        const response = await reservationApi.getReservations(statusParams);\n\n        // 使用后端返回的结果\n        let allReservations = response.data.items || [];\n        console.log(`Total reservations before filtering: ${allReservations.length}`);\n        const now = new Date();\n        console.log(`当前日期: ${now}`);\n\n        // 不再需要在前端进行筛选，因为后端已经返回了正确的状态\n        // 只记录日志，帮助调试\n        console.log(`获取到状态为 ${this.filter.status} 的预约数量: ${allReservations.length}`);\n\n        // 记录每个预约的状态，帮助调试\n        allReservations.forEach(reservation => {\n          console.log(`预约ID=${reservation.id}, 状态=${reservation.status}, 开始时间=${reservation.start_datetime}, 结束时间=${reservation.end_datetime}`);\n        });\n\n        // 更新总数\n        this.total = allReservations.length;\n        console.log(`Updated total to: ${this.total}`);\n\n        // 对筛选后的结果进行排序\n        if (this.filter.status === 'expired') {\n          // 对于已过期，按结束时间倒序排列\n          allReservations.sort((a, b) => new Date(b.end_datetime) - new Date(a.end_datetime));\n        } else if (this.filter.status === 'in_use') {\n          // 对于使用中，按开始时间倒序排列\n          allReservations.sort((a, b) => new Date(b.start_datetime) - new Date(a.start_datetime));\n        } else if (this.filter.status === 'confirmed') {\n          // 对于已确认，按开始时间升序排列\n          allReservations.sort((a, b) => new Date(a.start_datetime) - new Date(b.start_datetime));\n        } else if (this.filter.status === 'cancelled') {\n          // 对于已取消，按结束时间倒序排列\n          allReservations.sort((a, b) => new Date(b.end_datetime) - new Date(a.end_datetime));\n        }\n\n        // 计算当前页应该显示的预约\n        const maxPage = Math.ceil(allReservations.length / this.pageSize) || 1;\n\n        // 确保页码不超过最大页数\n        const targetPage = Math.min(savedCurrentPage, maxPage);\n        console.log(`计算页数: 总记录数=${allReservations.length}, 每页记录数=${this.pageSize}, 最大页数=${maxPage}, 目标页码=${targetPage}`);\n        const startIndex = (targetPage - 1) * this.pageSize;\n        const endIndex = Math.min(startIndex + this.pageSize, allReservations.length);\n        const currentPageReservations = allReservations.slice(startIndex, endIndex);\n        console.log(`当前页数据范围: 开始索引=${startIndex}, 结束索引=${endIndex}, 当前页记录数=${currentPageReservations.length}`);\n\n        // 更新当前页面的预约列表\n        if (currentPageReservations.length > 0) {\n          // 先更新数据\n          this.reservations = currentPageReservations;\n          // 然后更新页码，避免触发不必要的重新获取数据\n          if (this.currentPage !== targetPage) {\n            console.log(`更新页码: 从 ${this.currentPage} 到 ${targetPage}`);\n            this.$nextTick(() => {\n              this.currentPage = targetPage;\n            });\n          }\n          console.log(`更新当前页面的预约列表: ${this.reservations.length} 条记录`);\n        } else if (allReservations.length > 0) {\n          // 如果当前页没有数据但总数据不为空，自动回到第一页\n          console.log(`当前页没有数据，回到第一页`);\n          this.reservations = allReservations.slice(0, this.pageSize);\n          if (this.currentPage !== 1) {\n            this.$nextTick(() => {\n              this.currentPage = 1;\n            });\n          }\n        } else {\n          // 如果没有找到任何预约\n          this.reservations = [];\n          console.log('没有找到符合条件的预约');\n        }\n        console.log('Current page after fetchTotal:', this.currentPage);\n      } catch (error) {\n        console.error('Failed to fetch total for special status:', error);\n      }\n    },\n    // 保存当前页面状态\n    saveState() {\n      this.savedState = {\n        filter: {\n          ...this.filter\n        },\n        currentPage: this.currentPage\n      };\n      console.log('Saved state:', this.savedState);\n    },\n    // 恢复保存的页面状态\n    restoreState() {\n      if (this.savedState) {\n        this.filter = {\n          ...this.savedState.filter\n        };\n        this.currentPage = this.savedState.currentPage;\n        console.log('Restored state:', this.savedState);\n        this.fetchData();\n      } else {\n        this.fetchData();\n      }\n    },\n    // 打开预约详情\n    openReservationDetail(reservation) {\n      console.log('打开预约详情:', reservation);\n\n      // 计算当前状态\n      const statusText = this.getStatusText(reservation);\n      const statusType = this.getStatusType(reservation);\n      const dbStatus = reservation.status || 'confirmed';\n      const startTime = reservation.start_datetime;\n      const endTime = reservation.end_datetime;\n      console.log('计算的状态信息:', {\n        statusText,\n        statusType,\n        dbStatus,\n        startTime,\n        endTime\n      });\n\n      // 将状态保存到localStorage，以便详情页面使用\n      const stateKey = `reservation_status_${reservation.reservation_code}`;\n      const state = {\n        statusText,\n        statusType,\n        dbStatus,\n        timestamp: new Date().getTime()\n      };\n      console.log('Saved state:', state);\n      localStorage.setItem(stateKey, JSON.stringify(state));\n\n      // 导航到详情页面，并传递状态和时间参数\n      this.$router.push({\n        name: 'AdminReservationDetail',\n        params: {\n          code: reservation.reservation_code\n        },\n        query: {\n          displayStatus: statusText,\n          displayStatusType: statusType,\n          startTime: startTime,\n          endTime: endTime\n        }\n      });\n    },\n    // 在激活（从其他页面返回）时，检查预约状态是否需要更新\n    async checkReservationUpdates() {\n      // 如果当前显示的是预约列表，则检查是否需要刷新\n      if (this.reservations.length > 0) {\n        // 检查localStorage中是否有任何预约状态发生了变化\n        for (let i = 0; i < this.reservations.length; i++) {\n          const reservation = this.reservations[i];\n          const stateKey = `reservation_status_${reservation.reservation_code}`;\n          const savedStateStr = localStorage.getItem(stateKey);\n          if (savedStateStr) {\n            try {\n              const savedState = JSON.parse(savedStateStr);\n\n              // 检查保存的状态是否还是新鲜的（5分钟内）\n              const now = new Date().getTime();\n              const fiveMinutes = 5 * 60 * 1000;\n              if (now - savedState.timestamp <= fiveMinutes) {\n                console.log(`检测到预约 ${reservation.reservation_code} 的状态可能已更改，保存的状态:`, savedState);\n\n                // 检查是否有强制状态更新，特别是已取消状态\n                if (savedState.forcedStatus === 'cancelled' || savedState.statusText === this.$t('reservation.cancelled') && savedState.statusType === 'danger') {\n                  console.log(`预约 ${reservation.reservation_code} 已被标记为已取消，将在界面上更新`);\n\n                  // 更新当前列表中的预约状态\n                  this.reservations[i].status = 'cancelled';\n\n                  // 强制更新UI\n                  this.$forceUpdate();\n                }\n              } else {\n                // 如果状态过期，则移除它\n                console.log(`预约 ${reservation.reservation_code} 的保存状态已过期，移除`);\n                localStorage.removeItem(stateKey);\n              }\n            } catch (e) {\n              console.error('解析保存的状态时出错:', e);\n            }\n          }\n        }\n      }\n    },\n    // 显示导出对话框\n    showExportDialog() {\n      this.exportDialogVisible = true;\n      // 默认选择所有字段\n      this.exportForm.selectedFields = this.availableFields.map(field => field.key);\n      this.selectAllFields = true;\n    },\n    // 处理全选字段\n    handleSelectAllFields(value) {\n      if (value) {\n        this.exportForm.selectedFields = this.availableFields.map(field => field.key);\n      } else {\n        this.exportForm.selectedFields = [];\n      }\n    },\n    // 处理导出\n    async handleExport() {\n      if (this.exportForm.selectedFields.length === 0) {\n        this.$message.warning('请至少选择一个导出字段');\n        return;\n      }\n      this.exportLoading = true;\n      try {\n        // 构建导出请求数据\n        const exportData = {\n          export_format: this.exportForm.format,\n          export_scope: this.exportForm.scope,\n          selected_fields: this.exportForm.selectedFields\n        };\n\n        // 如果是导出全部筛选结果，添加筛选条件\n        if (this.exportForm.scope === 'all') {\n          exportData.reservation_code = this.filter.code || undefined;\n          exportData.user_name = this.filter.userName || undefined;\n          exportData.status = this.filter.status || undefined;\n          if (this.filter.dateRange && this.filter.dateRange.length === 2) {\n            exportData.from_date = this.filter.dateRange[0];\n            exportData.to_date = this.filter.dateRange[1];\n          }\n        } else {\n          // 导出当前页面数据\n          exportData.current_data = this.reservations.map(reservation => ({\n            id: reservation.id,\n            reservation_number: reservation.reservation_number,\n            reservation_code: reservation.reservation_code,\n            equipment_name: reservation.equipment_name,\n            equipment_category: reservation.equipment_category,\n            equipment_location: reservation.equipment_location,\n            user_name: reservation.user_name,\n            user_department: reservation.user_department,\n            user_contact: reservation.user_contact,\n            user_email: reservation.user_email,\n            start_datetime: reservation.start_datetime,\n            end_datetime: reservation.end_datetime,\n            purpose: reservation.purpose,\n            status: reservation.status,\n            created_at: reservation.created_at\n          }));\n        }\n        console.log('导出请求数据:', exportData);\n\n        // 调用导出API\n        const response = await reservationApi.exportReservations(exportData);\n\n        // 创建下载链接\n        const blob = new Blob([response.data], {\n          type: this.exportForm.format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        });\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n\n        // 设置文件名\n        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');\n        const extension = this.exportForm.format === 'csv' ? 'csv' : 'xlsx';\n        link.download = `预约数据_${timestamp}.${extension}`;\n\n        // 触发下载\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n        this.$message.success('导出成功');\n        this.exportDialogVisible = false;\n      } catch (error) {\n        console.error('导出失败:', error);\n        this.$message.error('导出失败: ' + (error.response?.data?.detail || error.message));\n      } finally {\n        this.exportLoading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["reservationApi", "isReservationExpired", "name", "data", "loading", "reservations", "total", "currentPage", "pageSize", "filter", "code", "userName", "status", "date<PERSON><PERSON><PERSON>", "savedState", "isMobile", "window", "innerWidth", "exportDialogVisible", "exportLoading", "exportForm", "format", "scope", "<PERSON><PERSON><PERSON>s", "selectAllFields", "availableFields", "key", "label", "computed", "paginationLayout", "created", "restoreState", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "activated", "forceRefresh", "localStorage", "getItem", "console", "log", "removeItem", "fetchData", "checkReservationUpdates", "deactivated", "saveState", "beforeRouteLeave", "to", "from", "next", "path", "includes", "methods", "timestamp", "Date", "getTime", "params", "skip", "limit", "reservation_code", "undefined", "user_name", "_t", "sort_by", "sort_order", "length", "from_date", "to_date", "response", "getReservations", "items", "for<PERSON>ach", "reservation", "id", "start_datetime", "end_datetime", "fetchTotalForSpecialStatus", "error", "$message", "$t", "formatDateTime", "_row", "_column", "cellValue", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getStatusType", "getStatusText", "handleFilterChange", "resetFilter", "handlePageChange", "page", "viewReservation", "statusText", "statusType", "db<PERSON><PERSON>us", "startTime", "endTime", "reservationNumber", "reservation_number", "url", "query", "displayStatus", "displayStatusType", "setItem", "$router", "push", "savedCurrentPage", "statusParams", "allReservations", "now", "sort", "a", "b", "maxPage", "Math", "ceil", "targetPage", "min", "startIndex", "endIndex", "currentPageReservations", "slice", "$nextTick", "openReservationDetail", "stateKey", "state", "JSON", "stringify", "i", "savedStateStr", "parse", "fiveMinutes", "<PERSON><PERSON><PERSON>us", "$forceUpdate", "e", "showExportDialog", "map", "field", "handleSelectAllFields", "value", "handleExport", "warning", "exportData", "export_format", "export_scope", "selected_fields", "current_data", "equipment_name", "equipment_category", "equipment_location", "user_department", "user_contact", "user_email", "purpose", "created_at", "exportReservations", "blob", "Blob", "type", "URL", "createObjectURL", "link", "document", "createElement", "href", "toISOString", "replace", "extension", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "detail", "message"], "sources": ["src/views/admin/AdminReservation.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-reservation\">\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">{{ $t('admin.reservation') }}</h1>\n    </div>\n\n    <!-- 筛选卡片 -->\n    <el-card shadow=\"hover\" class=\"filter-card\">\n      <el-form :inline=\"true\" :model=\"filter\" class=\"filter-form\">\n        <el-form-item :label=\"$t('reservation.code')\">\n          <el-input\n            v-model=\"filter.code\"\n            :placeholder=\"$t('reservation.queryPlaceholder')\"\n            clearable\n            @keyup.enter.native=\"handleFilterChange\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('reservation.userName')\">\n          <el-input\n            v-model=\"filter.userName\"\n            :placeholder=\"$t('reservation.userName')\"\n            clearable\n            @keyup.enter.native=\"handleFilterChange\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('reservation.status')\">\n          <el-select\n            v-model=\"filter.status\"\n            :placeholder=\"$t('equipment.allStatus')\"\n            clearable\n            @change=\"handleFilterChange\"\n          >\n            <el-option\n              :label=\"$t('reservation.confirmed')\"\n              value=\"confirmed\"\n            ></el-option>\n            <el-option\n              :label=\"$t('reservation.inUse')\"\n              value=\"in_use\"\n            ></el-option>\n            <el-option\n              :label=\"$t('reservation.expired')\"\n              value=\"expired\"\n            ></el-option>\n            <el-option\n              :label=\"$t('reservation.cancelled')\"\n              value=\"cancelled\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('reservation.dateRange')\">\n          <el-date-picker\n            v-model=\"filter.dateRange\"\n            type=\"daterange\"\n            range-separator=\"至\"\n            :start-placeholder=\"$t('reservation.startDate')\"\n            :end-placeholder=\"$t('reservation.endDate')\"\n            value-format=\"yyyy-MM-dd\"\n            @change=\"handleFilterChange\"\n          >\n          </el-date-picker>\n        </el-form-item>\n\n        <el-form-item>\n          <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"handleFilterChange\">\n            {{ $t('common.search') }}\n          </el-button>\n          <el-button @click=\"resetFilter\" icon=\"el-icon-refresh-left\">\n            {{ $t('common.reset') }}\n          </el-button>\n          <el-button type=\"success\" icon=\"el-icon-download\" @click=\"showExportDialog\">\n            导出数据\n          </el-button>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 预定列表 -->\n    <el-card shadow=\"hover\" class=\"reservation-list\">\n      <div v-if=\"loading\" class=\"loading-container\">\n        <el-skeleton :rows=\"10\" animated />\n      </div>\n\n      <div v-else-if=\"reservations.length === 0\" class=\"empty-data\">\n        <el-empty :description=\"$t('common.noData')\"></el-empty>\n      </div>\n\n      <!-- 移动端卡片视图 -->\n      <div v-if=\"isMobile\" class=\"mobile-card-container\">\n        <div\n          v-for=\"reservation in reservations\"\n          :key=\"reservation.id\"\n          class=\"reservation-mobile-card\"\n        >\n          <div class=\"card-header\">\n            <div class=\"reservation-info\">\n              <div class=\"reservation-id\">ID: {{ reservation.id }}</div>\n              <div class=\"reservation-number\">{{ reservation.reservation_number || '-' }}</div>\n            </div>\n            <el-tag\n              :type=\"getStatusType(reservation)\"\n              size=\"medium\"\n              class=\"status-tag\"\n            >\n              {{ getStatusText(reservation) }}\n            </el-tag>\n          </div>\n\n          <div class=\"card-content\">\n            <div class=\"info-row\">\n              <span class=\"label\">预约码:</span>\n              <span class=\"value reservation-code\">{{ reservation.reservation_code }}</span>\n            </div>\n            <div class=\"info-row\">\n              <span class=\"label\">设备:</span>\n              <span class=\"value\">{{ reservation.equipment_name }}</span>\n            </div>\n            <div class=\"info-row\">\n              <span class=\"label\">用户:</span>\n              <span class=\"value\">{{ reservation.user_name }} ({{ reservation.user_department }})</span>\n            </div>\n            <div class=\"info-row\">\n              <span class=\"label\">联系方式:</span>\n              <span class=\"value\">{{ reservation.user_contact }}</span>\n            </div>\n            <div class=\"info-row\">\n              <span class=\"label\">开始时间:</span>\n              <span class=\"value\">{{ formatDateTime(null, null, reservation.start_datetime) }}</span>\n            </div>\n            <div class=\"info-row\">\n              <span class=\"label\">结束时间:</span>\n              <span class=\"value\">{{ formatDateTime(null, null, reservation.end_datetime) }}</span>\n            </div>\n          </div>\n\n          <div class=\"card-actions\">\n            <el-button\n              type=\"primary\"\n              size=\"small\"\n              @click=\"viewReservation(reservation)\"\n            >\n              查看详情\n            </el-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- 桌面端表格视图 -->\n      <el-table\n        v-else\n        :data=\"reservations\"\n        style=\"width: 100%\"\n        :default-sort=\"{ prop: 'id', order: 'descending' }\"\n        header-align=\"center\"\n        cell-class-name=\"text-center\"\n        border\n        stripe\n      >\n        <!-- 添加ID列 -->\n        <el-table-column\n          prop=\"id\"\n          :label=\"$t('common.id')\"\n          min-width=\"60\"\n          sortable\n        >\n          <template slot-scope=\"scope\">\n            <span style=\"font-weight: bold;\">{{ scope.row.id }}</span>\n          </template>\n        </el-table-column>\n\n        <!-- 添加预约序号列 -->\n        <el-table-column\n          prop=\"reservation_number\"\n          :label=\"$t('reservation.number')\"\n          min-width=\"180\"\n        >\n          <template slot-scope=\"scope\">\n            <span style=\"font-weight: bold;\">{{ scope.row.reservation_number || '-' }}</span>\n          </template>\n        </el-table-column>\n\n        <el-table-column\n          prop=\"reservation_code\"\n          :label=\"$t('reservation.code')\"\n          min-width=\"100\"\n        >\n          <template slot-scope=\"scope\">\n            <span style=\"color: #F56C6C; font-weight: bold;\">{{ scope.row.reservation_code }}</span>\n          </template>\n        </el-table-column>\n\n        <el-table-column\n          prop=\"equipment_name\"\n          :label=\"$t('reservation.equipmentName')\"\n          min-width=\"120\"\n        ></el-table-column>\n\n        <el-table-column\n          prop=\"user_name\"\n          :label=\"$t('reservation.userName')\"\n          min-width=\"100\"\n        ></el-table-column>\n\n        <el-table-column\n          prop=\"user_department\"\n          :label=\"$t('reservation.userDepartment')\"\n          min-width=\"100\"\n        ></el-table-column>\n\n        <el-table-column\n          prop=\"user_contact\"\n          :label=\"$t('reservation.userContact')\"\n          min-width=\"120\"\n        ></el-table-column>\n\n        <el-table-column\n          prop=\"start_datetime\"\n          :label=\"$t('reservation.startTime')\"\n          min-width=\"150\"\n          :formatter=\"formatDateTime\"\n        ></el-table-column>\n\n        <el-table-column\n          prop=\"end_datetime\"\n          :label=\"$t('reservation.endTime')\"\n          min-width=\"150\"\n          :formatter=\"formatDateTime\"\n        ></el-table-column>\n\n        <el-table-column\n          prop=\"status\"\n          :label=\"$t('reservation.status')\"\n          min-width=\"100\"\n        >\n          <template slot-scope=\"scope\">\n            <el-tag\n              :type=\"getStatusType(scope.row)\"\n              size=\"medium\"\n              style=\"font-weight: bold; padding: 0px 10px; font-size: 14px;\"\n            >\n              {{ getStatusText(scope.row) }}\n            </el-tag>\n          </template>\n        </el-table-column>\n\n        <el-table-column\n          :label=\"$t('common.operation')\"\n          min-width=\"100\"\n        >\n          <template slot-scope=\"scope\">\n            <el-button\n              type=\"text\"\n              size=\"small\"\n              @click=\"viewReservation(scope.row)\"\n            >\n              {{ $t('admin.viewReservation') }}\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n\n      <!-- 分页 -->\n      <div class=\"pagination-container\" v-if=\"reservations.length > 0\">\n        <el-pagination\n          background\n          :layout=\"paginationLayout\"\n          :total=\"total\"\n          :page-size=\"pageSize\"\n          :current-page.sync=\"currentPage\"\n          @current-change=\"handlePageChange\"\n        ></el-pagination>\n      </div>\n    </el-card>\n\n    <!-- 导出对话框 -->\n    <el-dialog\n      title=\"导出预约数据\"\n      :visible.sync=\"exportDialogVisible\"\n      width=\"600px\"\n      :close-on-click-modal=\"false\"\n    >\n      <el-form :model=\"exportForm\" label-width=\"120px\">\n        <!-- 导出格式 -->\n        <el-form-item label=\"导出格式\">\n          <div style=\"padding: 8px 0;\">\n            <i class=\"el-icon-document\"></i>\n            <span style=\"margin-left: 8px;\">CSV格式 (.csv)</span>\n            <div style=\"font-size: 12px; color: #999; margin-top: 4px; margin-left: 24px;\">\n              支持Excel打开，包含完整的中文字段名\n            </div>\n          </div>\n        </el-form-item>\n\n        <!-- 导出范围 -->\n        <el-form-item label=\"导出范围\">\n          <el-radio-group v-model=\"exportForm.scope\">\n            <el-radio label=\"current\">当前页面数据 ({{ reservations.length }} 条)</el-radio>\n            <el-radio label=\"all\">全部筛选结果 ({{ total }} 条)</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <!-- 选择字段 -->\n        <el-form-item label=\"导出字段\">\n          <div style=\"max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; padding: 10px;\">\n            <el-checkbox\n              v-model=\"selectAllFields\"\n              @change=\"handleSelectAllFields\"\n              style=\"margin-bottom: 10px; font-weight: bold;\"\n            >\n              全选\n            </el-checkbox>\n            <div style=\"display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;\">\n              <el-checkbox\n                v-for=\"field in availableFields\"\n                :key=\"field.key\"\n                v-model=\"exportForm.selectedFields\"\n                :label=\"field.key\"\n              >\n                {{ field.label }}\n              </el-checkbox>\n            </div>\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"exportDialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"handleExport\" :loading=\"exportLoading\">\n          {{ exportLoading ? '导出中...' : '确认导出' }}\n        </el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { reservationApi } from '@/api'\nimport { isReservationExpired } from '@/utils/date'\n\nexport default {\n  name: 'AdminReservation',\n\n  data() {\n    return {\n      loading: false,\n      reservations: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 10,\n      filter: {\n        code: '',\n        userName: '',\n        status: '',\n        dateRange: []\n      },\n      // 添加一个保存页面状态的变量\n      savedState: null,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n      // 导出相关\n      exportDialogVisible: false,\n      exportLoading: false,\n      exportForm: {\n        format: 'csv',\n        scope: 'current',\n        selectedFields: []\n      },\n      selectAllFields: true,\n      availableFields: [\n        { key: 'id', label: 'ID' },\n        { key: 'reservation_number', label: '预约编号' },\n        { key: 'reservation_code', label: '预约码' },\n        { key: 'equipment_name', label: '设备名称' },\n        { key: 'equipment_category', label: '设备类别' },\n        { key: 'equipment_location', label: '设备位置' },\n        { key: 'user_name', label: '预约人姓名' },\n        { key: 'user_department', label: '预约人部门' },\n        { key: 'user_contact', label: '联系方式' },\n        { key: 'user_email', label: '邮箱地址' },\n        { key: 'start_datetime', label: '开始时间' },\n        { key: 'end_datetime', label: '结束时间' },\n        { key: 'purpose', label: '使用目的' },\n        { key: 'status', label: '预约状态' },\n        { key: 'created_at', label: '创建时间' }\n      ]\n    }\n  },\n\n  computed: {\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile\n        ? 'prev, next'\n        : 'prev, pager, next';\n    }\n  },\n\n  created() {\n    // 检查是否有保存的状态并恢复它\n    this.restoreState();\n\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n\n  // 添加activated钩子函数，在组件被激活时调用（如从预定详情页面返回）\n  activated() {\n    // 检查是否需要强制刷新\n    const forceRefresh = localStorage.getItem('force_refresh_reservation_list');\n    if (forceRefresh === 'true') {\n      console.log('检测到强制刷新标记，重新获取数据');\n      localStorage.removeItem('force_refresh_reservation_list');\n      this.fetchData();\n      return;\n    }\n\n    // 当从其他页面返回时，尝试恢复状态\n    this.restoreState();\n    // 检查预约状态更新\n    this.checkReservationUpdates();\n  },\n\n  // 添加deactivated钩子函数，在组件被停用时调用（如进入预定详情页面）\n  deactivated() {\n    // 保存当前页面状态\n    this.saveState();\n  },\n\n  // 添加beforeRouteLeave导航守卫，在离开组件时调用\n  beforeRouteLeave(to, from, next) {\n    // 如果是跳转到预定详情页面，保存状态\n    if (to.path.includes('/admin/reservation/') && to.path !== '/admin/reservation') {\n      this.saveState();\n    }\n    next();\n  },\n\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n\n    async fetchData() {\n      this.loading = true\n      console.log('Fetching data with filter:', this.filter);\n      console.log('Current page:', this.currentPage);\n\n      try {\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime()\n        console.log('添加时间戳参数:', timestamp)\n\n        const params = {\n          skip: (this.currentPage - 1) * this.pageSize, // 将页码转换为skip参数\n          limit: this.pageSize,\n          reservation_code: this.filter.code || undefined,\n          user_name: this.filter.userName || undefined,\n          _t: timestamp, // 添加时间戳，防止缓存\n          sort_by: 'id', // 按ID排序\n          sort_order: 'desc' // 降序排序\n        }\n\n        // 添加日期范围过滤\n        if (this.filter.dateRange && this.filter.dateRange.length === 2) {\n          params.from_date = this.filter.dateRange[0]\n          params.to_date = this.filter.dateRange[1]\n        }\n\n        // 处理不同的状态筛选\n        if (this.filter.status) {\n          console.log('Filtering by status:', this.filter.status);\n\n          // 直接使用选择的状态值，因为后端现在支持所有状态\n          params.status = this.filter.status;\n          console.log(`Setting status parameter to \"${this.filter.status}\"`);\n        }\n\n        console.log('Fetching reservations with params:', params)\n        const response = await reservationApi.getReservations(params)\n        console.log('API Response:', response)\n        let reservations = response.data.items || []\n        console.log('Received reservations:', reservations)\n\n        // 不再需要在前端进行筛选，因为后端已经返回了正确的状态\n        // 只记录日志，帮助调试\n        if (this.filter.status) {\n          console.log(`获取到状态为 ${this.filter.status} 的预约数量: ${reservations.length}`);\n\n          // 记录每个预约的状态，帮助调试\n          reservations.forEach(reservation => {\n            console.log(`预约ID=${reservation.id}, 状态=${reservation.status}, 开始时间=${reservation.start_datetime}, 结束时间=${reservation.end_datetime}`);\n          });\n        }\n\n        console.log('Filtered reservations:', reservations)\n        this.reservations = reservations\n\n        // 如果是特殊状态，总数需要重新计算\n        if (this.filter.status === 'in_use' || this.filter.status === 'expired' || this.filter.status === 'confirmed' || this.filter.status === 'cancelled') {\n          // 对于特殊状态，我们需要获取所有页的数据来计算总数\n          // 这里我们先使用当前页的数据计算一个临时总数\n          this.total = reservations.length\n          console.log(`Temporary total based on current page: ${this.total}`);\n\n          // 无论当前页是否有数据，都获取所有数据来计算真实总数\n          this.fetchTotalForSpecialStatus()\n        } else {\n          this.total = response.data.total\n          console.log(`Total from API response: ${this.total}`);\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservations:', error)\n        this.$message.error(this.$t('error.serverError'))\n      } finally {\n        this.loading = false\n      }\n    },\n\n    formatDateTime(_row, _column, cellValue) {\n      if (!cellValue) return ''\n\n      const date = new Date(cellValue)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n    },\n\n    getStatusType(reservation) {\n      // 直接根据后端返回的状态返回对应的类型\n      switch (reservation.status) {\n        case 'cancelled':\n          return 'danger';  // 已取消 - 红色\n        case 'expired':\n          return 'warning'; // 已过期 - 橙色\n        case 'in_use':\n          return 'primary'; // 使用中 - 蓝色\n        case 'confirmed':\n          return 'success'; // 已确认 - 绿色\n        default:\n          return 'info';    // 其他状态 - 灰色\n      }\n    },\n\n    getStatusText(reservation) {\n      // 直接根据后端返回的状态返回对应的文本\n      switch (reservation.status) {\n        case 'cancelled':\n          return this.$t('reservation.statusCancelled'); // 已取消\n        case 'expired':\n          return this.$t('reservation.statusExpired');   // 已过期\n        case 'in_use':\n          return this.$t('reservation.statusInUse');     // 使用中\n        case 'confirmed':\n          return this.$t('reservation.statusConfirmed'); // 已确认\n        default:\n          return reservation.status; // 其他状态直接显示\n      }\n    },\n\n    handleFilterChange() {\n      this.currentPage = 1\n      this.fetchData()\n    },\n\n    resetFilter() {\n      this.filter = {\n        code: '',\n        userName: '',\n        status: '',\n        dateRange: []\n      }\n      this.handleFilterChange()\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page\n      this.fetchData()\n    },\n\n    viewReservation(reservation) {\n      // 计算当前预约的实际状态文本和类型\n      const statusText = this.getStatusText(reservation)\n      const statusType = this.getStatusType(reservation)\n\n      console.log('计算的状态信息:', {\n        statusText,\n        statusType,\n        dbStatus: reservation.status,\n        startTime: reservation.start_datetime,\n        endTime: reservation.end_datetime,\n        reservationNumber: reservation.reservation_number\n      })\n\n      // 构建URL，添加预约码、时间参数、预约序号和计算好的状态信息\n      // 使用预约序号作为路径参数，而不是预约码\n      const url = {\n        path: `/admin/reservation/${reservation.reservation_code}`,\n        query: {\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime,\n          displayStatus: statusText,\n          displayStatusType: statusType,\n          reservationNumber: reservation.reservation_number // 添加预约序号参数\n        }\n      }\n\n      // 每次查看预约时，都重新设置一个标记，表示需要显示预约序号通知\n      localStorage.setItem('show_reservation_number_notification', 'true')\n\n      // 清除之前的预约序号，确保每次都使用新的预约序号\n      localStorage.removeItem('current_reservation_number')\n\n      // 将预约序号保存到localStorage，以便在页面刷新后仍然可以使用\n      if (reservation.reservation_number) {\n        localStorage.setItem('current_reservation_number', reservation.reservation_number)\n        console.log('保存预约序号到localStorage:', reservation.reservation_number)\n\n        // 强制使用预约序号查询，而不是预约码\n        localStorage.setItem('force_use_reservation_number', 'true')\n      }\n\n      this.$router.push(url)\n    },\n\n    // 获取特殊状态的总记录数并更新当前页面的预约列表\n    async fetchTotalForSpecialStatus() {\n      try {\n        console.log('Fetching total for special status:', this.filter.status);\n        console.log('Current page before fetchTotal:', this.currentPage);\n\n        // 保存当前页码，以便后续恢复\n        const savedCurrentPage = this.currentPage;\n\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime()\n        console.log('添加时间戳参数:', timestamp)\n\n        // 构建查询参数，不包含分页参数\n        const params = {\n          // 不设置limit，获取所有记录\n          limit: 1000, // 设置一个较大的值以获取尽可能多的记录\n          skip: 0,\n          reservation_code: this.filter.code || undefined, // 使用reservation_code而不是code\n          user_name: this.filter.userName || undefined,\n          _t: timestamp, // 添加时间戳，防止缓存\n          sort_by: 'id', // 按ID排序\n          sort_order: 'desc' // 降序排序\n        }\n\n        // 添加日期范围过滤\n        if (this.filter.dateRange && this.filter.dateRange.length === 2) {\n          params.from_date = this.filter.dateRange[0]\n          params.to_date = this.filter.dateRange[1]\n        }\n\n        // 直接获取指定状态的预约\n        const statusParams = { ...params, status: this.filter.status };\n\n        console.log(`直接获取状态为 ${this.filter.status} 的预约`);\n        const response = await reservationApi.getReservations(statusParams);\n\n        // 使用后端返回的结果\n        let allReservations = response.data.items || [];\n\n        console.log(`Total reservations before filtering: ${allReservations.length}`);\n\n        const now = new Date();\n        console.log(`当前日期: ${now}`);\n\n        // 不再需要在前端进行筛选，因为后端已经返回了正确的状态\n        // 只记录日志，帮助调试\n        console.log(`获取到状态为 ${this.filter.status} 的预约数量: ${allReservations.length}`);\n\n        // 记录每个预约的状态，帮助调试\n        allReservations.forEach(reservation => {\n          console.log(`预约ID=${reservation.id}, 状态=${reservation.status}, 开始时间=${reservation.start_datetime}, 结束时间=${reservation.end_datetime}`);\n        });\n\n        // 更新总数\n        this.total = allReservations.length;\n        console.log(`Updated total to: ${this.total}`);\n\n        // 对筛选后的结果进行排序\n        if (this.filter.status === 'expired') {\n          // 对于已过期，按结束时间倒序排列\n          allReservations.sort((a, b) => new Date(b.end_datetime) - new Date(a.end_datetime));\n        } else if (this.filter.status === 'in_use') {\n          // 对于使用中，按开始时间倒序排列\n          allReservations.sort((a, b) => new Date(b.start_datetime) - new Date(a.start_datetime));\n        } else if (this.filter.status === 'confirmed') {\n          // 对于已确认，按开始时间升序排列\n          allReservations.sort((a, b) => new Date(a.start_datetime) - new Date(b.start_datetime));\n        } else if (this.filter.status === 'cancelled') {\n          // 对于已取消，按结束时间倒序排列\n          allReservations.sort((a, b) => new Date(b.end_datetime) - new Date(a.end_datetime));\n        }\n\n        // 计算当前页应该显示的预约\n        const maxPage = Math.ceil(allReservations.length / this.pageSize) || 1;\n\n        // 确保页码不超过最大页数\n        const targetPage = Math.min(savedCurrentPage, maxPage);\n        console.log(`计算页数: 总记录数=${allReservations.length}, 每页记录数=${this.pageSize}, 最大页数=${maxPage}, 目标页码=${targetPage}`);\n\n        const startIndex = (targetPage - 1) * this.pageSize;\n        const endIndex = Math.min(startIndex + this.pageSize, allReservations.length);\n        const currentPageReservations = allReservations.slice(startIndex, endIndex);\n\n        console.log(`当前页数据范围: 开始索引=${startIndex}, 结束索引=${endIndex}, 当前页记录数=${currentPageReservations.length}`);\n\n        // 更新当前页面的预约列表\n        if (currentPageReservations.length > 0) {\n          // 先更新数据\n          this.reservations = currentPageReservations;\n          // 然后更新页码，避免触发不必要的重新获取数据\n          if (this.currentPage !== targetPage) {\n            console.log(`更新页码: 从 ${this.currentPage} 到 ${targetPage}`);\n            this.$nextTick(() => {\n              this.currentPage = targetPage;\n            });\n          }\n          console.log(`更新当前页面的预约列表: ${this.reservations.length} 条记录`);\n        } else if (allReservations.length > 0) {\n          // 如果当前页没有数据但总数据不为空，自动回到第一页\n          console.log(`当前页没有数据，回到第一页`);\n          this.reservations = allReservations.slice(0, this.pageSize);\n          if (this.currentPage !== 1) {\n            this.$nextTick(() => {\n              this.currentPage = 1;\n            });\n          }\n        } else {\n          // 如果没有找到任何预约\n          this.reservations = [];\n          console.log('没有找到符合条件的预约');\n        }\n\n        console.log('Current page after fetchTotal:', this.currentPage);\n      } catch (error) {\n        console.error('Failed to fetch total for special status:', error);\n      }\n    },\n\n    // 保存当前页面状态\n    saveState() {\n      this.savedState = {\n        filter: { ...this.filter },\n        currentPage: this.currentPage\n      };\n      console.log('Saved state:', this.savedState);\n    },\n\n    // 恢复保存的页面状态\n    restoreState() {\n      if (this.savedState) {\n        this.filter = { ...this.savedState.filter };\n        this.currentPage = this.savedState.currentPage;\n        console.log('Restored state:', this.savedState);\n        this.fetchData();\n      } else {\n        this.fetchData();\n      }\n    },\n\n    // 打开预约详情\n    openReservationDetail(reservation) {\n      console.log('打开预约详情:', reservation);\n\n      // 计算当前状态\n      const statusText = this.getStatusText(reservation);\n      const statusType = this.getStatusType(reservation);\n      const dbStatus = reservation.status || 'confirmed';\n      const startTime = reservation.start_datetime;\n      const endTime = reservation.end_datetime;\n\n      console.log('计算的状态信息:', {\n        statusText,\n        statusType,\n        dbStatus,\n        startTime,\n        endTime\n      });\n\n      // 将状态保存到localStorage，以便详情页面使用\n      const stateKey = `reservation_status_${reservation.reservation_code}`;\n      const state = {\n        statusText,\n        statusType,\n        dbStatus,\n        timestamp: new Date().getTime()\n      };\n\n      console.log('Saved state:', state);\n      localStorage.setItem(stateKey, JSON.stringify(state));\n\n      // 导航到详情页面，并传递状态和时间参数\n      this.$router.push({\n        name: 'AdminReservationDetail',\n        params: { code: reservation.reservation_code },\n        query: {\n          displayStatus: statusText,\n          displayStatusType: statusType,\n          startTime: startTime,\n          endTime: endTime\n        }\n      });\n    },\n\n    // 在激活（从其他页面返回）时，检查预约状态是否需要更新\n    async checkReservationUpdates() {\n      // 如果当前显示的是预约列表，则检查是否需要刷新\n      if (this.reservations.length > 0) {\n        // 检查localStorage中是否有任何预约状态发生了变化\n        for (let i = 0; i < this.reservations.length; i++) {\n          const reservation = this.reservations[i];\n          const stateKey = `reservation_status_${reservation.reservation_code}`;\n          const savedStateStr = localStorage.getItem(stateKey);\n\n          if (savedStateStr) {\n            try {\n              const savedState = JSON.parse(savedStateStr);\n\n              // 检查保存的状态是否还是新鲜的（5分钟内）\n              const now = new Date().getTime();\n              const fiveMinutes = 5 * 60 * 1000;\n\n              if (now - savedState.timestamp <= fiveMinutes) {\n                console.log(`检测到预约 ${reservation.reservation_code} 的状态可能已更改，保存的状态:`, savedState);\n\n                // 检查是否有强制状态更新，特别是已取消状态\n                if (savedState.forcedStatus === 'cancelled' ||\n                    (savedState.statusText === this.$t('reservation.cancelled') &&\n                     savedState.statusType === 'danger')) {\n                  console.log(`预约 ${reservation.reservation_code} 已被标记为已取消，将在界面上更新`);\n\n                  // 更新当前列表中的预约状态\n                  this.reservations[i].status = 'cancelled';\n\n                  // 强制更新UI\n                  this.$forceUpdate();\n                }\n              } else {\n                // 如果状态过期，则移除它\n                console.log(`预约 ${reservation.reservation_code} 的保存状态已过期，移除`);\n                localStorage.removeItem(stateKey);\n              }\n            } catch (e) {\n              console.error('解析保存的状态时出错:', e);\n            }\n          }\n        }\n      }\n    },\n\n    // 显示导出对话框\n    showExportDialog() {\n      this.exportDialogVisible = true\n      // 默认选择所有字段\n      this.exportForm.selectedFields = this.availableFields.map(field => field.key)\n      this.selectAllFields = true\n    },\n\n    // 处理全选字段\n    handleSelectAllFields(value) {\n      if (value) {\n        this.exportForm.selectedFields = this.availableFields.map(field => field.key)\n      } else {\n        this.exportForm.selectedFields = []\n      }\n    },\n\n    // 处理导出\n    async handleExport() {\n      if (this.exportForm.selectedFields.length === 0) {\n        this.$message.warning('请至少选择一个导出字段')\n        return\n      }\n\n      this.exportLoading = true\n      try {\n        // 构建导出请求数据\n        const exportData = {\n          export_format: this.exportForm.format,\n          export_scope: this.exportForm.scope,\n          selected_fields: this.exportForm.selectedFields\n        }\n\n        // 如果是导出全部筛选结果，添加筛选条件\n        if (this.exportForm.scope === 'all') {\n          exportData.reservation_code = this.filter.code || undefined\n          exportData.user_name = this.filter.userName || undefined\n          exportData.status = this.filter.status || undefined\n\n          if (this.filter.dateRange && this.filter.dateRange.length === 2) {\n            exportData.from_date = this.filter.dateRange[0]\n            exportData.to_date = this.filter.dateRange[1]\n          }\n        } else {\n          // 导出当前页面数据\n          exportData.current_data = this.reservations.map(reservation => ({\n            id: reservation.id,\n            reservation_number: reservation.reservation_number,\n            reservation_code: reservation.reservation_code,\n            equipment_name: reservation.equipment_name,\n            equipment_category: reservation.equipment_category,\n            equipment_location: reservation.equipment_location,\n            user_name: reservation.user_name,\n            user_department: reservation.user_department,\n            user_contact: reservation.user_contact,\n            user_email: reservation.user_email,\n            start_datetime: reservation.start_datetime,\n            end_datetime: reservation.end_datetime,\n            purpose: reservation.purpose,\n            status: reservation.status,\n            created_at: reservation.created_at\n          }))\n        }\n\n        console.log('导出请求数据:', exportData)\n\n        // 调用导出API\n        const response = await reservationApi.exportReservations(exportData)\n\n        // 创建下载链接\n        const blob = new Blob([response.data], {\n          type: this.exportForm.format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\n        })\n\n        const url = window.URL.createObjectURL(blob)\n        const link = document.createElement('a')\n        link.href = url\n\n        // 设置文件名\n        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')\n        const extension = this.exportForm.format === 'csv' ? 'csv' : 'xlsx'\n        link.download = `预约数据_${timestamp}.${extension}`\n\n        // 触发下载\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n        window.URL.revokeObjectURL(url)\n\n        this.$message.success('导出成功')\n        this.exportDialogVisible = false\n\n      } catch (error) {\n        console.error('导出失败:', error)\n        this.$message.error('导出失败: ' + (error.response?.data?.detail || error.message))\n      } finally {\n        this.exportLoading = false\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-reservation {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 15px 20px;\n  background-color: #FFFFFF;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n}\n\n.page-title {\n  margin: 0;\n  font-size: 24px;\n  color: #303133;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.reservation-list {\n  margin-bottom: 20px;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.empty-data {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.pagination-container {\n  text-align: center;\n  margin-top: 20px;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n/* 移动端卡片样式 */\n.mobile-card-container {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.reservation-mobile-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 20px;\n  border: 1px solid #e8e8e8;\n  min-height: 200px;\n  width: 100%;\n  max-width: 100%;\n  margin: 0 auto;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.reservation-info {\n  flex: 1;\n}\n\n.reservation-id {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n}\n\n.reservation-number {\n  font-weight: bold;\n  font-size: 14px;\n  color: #333;\n}\n\n.status-tag {\n  margin-left: 10px;\n}\n\n.card-content {\n  margin-bottom: 15px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 8px;\n  align-items: flex-start;\n}\n\n.info-row .label {\n  min-width: 70px;\n  font-size: 13px;\n  color: #666;\n  margin-right: 10px;\n  flex-shrink: 0;\n}\n\n.info-row .value {\n  font-size: 13px;\n  color: #333;\n  flex: 1;\n  word-break: break-all;\n}\n\n.info-row .reservation-code {\n  color: #F56C6C;\n  font-weight: bold;\n}\n\n.card-actions {\n  text-align: center;\n  padding-top: 10px;\n  border-top: 1px solid #f0f0f0;\n}\n\n@media (max-width: 768px) {\n  .admin-reservation {\n    padding-top: 60px;\n    padding-bottom: 100px;\n  }\n\n  .filter-form .el-form-item {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n\n  .reservation-list {\n    margin-top: 15px;\n  }\n}\n</style>\n"], "mappings": "AAmVA,SAAAA,cAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,YAAA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,MAAA;QACAC,IAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACA;MACAC,UAAA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;MACA;MACAC,mBAAA;MACAC,aAAA;MACAC,UAAA;QACAC,MAAA;QACAC,KAAA;QACAC,cAAA;MACA;MACAC,eAAA;MACAC,eAAA,GACA;QAAAC,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA,GACA;QAAAD,GAAA;QAAAC,KAAA;MAAA;IAEA;EACA;EAEAC,QAAA;IACA;IACAC,iBAAA;MACA,YAAAd,QAAA,GACA,eACA;IACA;EACA;EAEAe,QAAA;IACA;IACA,KAAAC,YAAA;;IAEA;IACAf,MAAA,CAAAgB,gBAAA,gBAAAC,YAAA;EACA;EAEAC,cAAA;IACA;IACAlB,MAAA,CAAAmB,mBAAA,gBAAAF,YAAA;EACA;EAEA;EACAG,UAAA;IACA;IACA,MAAAC,YAAA,GAAAC,YAAA,CAAAC,OAAA;IACA,IAAAF,YAAA;MACAG,OAAA,CAAAC,GAAA;MACAH,YAAA,CAAAI,UAAA;MACA,KAAAC,SAAA;MACA;IACA;;IAEA;IACA,KAAAZ,YAAA;IACA;IACA,KAAAa,uBAAA;EACA;EAEA;EACAC,YAAA;IACA;IACA,KAAAC,SAAA;EACA;EAEA;EACAC,iBAAAC,EAAA,EAAAC,IAAA,EAAAC,IAAA;IACA;IACA,IAAAF,EAAA,CAAAG,IAAA,CAAAC,QAAA,2BAAAJ,EAAA,CAAAG,IAAA;MACA,KAAAL,SAAA;IACA;IACAI,IAAA;EACA;EAEAG,OAAA;IACA;IACApB,aAAA;MACA,KAAAlB,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IAEA,MAAA0B,UAAA;MACA,KAAAvC,OAAA;MACAoC,OAAA,CAAAC,GAAA,oCAAAhC,MAAA;MACA+B,OAAA,CAAAC,GAAA,uBAAAlC,WAAA;MAEA;QACA;QACA,MAAA+C,SAAA,OAAAC,IAAA,GAAAC,OAAA;QACAhB,OAAA,CAAAC,GAAA,aAAAa,SAAA;QAEA,MAAAG,MAAA;UACAC,IAAA,QAAAnD,WAAA,aAAAC,QAAA;UAAA;UACAmD,KAAA,OAAAnD,QAAA;UACAoD,gBAAA,OAAAnD,MAAA,CAAAC,IAAA,IAAAmD,SAAA;UACAC,SAAA,OAAArD,MAAA,CAAAE,QAAA,IAAAkD,SAAA;UACAE,EAAA,EAAAT,SAAA;UAAA;UACAU,OAAA;UAAA;UACAC,UAAA;QACA;;QAEA;QACA,SAAAxD,MAAA,CAAAI,SAAA,SAAAJ,MAAA,CAAAI,SAAA,CAAAqD,MAAA;UACAT,MAAA,CAAAU,SAAA,QAAA1D,MAAA,CAAAI,SAAA;UACA4C,MAAA,CAAAW,OAAA,QAAA3D,MAAA,CAAAI,SAAA;QACA;;QAEA;QACA,SAAAJ,MAAA,CAAAG,MAAA;UACA4B,OAAA,CAAAC,GAAA,8BAAAhC,MAAA,CAAAG,MAAA;;UAEA;UACA6C,MAAA,CAAA7C,MAAA,QAAAH,MAAA,CAAAG,MAAA;UACA4B,OAAA,CAAAC,GAAA,sCAAAhC,MAAA,CAAAG,MAAA;QACA;QAEA4B,OAAA,CAAAC,GAAA,uCAAAgB,MAAA;QACA,MAAAY,QAAA,SAAArE,cAAA,CAAAsE,eAAA,CAAAb,MAAA;QACAjB,OAAA,CAAAC,GAAA,kBAAA4B,QAAA;QACA,IAAAhE,YAAA,GAAAgE,QAAA,CAAAlE,IAAA,CAAAoE,KAAA;QACA/B,OAAA,CAAAC,GAAA,2BAAApC,YAAA;;QAEA;QACA;QACA,SAAAI,MAAA,CAAAG,MAAA;UACA4B,OAAA,CAAAC,GAAA,gBAAAhC,MAAA,CAAAG,MAAA,WAAAP,YAAA,CAAA6D,MAAA;;UAEA;UACA7D,YAAA,CAAAmE,OAAA,CAAAC,WAAA;YACAjC,OAAA,CAAAC,GAAA,SAAAgC,WAAA,CAAAC,EAAA,QAAAD,WAAA,CAAA7D,MAAA,UAAA6D,WAAA,CAAAE,cAAA,UAAAF,WAAA,CAAAG,YAAA;UACA;QACA;QAEApC,OAAA,CAAAC,GAAA,2BAAApC,YAAA;QACA,KAAAA,YAAA,GAAAA,YAAA;;QAEA;QACA,SAAAI,MAAA,CAAAG,MAAA,sBAAAH,MAAA,CAAAG,MAAA,uBAAAH,MAAA,CAAAG,MAAA,yBAAAH,MAAA,CAAAG,MAAA;UACA;UACA;UACA,KAAAN,KAAA,GAAAD,YAAA,CAAA6D,MAAA;UACA1B,OAAA,CAAAC,GAAA,gDAAAnC,KAAA;;UAEA;UACA,KAAAuE,0BAAA;QACA;UACA,KAAAvE,KAAA,GAAA+D,QAAA,CAAAlE,IAAA,CAAAG,KAAA;UACAkC,OAAA,CAAAC,GAAA,kCAAAnC,KAAA;QACA;MACA,SAAAwE,KAAA;QACAtC,OAAA,CAAAsC,KAAA,kCAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA,MAAAE,EAAA;MACA;QACA,KAAA5E,OAAA;MACA;IACA;IAEA6E,eAAAC,IAAA,EAAAC,OAAA,EAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,MAAAC,IAAA,OAAA9B,IAAA,CAAA6B,SAAA;MACA,UAAAC,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAM,QAAA,IAAAF,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAO,UAAA,IAAAH,QAAA;IACA;IAEAI,cAAApB,WAAA;MACA;MACA,QAAAA,WAAA,CAAA7D,MAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;MACA;IACA;IAEAkF,cAAArB,WAAA;MACA;MACA,QAAAA,WAAA,CAAA7D,MAAA;QACA;UACA,YAAAoE,EAAA;QAAA;QACA;UACA,YAAAA,EAAA;QAAA;QACA;UACA,YAAAA,EAAA;QAAA;QACA;UACA,YAAAA,EAAA;QAAA;QACA;UACA,OAAAP,WAAA,CAAA7D,MAAA;QAAA;MACA;IACA;IAEAmF,mBAAA;MACA,KAAAxF,WAAA;MACA,KAAAoC,SAAA;IACA;IAEAqD,YAAA;MACA,KAAAvF,MAAA;QACAC,IAAA;QACAC,QAAA;QACAC,MAAA;QACAC,SAAA;MACA;MACA,KAAAkF,kBAAA;IACA;IAEAE,iBAAAC,IAAA;MACA,KAAA3F,WAAA,GAAA2F,IAAA;MACA,KAAAvD,SAAA;IACA;IAEAwD,gBAAA1B,WAAA;MACA;MACA,MAAA2B,UAAA,QAAAN,aAAA,CAAArB,WAAA;MACA,MAAA4B,UAAA,QAAAR,aAAA,CAAApB,WAAA;MAEAjC,OAAA,CAAAC,GAAA;QACA2D,UAAA;QACAC,UAAA;QACAC,QAAA,EAAA7B,WAAA,CAAA7D,MAAA;QACA2F,SAAA,EAAA9B,WAAA,CAAAE,cAAA;QACA6B,OAAA,EAAA/B,WAAA,CAAAG,YAAA;QACA6B,iBAAA,EAAAhC,WAAA,CAAAiC;MACA;;MAEA;MACA;MACA,MAAAC,GAAA;QACAxD,IAAA,wBAAAsB,WAAA,CAAAb,gBAAA;QACAgD,KAAA;UACAL,SAAA,EAAA9B,WAAA,CAAAE,cAAA;UACA6B,OAAA,EAAA/B,WAAA,CAAAG,YAAA;UACAiC,aAAA,EAAAT,UAAA;UACAU,iBAAA,EAAAT,UAAA;UACAI,iBAAA,EAAAhC,WAAA,CAAAiC,kBAAA;QACA;MACA;;MAEA;MACApE,YAAA,CAAAyE,OAAA;;MAEA;MACAzE,YAAA,CAAAI,UAAA;;MAEA;MACA,IAAA+B,WAAA,CAAAiC,kBAAA;QACApE,YAAA,CAAAyE,OAAA,+BAAAtC,WAAA,CAAAiC,kBAAA;QACAlE,OAAA,CAAAC,GAAA,yBAAAgC,WAAA,CAAAiC,kBAAA;;QAEA;QACApE,YAAA,CAAAyE,OAAA;MACA;MAEA,KAAAC,OAAA,CAAAC,IAAA,CAAAN,GAAA;IACA;IAEA;IACA,MAAA9B,2BAAA;MACA;QACArC,OAAA,CAAAC,GAAA,4CAAAhC,MAAA,CAAAG,MAAA;QACA4B,OAAA,CAAAC,GAAA,yCAAAlC,WAAA;;QAEA;QACA,MAAA2G,gBAAA,QAAA3G,WAAA;;QAEA;QACA,MAAA+C,SAAA,OAAAC,IAAA,GAAAC,OAAA;QACAhB,OAAA,CAAAC,GAAA,aAAAa,SAAA;;QAEA;QACA,MAAAG,MAAA;UACA;UACAE,KAAA;UAAA;UACAD,IAAA;UACAE,gBAAA,OAAAnD,MAAA,CAAAC,IAAA,IAAAmD,SAAA;UAAA;UACAC,SAAA,OAAArD,MAAA,CAAAE,QAAA,IAAAkD,SAAA;UACAE,EAAA,EAAAT,SAAA;UAAA;UACAU,OAAA;UAAA;UACAC,UAAA;QACA;;QAEA;QACA,SAAAxD,MAAA,CAAAI,SAAA,SAAAJ,MAAA,CAAAI,SAAA,CAAAqD,MAAA;UACAT,MAAA,CAAAU,SAAA,QAAA1D,MAAA,CAAAI,SAAA;UACA4C,MAAA,CAAAW,OAAA,QAAA3D,MAAA,CAAAI,SAAA;QACA;;QAEA;QACA,MAAAsG,YAAA;UAAA,GAAA1D,MAAA;UAAA7C,MAAA,OAAAH,MAAA,CAAAG;QAAA;QAEA4B,OAAA,CAAAC,GAAA,iBAAAhC,MAAA,CAAAG,MAAA;QACA,MAAAyD,QAAA,SAAArE,cAAA,CAAAsE,eAAA,CAAA6C,YAAA;;QAEA;QACA,IAAAC,eAAA,GAAA/C,QAAA,CAAAlE,IAAA,CAAAoE,KAAA;QAEA/B,OAAA,CAAAC,GAAA,yCAAA2E,eAAA,CAAAlD,MAAA;QAEA,MAAAmD,GAAA,OAAA9D,IAAA;QACAf,OAAA,CAAAC,GAAA,UAAA4E,GAAA;;QAEA;QACA;QACA7E,OAAA,CAAAC,GAAA,gBAAAhC,MAAA,CAAAG,MAAA,WAAAwG,eAAA,CAAAlD,MAAA;;QAEA;QACAkD,eAAA,CAAA5C,OAAA,CAAAC,WAAA;UACAjC,OAAA,CAAAC,GAAA,SAAAgC,WAAA,CAAAC,EAAA,QAAAD,WAAA,CAAA7D,MAAA,UAAA6D,WAAA,CAAAE,cAAA,UAAAF,WAAA,CAAAG,YAAA;QACA;;QAEA;QACA,KAAAtE,KAAA,GAAA8G,eAAA,CAAAlD,MAAA;QACA1B,OAAA,CAAAC,GAAA,2BAAAnC,KAAA;;QAEA;QACA,SAAAG,MAAA,CAAAG,MAAA;UACA;UACAwG,eAAA,CAAAE,IAAA,EAAAC,CAAA,EAAAC,CAAA,SAAAjE,IAAA,CAAAiE,CAAA,CAAA5C,YAAA,QAAArB,IAAA,CAAAgE,CAAA,CAAA3C,YAAA;QACA,gBAAAnE,MAAA,CAAAG,MAAA;UACA;UACAwG,eAAA,CAAAE,IAAA,EAAAC,CAAA,EAAAC,CAAA,SAAAjE,IAAA,CAAAiE,CAAA,CAAA7C,cAAA,QAAApB,IAAA,CAAAgE,CAAA,CAAA5C,cAAA;QACA,gBAAAlE,MAAA,CAAAG,MAAA;UACA;UACAwG,eAAA,CAAAE,IAAA,EAAAC,CAAA,EAAAC,CAAA,SAAAjE,IAAA,CAAAgE,CAAA,CAAA5C,cAAA,QAAApB,IAAA,CAAAiE,CAAA,CAAA7C,cAAA;QACA,gBAAAlE,MAAA,CAAAG,MAAA;UACA;UACAwG,eAAA,CAAAE,IAAA,EAAAC,CAAA,EAAAC,CAAA,SAAAjE,IAAA,CAAAiE,CAAA,CAAA5C,YAAA,QAAArB,IAAA,CAAAgE,CAAA,CAAA3C,YAAA;QACA;;QAEA;QACA,MAAA6C,OAAA,GAAAC,IAAA,CAAAC,IAAA,CAAAP,eAAA,CAAAlD,MAAA,QAAA1D,QAAA;;QAEA;QACA,MAAAoH,UAAA,GAAAF,IAAA,CAAAG,GAAA,CAAAX,gBAAA,EAAAO,OAAA;QACAjF,OAAA,CAAAC,GAAA,eAAA2E,eAAA,CAAAlD,MAAA,gBAAA1D,QAAA,UAAAiH,OAAA,UAAAG,UAAA;QAEA,MAAAE,UAAA,IAAAF,UAAA,aAAApH,QAAA;QACA,MAAAuH,QAAA,GAAAL,IAAA,CAAAG,GAAA,CAAAC,UAAA,QAAAtH,QAAA,EAAA4G,eAAA,CAAAlD,MAAA;QACA,MAAA8D,uBAAA,GAAAZ,eAAA,CAAAa,KAAA,CAAAH,UAAA,EAAAC,QAAA;QAEAvF,OAAA,CAAAC,GAAA,kBAAAqF,UAAA,UAAAC,QAAA,YAAAC,uBAAA,CAAA9D,MAAA;;QAEA;QACA,IAAA8D,uBAAA,CAAA9D,MAAA;UACA;UACA,KAAA7D,YAAA,GAAA2H,uBAAA;UACA;UACA,SAAAzH,WAAA,KAAAqH,UAAA;YACApF,OAAA,CAAAC,GAAA,iBAAAlC,WAAA,MAAAqH,UAAA;YACA,KAAAM,SAAA;cACA,KAAA3H,WAAA,GAAAqH,UAAA;YACA;UACA;UACApF,OAAA,CAAAC,GAAA,sBAAApC,YAAA,CAAA6D,MAAA;QACA,WAAAkD,eAAA,CAAAlD,MAAA;UACA;UACA1B,OAAA,CAAAC,GAAA;UACA,KAAApC,YAAA,GAAA+G,eAAA,CAAAa,KAAA,SAAAzH,QAAA;UACA,SAAAD,WAAA;YACA,KAAA2H,SAAA;cACA,KAAA3H,WAAA;YACA;UACA;QACA;UACA;UACA,KAAAF,YAAA;UACAmC,OAAA,CAAAC,GAAA;QACA;QAEAD,OAAA,CAAAC,GAAA,wCAAAlC,WAAA;MACA,SAAAuE,KAAA;QACAtC,OAAA,CAAAsC,KAAA,8CAAAA,KAAA;MACA;IACA;IAEA;IACAhC,UAAA;MACA,KAAAhC,UAAA;QACAL,MAAA;UAAA,QAAAA;QAAA;QACAF,WAAA,OAAAA;MACA;MACAiC,OAAA,CAAAC,GAAA,sBAAA3B,UAAA;IACA;IAEA;IACAiB,aAAA;MACA,SAAAjB,UAAA;QACA,KAAAL,MAAA;UAAA,QAAAK,UAAA,CAAAL;QAAA;QACA,KAAAF,WAAA,QAAAO,UAAA,CAAAP,WAAA;QACAiC,OAAA,CAAAC,GAAA,yBAAA3B,UAAA;QACA,KAAA6B,SAAA;MACA;QACA,KAAAA,SAAA;MACA;IACA;IAEA;IACAwF,sBAAA1D,WAAA;MACAjC,OAAA,CAAAC,GAAA,YAAAgC,WAAA;;MAEA;MACA,MAAA2B,UAAA,QAAAN,aAAA,CAAArB,WAAA;MACA,MAAA4B,UAAA,QAAAR,aAAA,CAAApB,WAAA;MACA,MAAA6B,QAAA,GAAA7B,WAAA,CAAA7D,MAAA;MACA,MAAA2F,SAAA,GAAA9B,WAAA,CAAAE,cAAA;MACA,MAAA6B,OAAA,GAAA/B,WAAA,CAAAG,YAAA;MAEApC,OAAA,CAAAC,GAAA;QACA2D,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;QACAC;MACA;;MAEA;MACA,MAAA4B,QAAA,yBAAA3D,WAAA,CAAAb,gBAAA;MACA,MAAAyE,KAAA;QACAjC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAhD,SAAA,MAAAC,IAAA,GAAAC,OAAA;MACA;MAEAhB,OAAA,CAAAC,GAAA,iBAAA4F,KAAA;MACA/F,YAAA,CAAAyE,OAAA,CAAAqB,QAAA,EAAAE,IAAA,CAAAC,SAAA,CAAAF,KAAA;;MAEA;MACA,KAAArB,OAAA,CAAAC,IAAA;QACA/G,IAAA;QACAuD,MAAA;UAAA/C,IAAA,EAAA+D,WAAA,CAAAb;QAAA;QACAgD,KAAA;UACAC,aAAA,EAAAT,UAAA;UACAU,iBAAA,EAAAT,UAAA;UACAE,SAAA,EAAAA,SAAA;UACAC,OAAA,EAAAA;QACA;MACA;IACA;IAEA;IACA,MAAA5D,wBAAA;MACA;MACA,SAAAvC,YAAA,CAAA6D,MAAA;QACA;QACA,SAAAsE,CAAA,MAAAA,CAAA,QAAAnI,YAAA,CAAA6D,MAAA,EAAAsE,CAAA;UACA,MAAA/D,WAAA,QAAApE,YAAA,CAAAmI,CAAA;UACA,MAAAJ,QAAA,yBAAA3D,WAAA,CAAAb,gBAAA;UACA,MAAA6E,aAAA,GAAAnG,YAAA,CAAAC,OAAA,CAAA6F,QAAA;UAEA,IAAAK,aAAA;YACA;cACA,MAAA3H,UAAA,GAAAwH,IAAA,CAAAI,KAAA,CAAAD,aAAA;;cAEA;cACA,MAAApB,GAAA,OAAA9D,IAAA,GAAAC,OAAA;cACA,MAAAmF,WAAA;cAEA,IAAAtB,GAAA,GAAAvG,UAAA,CAAAwC,SAAA,IAAAqF,WAAA;gBACAnG,OAAA,CAAAC,GAAA,UAAAgC,WAAA,CAAAb,gBAAA,oBAAA9C,UAAA;;gBAEA;gBACA,IAAAA,UAAA,CAAA8H,YAAA,oBACA9H,UAAA,CAAAsF,UAAA,UAAApB,EAAA,6BACAlE,UAAA,CAAAuF,UAAA;kBACA7D,OAAA,CAAAC,GAAA,OAAAgC,WAAA,CAAAb,gBAAA;;kBAEA;kBACA,KAAAvD,YAAA,CAAAmI,CAAA,EAAA5H,MAAA;;kBAEA;kBACA,KAAAiI,YAAA;gBACA;cACA;gBACA;gBACArG,OAAA,CAAAC,GAAA,OAAAgC,WAAA,CAAAb,gBAAA;gBACAtB,YAAA,CAAAI,UAAA,CAAA0F,QAAA;cACA;YACA,SAAAU,CAAA;cACAtG,OAAA,CAAAsC,KAAA,gBAAAgE,CAAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAC,iBAAA;MACA,KAAA7H,mBAAA;MACA;MACA,KAAAE,UAAA,CAAAG,cAAA,QAAAE,eAAA,CAAAuH,GAAA,CAAAC,KAAA,IAAAA,KAAA,CAAAvH,GAAA;MACA,KAAAF,eAAA;IACA;IAEA;IACA0H,sBAAAC,KAAA;MACA,IAAAA,KAAA;QACA,KAAA/H,UAAA,CAAAG,cAAA,QAAAE,eAAA,CAAAuH,GAAA,CAAAC,KAAA,IAAAA,KAAA,CAAAvH,GAAA;MACA;QACA,KAAAN,UAAA,CAAAG,cAAA;MACA;IACA;IAEA;IACA,MAAA6H,aAAA;MACA,SAAAhI,UAAA,CAAAG,cAAA,CAAA2C,MAAA;QACA,KAAAa,QAAA,CAAAsE,OAAA;QACA;MACA;MAEA,KAAAlI,aAAA;MACA;QACA;QACA,MAAAmI,UAAA;UACAC,aAAA,OAAAnI,UAAA,CAAAC,MAAA;UACAmI,YAAA,OAAApI,UAAA,CAAAE,KAAA;UACAmI,eAAA,OAAArI,UAAA,CAAAG;QACA;;QAEA;QACA,SAAAH,UAAA,CAAAE,KAAA;UACAgI,UAAA,CAAA1F,gBAAA,QAAAnD,MAAA,CAAAC,IAAA,IAAAmD,SAAA;UACAyF,UAAA,CAAAxF,SAAA,QAAArD,MAAA,CAAAE,QAAA,IAAAkD,SAAA;UACAyF,UAAA,CAAA1I,MAAA,QAAAH,MAAA,CAAAG,MAAA,IAAAiD,SAAA;UAEA,SAAApD,MAAA,CAAAI,SAAA,SAAAJ,MAAA,CAAAI,SAAA,CAAAqD,MAAA;YACAoF,UAAA,CAAAnF,SAAA,QAAA1D,MAAA,CAAAI,SAAA;YACAyI,UAAA,CAAAlF,OAAA,QAAA3D,MAAA,CAAAI,SAAA;UACA;QACA;UACA;UACAyI,UAAA,CAAAI,YAAA,QAAArJ,YAAA,CAAA2I,GAAA,CAAAvE,WAAA;YACAC,EAAA,EAAAD,WAAA,CAAAC,EAAA;YACAgC,kBAAA,EAAAjC,WAAA,CAAAiC,kBAAA;YACA9C,gBAAA,EAAAa,WAAA,CAAAb,gBAAA;YACA+F,cAAA,EAAAlF,WAAA,CAAAkF,cAAA;YACAC,kBAAA,EAAAnF,WAAA,CAAAmF,kBAAA;YACAC,kBAAA,EAAApF,WAAA,CAAAoF,kBAAA;YACA/F,SAAA,EAAAW,WAAA,CAAAX,SAAA;YACAgG,eAAA,EAAArF,WAAA,CAAAqF,eAAA;YACAC,YAAA,EAAAtF,WAAA,CAAAsF,YAAA;YACAC,UAAA,EAAAvF,WAAA,CAAAuF,UAAA;YACArF,cAAA,EAAAF,WAAA,CAAAE,cAAA;YACAC,YAAA,EAAAH,WAAA,CAAAG,YAAA;YACAqF,OAAA,EAAAxF,WAAA,CAAAwF,OAAA;YACArJ,MAAA,EAAA6D,WAAA,CAAA7D,MAAA;YACAsJ,UAAA,EAAAzF,WAAA,CAAAyF;UACA;QACA;QAEA1H,OAAA,CAAAC,GAAA,YAAA6G,UAAA;;QAEA;QACA,MAAAjF,QAAA,SAAArE,cAAA,CAAAmK,kBAAA,CAAAb,UAAA;;QAEA;QACA,MAAAc,IAAA,OAAAC,IAAA,EAAAhG,QAAA,CAAAlE,IAAA;UACAmK,IAAA,OAAAlJ,UAAA,CAAAC,MAAA;QACA;QAEA,MAAAsF,GAAA,GAAA3F,MAAA,CAAAuJ,GAAA,CAAAC,eAAA,CAAAJ,IAAA;QACA,MAAAK,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACAF,IAAA,CAAAG,IAAA,GAAAjE,GAAA;;QAEA;QACA,MAAArD,SAAA,OAAAC,IAAA,GAAAsH,WAAA,GAAA5C,KAAA,QAAA6C,OAAA;QACA,MAAAC,SAAA,QAAA3J,UAAA,CAAAC,MAAA;QACAoJ,IAAA,CAAAO,QAAA,WAAA1H,SAAA,IAAAyH,SAAA;;QAEA;QACAL,QAAA,CAAAO,IAAA,CAAAC,WAAA,CAAAT,IAAA;QACAA,IAAA,CAAAU,KAAA;QACAT,QAAA,CAAAO,IAAA,CAAAG,WAAA,CAAAX,IAAA;QACAzJ,MAAA,CAAAuJ,GAAA,CAAAc,eAAA,CAAA1E,GAAA;QAEA,KAAA5B,QAAA,CAAAuG,OAAA;QACA,KAAApK,mBAAA;MAEA,SAAA4D,KAAA;QACAtC,OAAA,CAAAsC,KAAA,UAAAA,KAAA;QACA,KAAAC,QAAA,CAAAD,KAAA,aAAAA,KAAA,CAAAT,QAAA,EAAAlE,IAAA,EAAAoL,MAAA,IAAAzG,KAAA,CAAA0G,OAAA;MACA;QACA,KAAArK,aAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}