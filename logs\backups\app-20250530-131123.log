2025-05-30 00:00:30,437 - root - INFO - 执行预约状态更新任务
2025-05-30 00:00:30,439 - backend.utils.status_updater - INFO - 当前时间: 2025-05-30 00:00:30.439620
2025-05-30 00:00:30,439 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=30, 时=0, 分=0, 秒=30
2025-05-30 00:00:30,441 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-30 00:00:30,441 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-30 00:00:30.439620 AND Reservation.end_datetime > 2025-05-30 00:00:30.439620
2025-05-30 00:00:30,442 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-30 00:00:30,442 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-30 00:00:30,442 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-30 00:00:30.439620
2025-05-30 00:00:30,443 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-30 00:00:30,443 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-30 00:00:30,444 - backend.utils.status_updater - INFO - 没有预约需要更新状态
