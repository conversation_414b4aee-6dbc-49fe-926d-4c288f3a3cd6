<template>
  <div class="admin-reservation">
    <h2>预定管理</h2>

    <!-- 筛选卡片 -->
    <el-card shadow="hover" class="filter-card">
      <el-form :inline="true" :model="filter" class="filter-form">
        <el-form-item :label="$t('reservation.code')">
          <el-input
            v-model="filter.code"
            :placeholder="$t('reservation.queryPlaceholder')"
            clearable
            @keyup.enter.native="handleFilterChange"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('reservation.userName')">
          <el-input
            v-model="filter.userName"
            :placeholder="$t('reservation.userName')"
            clearable
            @keyup.enter.native="handleFilterChange"
          ></el-input>
        </el-form-item>

        <el-form-item :label="$t('reservation.status')">
          <el-select
            v-model="filter.status"
            :placeholder="$t('equipment.allStatus')"
            clearable
            @change="handleFilterChange"
          >
            <el-option
              :label="$t('reservation.confirmed')"
              value="confirmed"
            ></el-option>
            <el-option
              :label="$t('reservation.inUse')"
              value="in_use"
            ></el-option>
            <el-option
              :label="$t('reservation.expired')"
              value="expired"
            ></el-option>
            <el-option
              :label="$t('reservation.cancelled')"
              value="cancelled"
            ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item :label="$t('reservation.dateRange')">
          <el-date-picker
            v-model="filter.dateRange"
            type="daterange"
            range-separator="至"
            :start-placeholder="$t('reservation.startDate')"
            :end-placeholder="$t('reservation.endDate')"
            value-format="yyyy-MM-dd"
            @change="handleFilterChange"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleFilterChange">
            {{ $t('common.search') }}
          </el-button>
          <el-button @click="resetFilter" icon="el-icon-refresh-left">
            {{ $t('common.reset') }}
          </el-button>
          <el-button type="success" icon="el-icon-download" @click="showExportDialog">
            导出数据
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 预定列表 -->
    <el-card shadow="hover" class="reservation-list">
      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="10" animated />
      </div>

      <div v-else-if="reservations.length === 0" class="empty-data">
        <el-empty :description="$t('common.noData')"></el-empty>
      </div>

      <!-- 移动端卡片视图 -->
      <div v-if="isMobile" class="mobile-card-container">
        <div
          v-for="reservation in reservations"
          :key="reservation.id"
          class="reservation-mobile-card"
        >
          <div class="card-header">
            <div class="reservation-info">
              <div class="reservation-id">ID: {{ reservation.id }}</div>
              <div class="reservation-number">{{ reservation.reservation_number || '-' }}</div>
            </div>
            <el-tag
              :type="getStatusType(reservation)"
              size="medium"
              class="status-tag"
            >
              {{ getStatusText(reservation) }}
            </el-tag>
          </div>

          <div class="card-content">
            <div class="info-row">
              <span class="label">预约码:</span>
              <span class="value reservation-code">{{ reservation.reservation_code }}</span>
            </div>
            <div class="info-row">
              <span class="label">设备:</span>
              <span class="value">{{ reservation.equipment_name }}</span>
            </div>
            <div class="info-row">
              <span class="label">用户:</span>
              <span class="value">{{ reservation.user_name }} ({{ reservation.user_department }})</span>
            </div>
            <div class="info-row">
              <span class="label">联系方式:</span>
              <span class="value">{{ reservation.user_contact }}</span>
            </div>
            <div class="info-row">
              <span class="label">开始时间:</span>
              <span class="value">{{ formatDateTime(null, null, reservation.start_datetime) }}</span>
            </div>
            <div class="info-row">
              <span class="label">结束时间:</span>
              <span class="value">{{ formatDateTime(null, null, reservation.end_datetime) }}</span>
            </div>
          </div>

          <div class="card-actions">
            <el-button
              type="primary"
              size="small"
              @click="viewReservation(reservation)"
            >
              查看详情
            </el-button>
          </div>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <el-table
        v-else
        :data="reservations"
        style="width: 100%"
        :default-sort="{ prop: 'id', order: 'descending' }"
        header-align="center"
        cell-class-name="text-center"
        border
        stripe
      >
        <!-- 添加ID列 -->
        <el-table-column
          prop="id"
          :label="$t('common.id')"
          min-width="60"
          sortable
        >
          <template slot-scope="scope">
            <span style="font-weight: bold;">{{ scope.row.id }}</span>
          </template>
        </el-table-column>

        <!-- 添加预约序号列 -->
        <el-table-column
          prop="reservation_number"
          :label="$t('reservation.number')"
          min-width="180"
        >
          <template slot-scope="scope">
            <span style="font-weight: bold;">{{ scope.row.reservation_number || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="reservation_code"
          :label="$t('reservation.code')"
          min-width="100"
        >
          <template slot-scope="scope">
            <span style="color: #F56C6C; font-weight: bold;">{{ scope.row.reservation_code }}</span>
          </template>
        </el-table-column>

        <el-table-column
          prop="equipment_name"
          :label="$t('reservation.equipmentName')"
          min-width="120"
        ></el-table-column>

        <el-table-column
          prop="user_name"
          :label="$t('reservation.userName')"
          min-width="100"
        ></el-table-column>

        <el-table-column
          prop="user_department"
          :label="$t('reservation.userDepartment')"
          min-width="100"
        ></el-table-column>

        <el-table-column
          prop="user_contact"
          :label="$t('reservation.userContact')"
          min-width="120"
        ></el-table-column>

        <el-table-column
          prop="start_datetime"
          :label="$t('reservation.startTime')"
          min-width="150"
          :formatter="formatDateTime"
        ></el-table-column>

        <el-table-column
          prop="end_datetime"
          :label="$t('reservation.endTime')"
          min-width="150"
          :formatter="formatDateTime"
        ></el-table-column>

        <el-table-column
          prop="status"
          :label="$t('reservation.status')"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-tag
              :type="getStatusType(scope.row)"
              size="medium"
              style="font-weight: bold; padding: 0px 10px; font-size: 14px;"
            >
              {{ getStatusText(scope.row) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('common.operation')"
          min-width="100"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="viewReservation(scope.row)"
            >
              {{ $t('admin.viewReservation') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container" v-if="reservations.length > 0">
        <el-pagination
          background
          :layout="paginationLayout"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 导出对话框 -->
    <el-dialog
      title="导出预约数据"
      :visible.sync="exportDialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form :model="exportForm" label-width="120px">
        <!-- 导出格式 -->
        <el-form-item label="导出格式">
          <div style="padding: 8px 0;">
            <i class="el-icon-document"></i>
            <span style="margin-left: 8px;">CSV格式 (.csv)</span>
            <div style="font-size: 12px; color: #999; margin-top: 4px; margin-left: 24px;">
              支持Excel打开，包含完整的中文字段名
            </div>
          </div>
        </el-form-item>

        <!-- 导出范围 -->
        <el-form-item label="导出范围">
          <el-radio-group v-model="exportForm.scope">
            <el-radio label="current">当前页面数据 ({{ reservations.length }} 条)</el-radio>
            <el-radio label="all">全部筛选结果 ({{ total }} 条)</el-radio>
          </el-radio-group>
        </el-form-item>

        <!-- 选择字段 -->
        <el-form-item label="导出字段">
          <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; padding: 10px;">
            <el-checkbox
              v-model="selectAllFields"
              @change="handleSelectAllFields"
              style="margin-bottom: 10px; font-weight: bold;"
            >
              全选
            </el-checkbox>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 8px;">
              <el-checkbox
                v-for="field in availableFields"
                :key="field.key"
                v-model="exportForm.selectedFields"
                :label="field.key"
              >
                {{ field.label }}
              </el-checkbox>
            </div>
          </div>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exportLoading">
          {{ exportLoading ? '导出中...' : '确认导出' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { reservationApi } from '@/api'
import { isReservationExpired } from '@/utils/date'

export default {
  name: 'AdminReservation',

  data() {
    return {
      loading: false,
      reservations: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      filter: {
        code: '',
        userName: '',
        status: '',
        dateRange: []
      },
      // 添加一个保存页面状态的变量
      savedState: null,
      // 响应式布局相关
      isMobile: window.innerWidth <= 768,
      // 导出相关
      exportDialogVisible: false,
      exportLoading: false,
      exportForm: {
        format: 'csv',
        scope: 'current',
        selectedFields: []
      },
      selectAllFields: true,
      availableFields: [
        { key: 'id', label: 'ID' },
        { key: 'reservation_number', label: '预约编号' },
        { key: 'reservation_code', label: '预约码' },
        { key: 'equipment_name', label: '设备名称' },
        { key: 'equipment_category', label: '设备类别' },
        { key: 'equipment_location', label: '设备位置' },
        { key: 'user_name', label: '预约人姓名' },
        { key: 'user_department', label: '预约人部门' },
        { key: 'user_contact', label: '联系方式' },
        { key: 'user_email', label: '邮箱地址' },
        { key: 'start_datetime', label: '开始时间' },
        { key: 'end_datetime', label: '结束时间' },
        { key: 'purpose', label: '使用目的' },
        { key: 'status', label: '预约状态' },
        { key: 'created_at', label: '创建时间' }
      ]
    }
  },

  computed: {
    // 根据屏幕宽度动态调整分页组件布局
    paginationLayout() {
      return this.isMobile
        ? 'prev, next'
        : 'prev, pager, next';
    }
  },

  created() {
    // 检查是否有保存的状态并恢复它
    this.restoreState();

    // 添加窗口大小变化的监听器
    window.addEventListener('resize', this.handleResize);
  },

  beforeDestroy() {
    // 移除窗口大小变化的监听器
    window.removeEventListener('resize', this.handleResize);
  },

  // 添加activated钩子函数，在组件被激活时调用（如从预定详情页面返回）
  activated() {
    // 检查是否需要强制刷新
    const forceRefresh = localStorage.getItem('force_refresh_reservation_list');
    if (forceRefresh === 'true') {
      console.log('检测到强制刷新标记，重新获取数据');
      localStorage.removeItem('force_refresh_reservation_list');
      this.fetchData();
      return;
    }

    // 当从其他页面返回时，尝试恢复状态
    this.restoreState();
    // 检查预约状态更新
    this.checkReservationUpdates();
  },

  // 添加deactivated钩子函数，在组件被停用时调用（如进入预定详情页面）
  deactivated() {
    // 保存当前页面状态
    this.saveState();
  },

  // 添加beforeRouteLeave导航守卫，在离开组件时调用
  beforeRouteLeave(to, from, next) {
    // 如果是跳转到预定详情页面，保存状态
    if (to.path.includes('/admin/reservation/') && to.path !== '/admin/reservation') {
      this.saveState();
    }
    next();
  },

  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.isMobile = window.innerWidth <= 768;
    },

    async fetchData() {
      this.loading = true
      console.log('Fetching data with filter:', this.filter);
      console.log('Current page:', this.currentPage);

      try {
        // 添加时间戳参数，确保每次都获取最新数据
        const timestamp = new Date().getTime()
        console.log('添加时间戳参数:', timestamp)

        const params = {
          skip: (this.currentPage - 1) * this.pageSize, // 将页码转换为skip参数
          limit: this.pageSize,
          reservation_code: this.filter.code || undefined,
          user_name: this.filter.userName || undefined,
          _t: timestamp, // 添加时间戳，防止缓存
          sort_by: 'id', // 按ID排序
          sort_order: 'desc' // 降序排序
        }

        // 添加日期范围过滤
        if (this.filter.dateRange && this.filter.dateRange.length === 2) {
          params.from_date = this.filter.dateRange[0]
          params.to_date = this.filter.dateRange[1]
        }

        // 处理不同的状态筛选
        if (this.filter.status) {
          console.log('Filtering by status:', this.filter.status);

          // 直接使用选择的状态值，因为后端现在支持所有状态
          params.status = this.filter.status;
          console.log(`Setting status parameter to "${this.filter.status}"`);
        }

        console.log('Fetching reservations with params:', params)
        const response = await reservationApi.getReservations(params)
        console.log('API Response:', response)
        let reservations = response.data.items || []
        console.log('Received reservations:', reservations)

        // 不再需要在前端进行筛选，因为后端已经返回了正确的状态
        // 只记录日志，帮助调试
        if (this.filter.status) {
          console.log(`获取到状态为 ${this.filter.status} 的预约数量: ${reservations.length}`);

          // 记录每个预约的状态，帮助调试
          reservations.forEach(reservation => {
            console.log(`预约ID=${reservation.id}, 状态=${reservation.status}, 开始时间=${reservation.start_datetime}, 结束时间=${reservation.end_datetime}`);
          });
        }

        console.log('Filtered reservations:', reservations)
        this.reservations = reservations

        // 如果是特殊状态，总数需要重新计算
        if (this.filter.status === 'in_use' || this.filter.status === 'expired' || this.filter.status === 'confirmed' || this.filter.status === 'cancelled') {
          // 对于特殊状态，我们需要获取所有页的数据来计算总数
          // 这里我们先使用当前页的数据计算一个临时总数
          this.total = reservations.length
          console.log(`Temporary total based on current page: ${this.total}`);

          // 无论当前页是否有数据，都获取所有数据来计算真实总数
          this.fetchTotalForSpecialStatus()
        } else {
          this.total = response.data.total
          console.log(`Total from API response: ${this.total}`);
        }
      } catch (error) {
        console.error('Failed to fetch reservations:', error)
        this.$message.error(this.$t('error.serverError'))
      } finally {
        this.loading = false
      }
    },

    formatDateTime(_row, _column, cellValue) {
      if (!cellValue) return ''

      const date = new Date(cellValue)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },

    getStatusType(reservation) {
      // 直接根据后端返回的状态返回对应的类型
      switch (reservation.status) {
        case 'cancelled':
          return 'danger';  // 已取消 - 红色
        case 'expired':
          return 'warning'; // 已过期 - 橙色
        case 'in_use':
          return 'primary'; // 使用中 - 蓝色
        case 'confirmed':
          return 'success'; // 已确认 - 绿色
        default:
          return 'info';    // 其他状态 - 灰色
      }
    },

    getStatusText(reservation) {
      // 直接根据后端返回的状态返回对应的文本
      switch (reservation.status) {
        case 'cancelled':
          return this.$t('reservation.statusCancelled'); // 已取消
        case 'expired':
          return this.$t('reservation.statusExpired');   // 已过期
        case 'in_use':
          return this.$t('reservation.statusInUse');     // 使用中
        case 'confirmed':
          return this.$t('reservation.statusConfirmed'); // 已确认
        default:
          return reservation.status; // 其他状态直接显示
      }
    },

    handleFilterChange() {
      this.currentPage = 1
      this.fetchData()
    },

    resetFilter() {
      this.filter = {
        code: '',
        userName: '',
        status: '',
        dateRange: []
      }
      this.handleFilterChange()
    },

    handlePageChange(page) {
      this.currentPage = page
      this.fetchData()
    },

    viewReservation(reservation) {
      // 计算当前预约的实际状态文本和类型
      const statusText = this.getStatusText(reservation)
      const statusType = this.getStatusType(reservation)

      console.log('计算的状态信息:', {
        statusText,
        statusType,
        dbStatus: reservation.status,
        startTime: reservation.start_datetime,
        endTime: reservation.end_datetime,
        reservationNumber: reservation.reservation_number
      })

      // 构建URL，添加预约码、时间参数、预约序号和计算好的状态信息
      // 使用预约序号作为路径参数，而不是预约码
      const url = {
        path: `/admin/reservation/${reservation.reservation_code}`,
        query: {
          startTime: reservation.start_datetime,
          endTime: reservation.end_datetime,
          displayStatus: statusText,
          displayStatusType: statusType,
          reservationNumber: reservation.reservation_number // 添加预约序号参数
        }
      }

      // 每次查看预约时，都重新设置一个标记，表示需要显示预约序号通知
      localStorage.setItem('show_reservation_number_notification', 'true')

      // 清除之前的预约序号，确保每次都使用新的预约序号
      localStorage.removeItem('current_reservation_number')

      // 将预约序号保存到localStorage，以便在页面刷新后仍然可以使用
      if (reservation.reservation_number) {
        localStorage.setItem('current_reservation_number', reservation.reservation_number)
        console.log('保存预约序号到localStorage:', reservation.reservation_number)

        // 强制使用预约序号查询，而不是预约码
        localStorage.setItem('force_use_reservation_number', 'true')
      }

      this.$router.push(url)
    },

    // 获取特殊状态的总记录数并更新当前页面的预约列表
    async fetchTotalForSpecialStatus() {
      try {
        console.log('Fetching total for special status:', this.filter.status);
        console.log('Current page before fetchTotal:', this.currentPage);

        // 保存当前页码，以便后续恢复
        const savedCurrentPage = this.currentPage;

        // 添加时间戳参数，确保每次都获取最新数据
        const timestamp = new Date().getTime()
        console.log('添加时间戳参数:', timestamp)

        // 构建查询参数，不包含分页参数
        const params = {
          // 不设置limit，获取所有记录
          limit: 1000, // 设置一个较大的值以获取尽可能多的记录
          skip: 0,
          reservation_code: this.filter.code || undefined, // 使用reservation_code而不是code
          user_name: this.filter.userName || undefined,
          _t: timestamp, // 添加时间戳，防止缓存
          sort_by: 'id', // 按ID排序
          sort_order: 'desc' // 降序排序
        }

        // 添加日期范围过滤
        if (this.filter.dateRange && this.filter.dateRange.length === 2) {
          params.from_date = this.filter.dateRange[0]
          params.to_date = this.filter.dateRange[1]
        }

        // 直接获取指定状态的预约
        const statusParams = { ...params, status: this.filter.status };

        console.log(`直接获取状态为 ${this.filter.status} 的预约`);
        const response = await reservationApi.getReservations(statusParams);

        // 使用后端返回的结果
        let allReservations = response.data.items || [];

        console.log(`Total reservations before filtering: ${allReservations.length}`);

        const now = new Date();
        console.log(`当前日期: ${now}`);

        // 不再需要在前端进行筛选，因为后端已经返回了正确的状态
        // 只记录日志，帮助调试
        console.log(`获取到状态为 ${this.filter.status} 的预约数量: ${allReservations.length}`);

        // 记录每个预约的状态，帮助调试
        allReservations.forEach(reservation => {
          console.log(`预约ID=${reservation.id}, 状态=${reservation.status}, 开始时间=${reservation.start_datetime}, 结束时间=${reservation.end_datetime}`);
        });

        // 更新总数
        this.total = allReservations.length;
        console.log(`Updated total to: ${this.total}`);

        // 对筛选后的结果进行排序
        if (this.filter.status === 'expired') {
          // 对于已过期，按结束时间倒序排列
          allReservations.sort((a, b) => new Date(b.end_datetime) - new Date(a.end_datetime));
        } else if (this.filter.status === 'in_use') {
          // 对于使用中，按开始时间倒序排列
          allReservations.sort((a, b) => new Date(b.start_datetime) - new Date(a.start_datetime));
        } else if (this.filter.status === 'confirmed') {
          // 对于已确认，按开始时间升序排列
          allReservations.sort((a, b) => new Date(a.start_datetime) - new Date(b.start_datetime));
        } else if (this.filter.status === 'cancelled') {
          // 对于已取消，按结束时间倒序排列
          allReservations.sort((a, b) => new Date(b.end_datetime) - new Date(a.end_datetime));
        }

        // 计算当前页应该显示的预约
        const maxPage = Math.ceil(allReservations.length / this.pageSize) || 1;

        // 确保页码不超过最大页数
        const targetPage = Math.min(savedCurrentPage, maxPage);
        console.log(`计算页数: 总记录数=${allReservations.length}, 每页记录数=${this.pageSize}, 最大页数=${maxPage}, 目标页码=${targetPage}`);

        const startIndex = (targetPage - 1) * this.pageSize;
        const endIndex = Math.min(startIndex + this.pageSize, allReservations.length);
        const currentPageReservations = allReservations.slice(startIndex, endIndex);

        console.log(`当前页数据范围: 开始索引=${startIndex}, 结束索引=${endIndex}, 当前页记录数=${currentPageReservations.length}`);

        // 更新当前页面的预约列表
        if (currentPageReservations.length > 0) {
          // 先更新数据
          this.reservations = currentPageReservations;
          // 然后更新页码，避免触发不必要的重新获取数据
          if (this.currentPage !== targetPage) {
            console.log(`更新页码: 从 ${this.currentPage} 到 ${targetPage}`);
            this.$nextTick(() => {
              this.currentPage = targetPage;
            });
          }
          console.log(`更新当前页面的预约列表: ${this.reservations.length} 条记录`);
        } else if (allReservations.length > 0) {
          // 如果当前页没有数据但总数据不为空，自动回到第一页
          console.log(`当前页没有数据，回到第一页`);
          this.reservations = allReservations.slice(0, this.pageSize);
          if (this.currentPage !== 1) {
            this.$nextTick(() => {
              this.currentPage = 1;
            });
          }
        } else {
          // 如果没有找到任何预约
          this.reservations = [];
          console.log('没有找到符合条件的预约');
        }

        console.log('Current page after fetchTotal:', this.currentPage);
      } catch (error) {
        console.error('Failed to fetch total for special status:', error);
      }
    },

    // 保存当前页面状态
    saveState() {
      this.savedState = {
        filter: { ...this.filter },
        currentPage: this.currentPage
      };
      console.log('Saved state:', this.savedState);
    },

    // 恢复保存的页面状态
    restoreState() {
      if (this.savedState) {
        this.filter = { ...this.savedState.filter };
        this.currentPage = this.savedState.currentPage;
        console.log('Restored state:', this.savedState);
        this.fetchData();
      } else {
        this.fetchData();
      }
    },

    // 打开预约详情
    openReservationDetail(reservation) {
      console.log('打开预约详情:', reservation);

      // 计算当前状态
      const statusText = this.getStatusText(reservation);
      const statusType = this.getStatusType(reservation);
      const dbStatus = reservation.status || 'confirmed';
      const startTime = reservation.start_datetime;
      const endTime = reservation.end_datetime;

      console.log('计算的状态信息:', {
        statusText,
        statusType,
        dbStatus,
        startTime,
        endTime
      });

      // 将状态保存到localStorage，以便详情页面使用
      const stateKey = `reservation_status_${reservation.reservation_code}`;
      const state = {
        statusText,
        statusType,
        dbStatus,
        timestamp: new Date().getTime()
      };

      console.log('Saved state:', state);
      localStorage.setItem(stateKey, JSON.stringify(state));

      // 导航到详情页面，并传递状态和时间参数
      this.$router.push({
        name: 'AdminReservationDetail',
        params: { code: reservation.reservation_code },
        query: {
          displayStatus: statusText,
          displayStatusType: statusType,
          startTime: startTime,
          endTime: endTime
        }
      });
    },

    // 在激活（从其他页面返回）时，检查预约状态是否需要更新
    async checkReservationUpdates() {
      // 如果当前显示的是预约列表，则检查是否需要刷新
      if (this.reservations.length > 0) {
        // 检查localStorage中是否有任何预约状态发生了变化
        for (let i = 0; i < this.reservations.length; i++) {
          const reservation = this.reservations[i];
          const stateKey = `reservation_status_${reservation.reservation_code}`;
          const savedStateStr = localStorage.getItem(stateKey);

          if (savedStateStr) {
            try {
              const savedState = JSON.parse(savedStateStr);

              // 检查保存的状态是否还是新鲜的（5分钟内）
              const now = new Date().getTime();
              const fiveMinutes = 5 * 60 * 1000;

              if (now - savedState.timestamp <= fiveMinutes) {
                console.log(`检测到预约 ${reservation.reservation_code} 的状态可能已更改，保存的状态:`, savedState);

                // 检查是否有强制状态更新，特别是已取消状态
                if (savedState.forcedStatus === 'cancelled' ||
                    (savedState.statusText === this.$t('reservation.cancelled') &&
                     savedState.statusType === 'danger')) {
                  console.log(`预约 ${reservation.reservation_code} 已被标记为已取消，将在界面上更新`);

                  // 更新当前列表中的预约状态
                  this.reservations[i].status = 'cancelled';

                  // 强制更新UI
                  this.$forceUpdate();
                }
              } else {
                // 如果状态过期，则移除它
                console.log(`预约 ${reservation.reservation_code} 的保存状态已过期，移除`);
                localStorage.removeItem(stateKey);
              }
            } catch (e) {
              console.error('解析保存的状态时出错:', e);
            }
          }
        }
      }
    },

    // 显示导出对话框
    showExportDialog() {
      this.exportDialogVisible = true
      // 默认选择所有字段
      this.exportForm.selectedFields = this.availableFields.map(field => field.key)
      this.selectAllFields = true
    },

    // 处理全选字段
    handleSelectAllFields(value) {
      if (value) {
        this.exportForm.selectedFields = this.availableFields.map(field => field.key)
      } else {
        this.exportForm.selectedFields = []
      }
    },

    // 处理导出
    async handleExport() {
      if (this.exportForm.selectedFields.length === 0) {
        this.$message.warning('请至少选择一个导出字段')
        return
      }

      this.exportLoading = true
      try {
        // 构建导出请求数据
        const exportData = {
          export_format: this.exportForm.format,
          export_scope: this.exportForm.scope,
          selected_fields: this.exportForm.selectedFields
        }

        // 如果是导出全部筛选结果，添加筛选条件
        if (this.exportForm.scope === 'all') {
          exportData.reservation_code = this.filter.code || undefined
          exportData.user_name = this.filter.userName || undefined
          exportData.status = this.filter.status || undefined

          if (this.filter.dateRange && this.filter.dateRange.length === 2) {
            exportData.from_date = this.filter.dateRange[0]
            exportData.to_date = this.filter.dateRange[1]
          }
        } else {
          // 导出当前页面数据
          exportData.current_data = this.reservations.map(reservation => ({
            id: reservation.id,
            reservation_number: reservation.reservation_number,
            reservation_code: reservation.reservation_code,
            equipment_name: reservation.equipment_name,
            equipment_category: reservation.equipment_category,
            equipment_location: reservation.equipment_location,
            user_name: reservation.user_name,
            user_department: reservation.user_department,
            user_contact: reservation.user_contact,
            user_email: reservation.user_email,
            start_datetime: reservation.start_datetime,
            end_datetime: reservation.end_datetime,
            purpose: reservation.purpose,
            status: reservation.status,
            created_at: reservation.created_at
          }))
        }

        console.log('导出请求数据:', exportData)

        // 调用导出API
        const response = await reservationApi.exportReservations(exportData)

        // 创建下载链接
        const blob = new Blob([response.data], {
          type: this.exportForm.format === 'csv' ? 'text/csv' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 设置文件名
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
        const extension = this.exportForm.format === 'csv' ? 'csv' : 'xlsx'
        link.download = `预约数据_${timestamp}.${extension}`

        // 触发下载
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
        this.exportDialogVisible = false

      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + (error.response?.data?.detail || error.message))
      } finally {
        this.exportLoading = false
      }
    }
  }
}
</script>

<style scoped>
.admin-reservation {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #FFFFFF;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.page-title {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.filter-card {
  margin-bottom: 20px;
}

.reservation-list {
  margin-bottom: 20px;
}

.loading-container {
  padding: 40px 0;
}

.empty-data {
  padding: 40px 0;
  text-align: center;
}

.pagination-container {
  text-align: center;
  margin-top: 20px;
}

.text-center {
  text-align: center !important;
}

/* 移动端卡片样式 */
.mobile-card-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.reservation-mobile-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  border: 1px solid #e8e8e8;
  min-height: 200px;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.reservation-info {
  flex: 1;
}

.reservation-id {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.reservation-number {
  font-weight: bold;
  font-size: 14px;
  color: #333;
}

.status-tag {
  margin-left: 10px;
}

.card-content {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.info-row .label {
  min-width: 70px;
  font-size: 13px;
  color: #666;
  margin-right: 10px;
  flex-shrink: 0;
}

.info-row .value {
  font-size: 13px;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.info-row .reservation-code {
  color: #F56C6C;
  font-weight: bold;
}

.card-actions {
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .admin-reservation {
    padding-top: 150px !important;
    padding-bottom: 100px !important;
  }

  .admin-reservation h2 {
    margin-top: 80px !important;
  }

  .filter-form .el-form-item {
    margin-right: 0;
    margin-bottom: 10px;
  }

  .reservation-list {
    margin-top: 15px;
  }
}
</style>
