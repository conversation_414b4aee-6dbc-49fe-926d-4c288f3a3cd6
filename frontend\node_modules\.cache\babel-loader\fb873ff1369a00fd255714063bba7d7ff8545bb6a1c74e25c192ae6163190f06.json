{"ast": null, "code": "import { reservationApi, equipmentApi } from '@/api';\nimport { isReservationExpired } from '@/utils/date';\nimport axios from 'axios';\nexport default {\n  name: 'AdminDashboard',\n  data() {\n    return {\n      loading: false,\n      isMobile: false,\n      stats: {\n        totalEquipment: 0,\n        availableEquipment: 0,\n        totalReservation: 0,\n        activeReservation: 0,\n        inUseReservation: 0,\n        confirmedReservation: 0,\n        expiredReservation: 0,\n        cancelledReservation: 0\n      },\n      recentReservations: []\n    };\n  },\n  created() {\n    this.checkMobile();\n    this.fetchData();\n  },\n  mounted() {\n    window.addEventListener('resize', this.checkMobile);\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.checkMobile);\n  },\n  methods: {\n    checkMobile() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    async fetchData() {\n      this.loading = true;\n      try {\n        // 检查用户是否已登录\n        const token = localStorage.getItem('token');\n        if (!token) {\n          console.log('用户未登录，跳过仪表盘数据获取');\n          this.loading = false;\n          return;\n        }\n        try {\n          // 使用统计API获取仪表盘数据\n          const dashboardResponse = await axios.get('/api/statistics/dashboard');\n          const dashboardData = dashboardResponse.data;\n          this.stats.totalEquipment = dashboardData.total_equipment;\n          this.stats.availableEquipment = dashboardData.available_equipment;\n          this.stats.totalReservation = dashboardData.total_reservation;\n          this.stats.activeReservation = dashboardData.active_reservation;\n\n          // 处理最近预定数据\n          if (dashboardData.recent_reservations && dashboardData.recent_reservations.length > 0) {\n            this.recentReservations = dashboardData.recent_reservations.map(reservation => ({\n              id: reservation.id,\n              reservation_number: reservation.reservation_number,\n              // 添加预约序号字段\n              reservation_code: reservation.reservation_code,\n              equipment_id: reservation.equipment_id,\n              equipment_name: reservation.equipment_name,\n              user_name: reservation.user_name,\n              user_department: reservation.user_department || '',\n              user_contact: reservation.user_contact || '',\n              start_datetime: reservation.start_datetime,\n              end_datetime: reservation.end_datetime,\n              status: reservation.status,\n              created_at: reservation.created_at\n            }));\n          }\n\n          // 直接从预约管理表获取所有预约数据并计算统计数字\n          try {\n            // 获取预约管理表中的所有预约数据（可能需要分页）\n            const allReservationsResponse = await reservationApi.getReservations({\n              limit: 1000,\n              // 获取尽可能多的预约数据\n              page: 1\n            });\n            if (allReservationsResponse.data && allReservationsResponse.data.items) {\n              const now = new Date();\n              let inUseCount = 0;\n              let confirmedCount = 0;\n              let expiredCount = 0;\n              let cancelledCount = 0;\n\n              // 遍历所有预约并计算状态\n              allReservationsResponse.data.items.forEach(reservation => {\n                const start = new Date(reservation.start_datetime);\n                const end = new Date(reservation.end_datetime);\n                if (reservation.status === 'cancelled') {\n                  cancelledCount++;\n                } else if (now > end) {\n                  expiredCount++;\n                } else if (now >= start && now <= end) {\n                  inUseCount++;\n                } else if (now < start) {\n                  confirmedCount++;\n                }\n              });\n\n              // 更新统计数据\n              this.stats.inUseReservation = inUseCount;\n              this.stats.confirmedReservation = confirmedCount;\n              this.stats.expiredReservation = expiredCount;\n              this.stats.cancelledReservation = cancelledCount;\n            }\n          } catch (error) {\n            // 静默处理此错误，使用备选方案\n            if (error._silenced) {\n              console.log('预约数据获取被静默处理');\n            } else {\n              console.log('获取预约状态统计失败，使用备选数据');\n            }\n\n            // 出错时，尝试使用近期预约数据（最后的备选）\n            if (dashboardData.recent_reservations && dashboardData.recent_reservations.length > 0) {\n              const now = new Date();\n              let inUseCount = 0;\n              let confirmedCount = 0;\n              let expiredCount = 0;\n              let cancelledCount = 0;\n              dashboardData.recent_reservations.forEach(r => {\n                const start = new Date(r.start_datetime);\n                const end = new Date(r.end_datetime);\n                if (r.status === 'cancelled') {\n                  cancelledCount++;\n                } else if (now > end) {\n                  expiredCount++;\n                } else if (now >= start && now <= end) {\n                  inUseCount++;\n                } else {\n                  confirmedCount++;\n                }\n              });\n              this.stats.inUseReservation = inUseCount;\n              this.stats.confirmedReservation = confirmedCount;\n              this.stats.expiredReservation = expiredCount;\n              this.stats.cancelledReservation = cancelledCount;\n            }\n          }\n        } catch (error) {\n          // 检查是否是被静默处理的错误\n          if (error._silenced) {\n            console.log('仪表盘数据获取被静默处理');\n          } else {\n            console.log('获取仪表盘数据失败，可能是权限问题');\n          }\n          // 不显示错误消息，静默处理\n        }\n      } catch (error) {\n        // 捕获所有其他错误\n        console.log('仪表盘组件出现未预期的错误');\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDateTime(row, column, cellValue) {\n      if (!cellValue) return '';\n      const date = new Date(cellValue);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n    getStatusType(reservation) {\n      // 如果预约已取消，返回红色\n      if (reservation.status === 'cancelled') {\n        return 'danger';\n      }\n\n      // 如果预约已过期，返回橙色\n      if (isReservationExpired(reservation.end_datetime)) {\n        return 'warning';\n      }\n\n      // 如果预约正在进行中，返回蓝色\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        return 'primary';\n      }\n\n      // 如果预约已确认且未开始，返回绿色\n      // \"已确认\"状态出现在预约被管理员批准，但还未开始使用的情况\n      return 'success';\n    },\n    getStatusText(reservation) {\n      // 如果预约已取消，显示\"已取消\"\n      if (reservation.status === 'cancelled') {\n        return this.$t('reservation.cancelled');\n      }\n\n      // 如果预约已过期，显示\"已过期\"\n      if (isReservationExpired(reservation.end_datetime)) {\n        return this.$t('reservation.expired');\n      }\n\n      // 如果预约正在进行中，显示\"使用中\"\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        return this.$t('reservation.inUse');\n      }\n\n      // 如果预约已确认且未开始，显示\"已确认\"\n      // \"已确认\"状态出现在预约被管理员批准，但还未开始使用的情况\n      return this.$t('reservation.confirmed');\n    },\n    viewReservation(reservation) {\n      // 计算当前预约的实际状态文本和类型\n      const statusText = this.getStatusText(reservation);\n      const statusType = this.getStatusType(reservation);\n      console.log('计算的状态信息:', {\n        statusText,\n        statusType,\n        dbStatus: reservation.status,\n        startTime: reservation.start_datetime,\n        endTime: reservation.end_datetime,\n        reservationNumber: reservation.reservation_number\n      });\n\n      // 构建URL，添加预约码、时间参数、预约序号和计算好的状态信息\n      const url = {\n        path: `/admin/reservation/${reservation.reservation_code}`,\n        query: {\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime,\n          displayStatus: statusText,\n          displayStatusType: statusType,\n          reservationNumber: reservation.reservation_number // 添加预约序号参数\n        }\n      };\n\n      // 每次查看预约时，都重新设置一个标记，表示需要显示预约序号通知\n      localStorage.setItem('show_reservation_number_notification', 'true');\n\n      // 清除之前的预约序号，确保每次都使用新的预约序号\n      localStorage.removeItem('current_reservation_number');\n\n      // 将预约序号保存到localStorage，以便在页面刷新后仍然可以使用\n      if (reservation.reservation_number) {\n        localStorage.setItem('current_reservation_number', reservation.reservation_number);\n        console.log('保存预约序号到localStorage:', reservation.reservation_number);\n\n        // 强制使用预约序号查询，而不是预约码\n        localStorage.setItem('force_use_reservation_number', 'true');\n      }\n      this.$router.push(url);\n    }\n  }\n};", "map": {"version": 3, "names": ["reservationApi", "equipmentApi", "isReservationExpired", "axios", "name", "data", "loading", "isMobile", "stats", "totalEquipment", "availableEquipment", "totalReservation", "activeReservation", "inUseReservation", "confirmedReservation", "expiredReservation", "cancelledReservation", "recentReservations", "created", "checkMobile", "fetchData", "mounted", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "innerWidth", "token", "localStorage", "getItem", "console", "log", "dashboardResponse", "get", "dashboardData", "total_equipment", "available_equipment", "total_reservation", "active_reservation", "recent_reservations", "length", "map", "reservation", "id", "reservation_number", "reservation_code", "equipment_id", "equipment_name", "user_name", "user_department", "user_contact", "start_datetime", "end_datetime", "status", "created_at", "allReservationsResponse", "getReservations", "limit", "page", "items", "now", "Date", "inUseCount", "confirmedCount", "expiredCount", "cancelledCount", "for<PERSON>ach", "start", "end", "error", "_silenced", "r", "formatDateTime", "row", "column", "cellValue", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getStatusType", "getStatusText", "$t", "viewReservation", "statusText", "statusType", "db<PERSON><PERSON>us", "startTime", "endTime", "reservationNumber", "url", "path", "query", "displayStatus", "displayStatusType", "setItem", "removeItem", "$router", "push"], "sources": ["src/views/admin/AdminDashboard.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-dashboard\">\n    <h2>控制台</h2>\n\n    <!-- 统计卡片 -->\n    <el-row :gutter=\"20\" class=\"stats-row\">\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card primary\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.totalEquipment }}</div>\n              <div class=\"stats-label\">{{ $t('admin.totalEquipment') }}</div>\n            </div>\n            <i class=\"el-icon-s-grid stats-icon\"></i>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card success\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.availableEquipment }}</div>\n              <div class=\"stats-label\">{{ $t('admin.availableEquipment') }}</div>\n            </div>\n            <i class=\"el-icon-s-cooperation stats-icon\"></i>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card warning\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.totalReservation }}</div>\n              <div class=\"stats-label\">{{ $t('admin.totalReservation') }}</div>\n            </div>\n            <i class=\"el-icon-s-order stats-icon\"></i>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card danger\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.activeReservation }}</div>\n              <div class=\"stats-label\">{{ $t('admin.activeReservation') }}</div>\n            </div>\n            <i class=\"el-icon-s-claim stats-icon\"></i>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 详细预约状态统计 -->\n      <el-row :gutter=\"20\" class=\"stats-row\">\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card primary-light\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.inUseReservation || 0 }}</div>\n              <div class=\"stats-label\">{{ $t('reservation.inUse') }}</div>\n            </div>\n            <i class=\"el-icon-loading stats-icon\"></i>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card success-light\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.confirmedReservation || 0 }}</div>\n              <div class=\"stats-label\">{{ $t('reservation.confirmed') }}</div>\n            </div>\n            <i class=\"el-icon-check stats-icon\"></i>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card warning-light\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.expiredReservation || 0 }}</div>\n              <div class=\"stats-label\">{{ $t('reservation.expired') }}</div>\n            </div>\n            <i class=\"el-icon-time stats-icon\"></i>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"12\" :md=\"6\">\n          <el-card shadow=\"hover\" class=\"stats-card danger-light\">\n            <div class=\"stats-content\">\n              <div class=\"stats-value\">{{ stats.cancelledReservation || 0 }}</div>\n              <div class=\"stats-label\">{{ $t('reservation.cancelled') }}</div>\n            </div>\n            <i class=\"el-icon-close stats-icon\"></i>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 最近预定 -->\n      <el-card shadow=\"hover\" class=\"recent-reservations\">\n        <div slot=\"header\" class=\"card-header\">\n          <div class=\"header-with-info\">\n            <span>{{ $t('admin.recentReservations') }}</span>\n            <el-tooltip content=\"显示最近创建的10条预约记录\" placement=\"top\">\n              <i class=\"el-icon-info info-icon\"></i>\n            </el-tooltip>\n          </div>\n          <el-button\n            type=\"text\"\n            @click=\"$router.push('/admin/reservation')\"\n          >\n            {{ $t('common.more') }} <i class=\"el-icon-arrow-right\"></i>\n          </el-button>\n        </div>\n\n        <div v-if=\"loading\" class=\"loading-container\">\n          <el-skeleton :rows=\"5\" animated />\n        </div>\n\n        <div v-else-if=\"recentReservations.length === 0\" class=\"empty-data\">\n          <el-empty :description=\"$t('common.noData')\"></el-empty>\n        </div>\n\n        <!-- 桌面端表格视图 -->\n        <el-table\n          v-if=\"!isMobile\"\n          :data=\"recentReservations\"\n          style=\"width: 100%\"\n          :default-sort=\"{ prop: 'created_at', order: 'descending' }\"\n          header-align=\"center\"\n          cell-class-name=\"text-center\"\n          border\n          stripe\n        >\n          <!-- 添加预约序号列 -->\n          <el-table-column\n            prop=\"reservation_number\"\n            :label=\"$t('reservation.number')\"\n            min-width=\"180\"\n          >\n            <template slot-scope=\"scope\">\n              <span style=\"font-weight: bold;\">{{ scope.row.reservation_number || '-' }}</span>\n            </template>\n          </el-table-column>\n\n          <el-table-column\n            prop=\"reservation_code\"\n            :label=\"$t('reservation.code')\"\n            min-width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <span style=\"color: #F56C6C; font-weight: bold;\">{{ scope.row.reservation_code }}</span>\n            </template>\n          </el-table-column>\n\n          <el-table-column\n            prop=\"equipment_name\"\n            :label=\"$t('reservation.equipmentName')\"\n            min-width=\"120\"\n          ></el-table-column>\n\n          <el-table-column\n            prop=\"user_name\"\n            :label=\"$t('reservation.userName')\"\n            min-width=\"100\"\n          ></el-table-column>\n\n          <el-table-column\n            prop=\"user_department\"\n            :label=\"$t('reservation.userDepartment')\"\n            min-width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              {{ scope.row.user_department || '-' }}\n            </template>\n          </el-table-column>\n\n          <el-table-column\n            prop=\"user_contact\"\n            :label=\"$t('reservation.userContact')\"\n            min-width=\"120\"\n          >\n            <template slot-scope=\"scope\">\n              {{ scope.row.user_contact || '-' }}\n            </template>\n          </el-table-column>\n\n          <el-table-column\n            prop=\"start_datetime\"\n            :label=\"$t('reservation.startTime')\"\n            min-width=\"150\"\n            :formatter=\"formatDateTime\"\n          ></el-table-column>\n\n          <el-table-column\n            prop=\"end_datetime\"\n            :label=\"$t('reservation.endTime')\"\n            min-width=\"150\"\n            :formatter=\"formatDateTime\"\n          ></el-table-column>\n\n          <el-table-column\n            prop=\"status\"\n            :label=\"$t('reservation.status')\"\n            min-width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-tag\n                :type=\"getStatusType(scope.row)\"\n                size=\"medium\"\n                style=\"font-weight: bold; padding: 0px 10px; font-size: 14px;\"\n              >\n                {{ getStatusText(scope.row) }}\n              </el-tag>\n            </template>\n          </el-table-column>\n\n          <el-table-column\n            :label=\"$t('common.operation')\"\n            min-width=\"100\"\n          >\n            <template slot-scope=\"scope\">\n              <el-button\n                type=\"text\"\n                size=\"small\"\n                @click=\"viewReservation(scope.row)\"\n              >\n                {{ $t('admin.viewReservation') }}\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        <!-- 移动端卡片视图 -->\n        <div v-else class=\"mobile-card-container\">\n          <div\n            v-for=\"reservation in recentReservations\"\n            :key=\"reservation.id\"\n            class=\"reservation-mobile-card\"\n            @click=\"viewReservation(reservation)\"\n          >\n            <div class=\"card-header-row\">\n              <div class=\"reservation-info\">\n                <span class=\"reservation-number\">{{ reservation.reservation_number || '-' }}</span>\n                <span class=\"reservation-code\">{{ reservation.reservation_code }}</span>\n              </div>\n              <el-tag\n                :type=\"getStatusType(reservation)\"\n                size=\"small\"\n                class=\"status-tag\"\n              >\n                {{ getStatusText(reservation) }}\n              </el-tag>\n            </div>\n\n            <div class=\"card-content\">\n              <div class=\"info-row\">\n                <span class=\"label\">设备：</span>\n                <span class=\"value\">{{ reservation.equipment_name }}</span>\n              </div>\n              <div class=\"info-row\">\n                <span class=\"label\">用户：</span>\n                <span class=\"value\">{{ reservation.user_name }}</span>\n              </div>\n              <div class=\"info-row\" v-if=\"reservation.user_department\">\n                <span class=\"label\">部门：</span>\n                <span class=\"value\">{{ reservation.user_department }}</span>\n              </div>\n              <div class=\"info-row\" v-if=\"reservation.user_contact\">\n                <span class=\"label\">联系方式：</span>\n                <span class=\"value\">{{ reservation.user_contact }}</span>\n              </div>\n              <div class=\"time-info\">\n                <div class=\"time-row\">\n                  <span class=\"time-label\">开始：</span>\n                  <span class=\"time-value\">{{ formatDateTime(null, null, reservation.start_datetime) }}</span>\n                </div>\n                <div class=\"time-row\">\n                  <span class=\"time-label\">结束：</span>\n                  <span class=\"time-value\">{{ formatDateTime(null, null, reservation.end_datetime) }}</span>\n                </div>\n              </div>\n            </div>\n\n            <div class=\"card-actions\">\n              <el-button\n                type=\"primary\"\n                size=\"small\"\n                @click.stop=\"viewReservation(reservation)\"\n                class=\"view-button\"\n              >\n                查看详情\n              </el-button>\n            </div>\n          </div>\n        </div>\n      </el-card>\n  </div>\n</template>\n\n<script>\nimport { reservationApi, equipmentApi } from '@/api'\nimport { isReservationExpired } from '@/utils/date'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminDashboard',\n\n  data() {\n    return {\n      loading: false,\n      isMobile: false,\n      stats: {\n        totalEquipment: 0,\n        availableEquipment: 0,\n        totalReservation: 0,\n        activeReservation: 0,\n        inUseReservation: 0,\n        confirmedReservation: 0,\n        expiredReservation: 0,\n        cancelledReservation: 0\n      },\n      recentReservations: []\n    }\n  },\n\n  created() {\n    this.checkMobile()\n    this.fetchData()\n  },\n\n  mounted() {\n    window.addEventListener('resize', this.checkMobile)\n  },\n\n  beforeDestroy() {\n    window.removeEventListener('resize', this.checkMobile)\n  },\n\n  methods: {\n    checkMobile() {\n      this.isMobile = window.innerWidth <= 768\n    },\n\n    async fetchData() {\n      this.loading = true\n\n      try {\n        // 检查用户是否已登录\n        const token = localStorage.getItem('token')\n        if (!token) {\n          console.log('用户未登录，跳过仪表盘数据获取')\n          this.loading = false\n          return\n        }\n\n        try {\n          // 使用统计API获取仪表盘数据\n          const dashboardResponse = await axios.get('/api/statistics/dashboard')\n          const dashboardData = dashboardResponse.data\n\n          this.stats.totalEquipment = dashboardData.total_equipment\n          this.stats.availableEquipment = dashboardData.available_equipment\n          this.stats.totalReservation = dashboardData.total_reservation\n          this.stats.activeReservation = dashboardData.active_reservation\n\n          // 处理最近预定数据\n          if (dashboardData.recent_reservations && dashboardData.recent_reservations.length > 0) {\n            this.recentReservations = dashboardData.recent_reservations.map(reservation => ({\n              id: reservation.id,\n              reservation_number: reservation.reservation_number, // 添加预约序号字段\n              reservation_code: reservation.reservation_code,\n              equipment_id: reservation.equipment_id,\n              equipment_name: reservation.equipment_name,\n              user_name: reservation.user_name,\n              user_department: reservation.user_department || '',\n              user_contact: reservation.user_contact || '',\n              start_datetime: reservation.start_datetime,\n              end_datetime: reservation.end_datetime,\n              status: reservation.status,\n              created_at: reservation.created_at\n            }))\n          }\n\n          // 直接从预约管理表获取所有预约数据并计算统计数字\n          try {\n            // 获取预约管理表中的所有预约数据（可能需要分页）\n            const allReservationsResponse = await reservationApi.getReservations({\n              limit: 1000,  // 获取尽可能多的预约数据\n              page: 1\n            })\n\n            if (allReservationsResponse.data && allReservationsResponse.data.items) {\n              const now = new Date()\n              let inUseCount = 0\n              let confirmedCount = 0\n              let expiredCount = 0\n              let cancelledCount = 0\n\n              // 遍历所有预约并计算状态\n              allReservationsResponse.data.items.forEach(reservation => {\n                const start = new Date(reservation.start_datetime)\n                const end = new Date(reservation.end_datetime)\n\n                if (reservation.status === 'cancelled') {\n                  cancelledCount++\n                } else if (now > end) {\n                  expiredCount++\n                } else if (now >= start && now <= end) {\n                  inUseCount++\n                } else if (now < start) {\n                  confirmedCount++\n                }\n              })\n\n              // 更新统计数据\n              this.stats.inUseReservation = inUseCount\n              this.stats.confirmedReservation = confirmedCount\n              this.stats.expiredReservation = expiredCount\n              this.stats.cancelledReservation = cancelledCount\n            }\n          } catch (error) {\n            // 静默处理此错误，使用备选方案\n            if (error._silenced) {\n              console.log('预约数据获取被静默处理')\n            } else {\n              console.log('获取预约状态统计失败，使用备选数据')\n            }\n\n            // 出错时，尝试使用近期预约数据（最后的备选）\n            if (dashboardData.recent_reservations && dashboardData.recent_reservations.length > 0) {\n              const now = new Date()\n              let inUseCount = 0\n              let confirmedCount = 0\n              let expiredCount = 0\n              let cancelledCount = 0\n\n              dashboardData.recent_reservations.forEach(r => {\n                const start = new Date(r.start_datetime)\n                const end = new Date(r.end_datetime)\n\n                if (r.status === 'cancelled') {\n                  cancelledCount++\n                } else if (now > end) {\n                  expiredCount++\n                } else if (now >= start && now <= end) {\n                  inUseCount++\n                } else {\n                  confirmedCount++\n                }\n              })\n\n              this.stats.inUseReservation = inUseCount\n              this.stats.confirmedReservation = confirmedCount\n              this.stats.expiredReservation = expiredCount\n              this.stats.cancelledReservation = cancelledCount\n            }\n          }\n        } catch (error) {\n          // 检查是否是被静默处理的错误\n          if (error._silenced) {\n            console.log('仪表盘数据获取被静默处理')\n          } else {\n            console.log('获取仪表盘数据失败，可能是权限问题')\n          }\n          // 不显示错误消息，静默处理\n        }\n      } catch (error) {\n        // 捕获所有其他错误\n        console.log('仪表盘组件出现未预期的错误')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    formatDateTime(row, column, cellValue) {\n      if (!cellValue) return ''\n\n      const date = new Date(cellValue)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n    },\n\n    getStatusType(reservation) {\n      // 如果预约已取消，返回红色\n      if (reservation.status === 'cancelled') {\n        return 'danger'\n      }\n\n      // 如果预约已过期，返回橙色\n      if (isReservationExpired(reservation.end_datetime)) {\n        return 'warning'\n      }\n\n      // 如果预约正在进行中，返回蓝色\n      const now = new Date()\n      const start = new Date(reservation.start_datetime)\n      const end = new Date(reservation.end_datetime)\n      if (now >= start && now <= end) {\n        return 'primary'\n      }\n\n      // 如果预约已确认且未开始，返回绿色\n      // \"已确认\"状态出现在预约被管理员批准，但还未开始使用的情况\n      return 'success'\n    },\n\n    getStatusText(reservation) {\n      // 如果预约已取消，显示\"已取消\"\n      if (reservation.status === 'cancelled') {\n        return this.$t('reservation.cancelled')\n      }\n\n      // 如果预约已过期，显示\"已过期\"\n      if (isReservationExpired(reservation.end_datetime)) {\n        return this.$t('reservation.expired')\n      }\n\n      // 如果预约正在进行中，显示\"使用中\"\n      const now = new Date()\n      const start = new Date(reservation.start_datetime)\n      const end = new Date(reservation.end_datetime)\n      if (now >= start && now <= end) {\n        return this.$t('reservation.inUse')\n      }\n\n      // 如果预约已确认且未开始，显示\"已确认\"\n      // \"已确认\"状态出现在预约被管理员批准，但还未开始使用的情况\n      return this.$t('reservation.confirmed')\n    },\n\n    viewReservation(reservation) {\n      // 计算当前预约的实际状态文本和类型\n      const statusText = this.getStatusText(reservation)\n      const statusType = this.getStatusType(reservation)\n\n      console.log('计算的状态信息:', {\n        statusText,\n        statusType,\n        dbStatus: reservation.status,\n        startTime: reservation.start_datetime,\n        endTime: reservation.end_datetime,\n        reservationNumber: reservation.reservation_number\n      })\n\n      // 构建URL，添加预约码、时间参数、预约序号和计算好的状态信息\n      const url = {\n        path: `/admin/reservation/${reservation.reservation_code}`,\n        query: {\n          startTime: reservation.start_datetime,\n          endTime: reservation.end_datetime,\n          displayStatus: statusText,\n          displayStatusType: statusType,\n          reservationNumber: reservation.reservation_number // 添加预约序号参数\n        }\n      }\n\n      // 每次查看预约时，都重新设置一个标记，表示需要显示预约序号通知\n      localStorage.setItem('show_reservation_number_notification', 'true')\n\n      // 清除之前的预约序号，确保每次都使用新的预约序号\n      localStorage.removeItem('current_reservation_number')\n\n      // 将预约序号保存到localStorage，以便在页面刷新后仍然可以使用\n      if (reservation.reservation_number) {\n        localStorage.setItem('current_reservation_number', reservation.reservation_number)\n        console.log('保存预约序号到localStorage:', reservation.reservation_number)\n\n        // 强制使用预约序号查询，而不是预约码\n        localStorage.setItem('force_use_reservation_number', 'true')\n      }\n\n      this.$router.push(url)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-dashboard {\n  width: 100%;\n}\n\n.page-header {\n  margin-bottom: 20px;\n  padding: 15px 20px;\n  background-color: #FFFFFF;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n}\n\n.page-title {\n  margin: 0;\n  font-size: 24px;\n  font-weight: 500;\n  color: #303133;\n}\n\n.stats-row {\n  margin-bottom: 20px;\n}\n\n.stats-card {\n  height: 100px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 20px;\n  position: relative;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n/* 第二行的状态卡片可以稍微高一些，以确保图标完全显示 */\n.stats-row:nth-child(2) .stats-card {\n  height: 120px; /* 增加高度 */\n  padding: 20px 20px 25px 20px; /* 增加底部内边距 */\n}\n\n.stats-card.primary {\n  border-left: 4px solid #409EFF;\n}\n\n.stats-card.success {\n  border-left: 4px solid #67C23A;\n}\n\n.stats-card.warning {\n  border-left: 4px solid #E6A23C;\n}\n\n.stats-card.danger {\n  border-left: 4px solid #F56C6C;\n}\n\n/* 新增浅色样式卡片 */\n.stats-card.primary-light {\n  border-left: 4px solid #409EFF;\n  background-color: rgba(64, 158, 255, 0.1);\n}\n\n.stats-card.success-light {\n  border-left: 4px solid #67C23A;\n  background-color: rgba(103, 194, 58, 0.1);\n}\n\n.stats-card.warning-light {\n  border-left: 4px solid #E6A23C;\n  background-color: rgba(230, 162, 60, 0.1);\n}\n\n.stats-card.danger-light {\n  border-left: 4px solid #F56C6C;\n  background-color: rgba(245, 108, 108, 0.1);\n}\n\n.stats-content {\n  z-index: 1;\n}\n\n.stats-value {\n  font-size: 28px;\n  font-weight: bold;\n  color: #303133;\n  margin-bottom: 5px;\n}\n\n.stats-label {\n  font-size: 14px;\n  color: #909399;\n}\n\n.stats-icon {\n  position: absolute;\n  right: 20px;\n  font-size: 60px;\n  opacity: 0.1;\n  color: #000;\n}\n\n/* 修改图标样式，确保完整显示在卡片内 */\n.stats-card .stats-icon {\n  position: absolute;\n  right: 20px;\n  bottom: 15px; /* 调整从底部的距离 */\n  font-size: 50px; /* 调小图标尺寸 */\n  opacity: 0.2; /* 增加透明度，让背景色更明显 */\n  color: #000;\n  overflow: hidden; /* 确保溢出部分隐藏 */\n}\n\n.recent-reservations {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.header-with-info {\n  display: flex;\n  align-items: center;\n}\n\n.info-icon {\n  margin-left: 5px;\n  font-size: 14px;\n  color: #909399;\n  cursor: pointer;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.empty-data {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n/* 移动端卡片样式 */\n.mobile-card-container {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.reservation-mobile-card {\n  background: #fff;\n  border: 1px solid #EBEEF5;\n  border-radius: 8px;\n  padding: 16px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.reservation-mobile-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  transform: translateY(-2px);\n}\n\n.card-header-row {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n  padding-bottom: 8px;\n  border-bottom: 1px solid #F5F7FA;\n}\n\n.reservation-info {\n  display: flex;\n  flex-direction: column;\n  gap: 4px;\n}\n\n.reservation-number {\n  font-size: 16px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.reservation-code {\n  font-size: 14px;\n  color: #F56C6C;\n  font-weight: 600;\n}\n\n.status-tag {\n  font-weight: bold;\n}\n\n.card-content {\n  margin-bottom: 12px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 6px;\n  align-items: center;\n}\n\n.label {\n  font-size: 14px;\n  color: #909399;\n  min-width: 60px;\n  flex-shrink: 0;\n}\n\n.value {\n  font-size: 14px;\n  color: #303133;\n  flex: 1;\n}\n\n.time-info {\n  margin-top: 8px;\n  padding-top: 8px;\n  border-top: 1px solid #F5F7FA;\n}\n\n.time-row {\n  display: flex;\n  margin-bottom: 4px;\n  align-items: center;\n}\n\n.time-label {\n  font-size: 13px;\n  color: #909399;\n  min-width: 40px;\n  flex-shrink: 0;\n}\n\n.time-value {\n  font-size: 13px;\n  color: #E6A23C;\n  font-weight: 500;\n  flex: 1;\n}\n\n.card-actions {\n  display: flex;\n  justify-content: flex-end;\n  padding-top: 8px;\n  border-top: 1px solid #F5F7FA;\n}\n\n.view-button {\n  padding: 6px 16px !important;\n  font-size: 13px !important;\n}\n\n@media (max-width: 768px) {\n  .admin-dashboard {\n    padding-top: 100px !important;\n    padding-bottom: 100px !important;\n  }\n\n  .admin-dashboard h2 {\n    margin-top: 120px !important;\n  }\n\n  .stats-card {\n    height: 80px;\n  }\n\n  .stats-value {\n    font-size: 24px;\n  }\n\n  .stats-icon {\n    font-size: 40px;\n  }\n\n  /* 移动端隐藏表格，显示卡片 */\n  .el-table {\n    display: none;\n  }\n\n  .mobile-card-container {\n    display: block;\n  }\n}\n</style>\n"], "mappings": "AAqSA,SAAAA,cAAA,EAAAC,YAAA;AACA,SAAAC,oBAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,KAAA;QACAC,cAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,oBAAA;QACAC,kBAAA;QACAC,oBAAA;MACA;MACAC,kBAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,SAAA;EACA;EAEAC,QAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAJ,WAAA;EACA;EAEAK,cAAA;IACAF,MAAA,CAAAG,mBAAA,gBAAAN,WAAA;EACA;EAEAO,OAAA;IACAP,YAAA;MACA,KAAAZ,QAAA,GAAAe,MAAA,CAAAK,UAAA;IACA;IAEA,MAAAP,UAAA;MACA,KAAAd,OAAA;MAEA;QACA;QACA,MAAAsB,KAAA,GAAAC,YAAA,CAAAC,OAAA;QACA,KAAAF,KAAA;UACAG,OAAA,CAAAC,GAAA;UACA,KAAA1B,OAAA;UACA;QACA;QAEA;UACA;UACA,MAAA2B,iBAAA,SAAA9B,KAAA,CAAA+B,GAAA;UACA,MAAAC,aAAA,GAAAF,iBAAA,CAAA5B,IAAA;UAEA,KAAAG,KAAA,CAAAC,cAAA,GAAA0B,aAAA,CAAAC,eAAA;UACA,KAAA5B,KAAA,CAAAE,kBAAA,GAAAyB,aAAA,CAAAE,mBAAA;UACA,KAAA7B,KAAA,CAAAG,gBAAA,GAAAwB,aAAA,CAAAG,iBAAA;UACA,KAAA9B,KAAA,CAAAI,iBAAA,GAAAuB,aAAA,CAAAI,kBAAA;;UAEA;UACA,IAAAJ,aAAA,CAAAK,mBAAA,IAAAL,aAAA,CAAAK,mBAAA,CAAAC,MAAA;YACA,KAAAxB,kBAAA,GAAAkB,aAAA,CAAAK,mBAAA,CAAAE,GAAA,CAAAC,WAAA;cACAC,EAAA,EAAAD,WAAA,CAAAC,EAAA;cACAC,kBAAA,EAAAF,WAAA,CAAAE,kBAAA;cAAA;cACAC,gBAAA,EAAAH,WAAA,CAAAG,gBAAA;cACAC,YAAA,EAAAJ,WAAA,CAAAI,YAAA;cACAC,cAAA,EAAAL,WAAA,CAAAK,cAAA;cACAC,SAAA,EAAAN,WAAA,CAAAM,SAAA;cACAC,eAAA,EAAAP,WAAA,CAAAO,eAAA;cACAC,YAAA,EAAAR,WAAA,CAAAQ,YAAA;cACAC,cAAA,EAAAT,WAAA,CAAAS,cAAA;cACAC,YAAA,EAAAV,WAAA,CAAAU,YAAA;cACAC,MAAA,EAAAX,WAAA,CAAAW,MAAA;cACAC,UAAA,EAAAZ,WAAA,CAAAY;YACA;UACA;;UAEA;UACA;YACA;YACA,MAAAC,uBAAA,SAAAxD,cAAA,CAAAyD,eAAA;cACAC,KAAA;cAAA;cACAC,IAAA;YACA;YAEA,IAAAH,uBAAA,CAAAnD,IAAA,IAAAmD,uBAAA,CAAAnD,IAAA,CAAAuD,KAAA;cACA,MAAAC,GAAA,OAAAC,IAAA;cACA,IAAAC,UAAA;cACA,IAAAC,cAAA;cACA,IAAAC,YAAA;cACA,IAAAC,cAAA;;cAEA;cACAV,uBAAA,CAAAnD,IAAA,CAAAuD,KAAA,CAAAO,OAAA,CAAAxB,WAAA;gBACA,MAAAyB,KAAA,OAAAN,IAAA,CAAAnB,WAAA,CAAAS,cAAA;gBACA,MAAAiB,GAAA,OAAAP,IAAA,CAAAnB,WAAA,CAAAU,YAAA;gBAEA,IAAAV,WAAA,CAAAW,MAAA;kBACAY,cAAA;gBACA,WAAAL,GAAA,GAAAQ,GAAA;kBACAJ,YAAA;gBACA,WAAAJ,GAAA,IAAAO,KAAA,IAAAP,GAAA,IAAAQ,GAAA;kBACAN,UAAA;gBACA,WAAAF,GAAA,GAAAO,KAAA;kBACAJ,cAAA;gBACA;cACA;;cAEA;cACA,KAAAxD,KAAA,CAAAK,gBAAA,GAAAkD,UAAA;cACA,KAAAvD,KAAA,CAAAM,oBAAA,GAAAkD,cAAA;cACA,KAAAxD,KAAA,CAAAO,kBAAA,GAAAkD,YAAA;cACA,KAAAzD,KAAA,CAAAQ,oBAAA,GAAAkD,cAAA;YACA;UACA,SAAAI,KAAA;YACA;YACA,IAAAA,KAAA,CAAAC,SAAA;cACAxC,OAAA,CAAAC,GAAA;YACA;cACAD,OAAA,CAAAC,GAAA;YACA;;YAEA;YACA,IAAAG,aAAA,CAAAK,mBAAA,IAAAL,aAAA,CAAAK,mBAAA,CAAAC,MAAA;cACA,MAAAoB,GAAA,OAAAC,IAAA;cACA,IAAAC,UAAA;cACA,IAAAC,cAAA;cACA,IAAAC,YAAA;cACA,IAAAC,cAAA;cAEA/B,aAAA,CAAAK,mBAAA,CAAA2B,OAAA,CAAAK,CAAA;gBACA,MAAAJ,KAAA,OAAAN,IAAA,CAAAU,CAAA,CAAApB,cAAA;gBACA,MAAAiB,GAAA,OAAAP,IAAA,CAAAU,CAAA,CAAAnB,YAAA;gBAEA,IAAAmB,CAAA,CAAAlB,MAAA;kBACAY,cAAA;gBACA,WAAAL,GAAA,GAAAQ,GAAA;kBACAJ,YAAA;gBACA,WAAAJ,GAAA,IAAAO,KAAA,IAAAP,GAAA,IAAAQ,GAAA;kBACAN,UAAA;gBACA;kBACAC,cAAA;gBACA;cACA;cAEA,KAAAxD,KAAA,CAAAK,gBAAA,GAAAkD,UAAA;cACA,KAAAvD,KAAA,CAAAM,oBAAA,GAAAkD,cAAA;cACA,KAAAxD,KAAA,CAAAO,kBAAA,GAAAkD,YAAA;cACA,KAAAzD,KAAA,CAAAQ,oBAAA,GAAAkD,cAAA;YACA;UACA;QACA,SAAAI,KAAA;UACA;UACA,IAAAA,KAAA,CAAAC,SAAA;YACAxC,OAAA,CAAAC,GAAA;UACA;YACAD,OAAA,CAAAC,GAAA;UACA;UACA;QACA;MACA,SAAAsC,KAAA;QACA;QACAvC,OAAA,CAAAC,GAAA;MACA;QACA,KAAA1B,OAAA;MACA;IACA;IAEAmE,eAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,MAAAC,IAAA,OAAAf,IAAA,CAAAc,SAAA;MACA,UAAAC,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAM,QAAA,IAAAF,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAO,UAAA,IAAAH,QAAA;IACA;IAEAI,cAAA1C,WAAA;MACA;MACA,IAAAA,WAAA,CAAAW,MAAA;QACA;MACA;;MAEA;MACA,IAAApD,oBAAA,CAAAyC,WAAA,CAAAU,YAAA;QACA;MACA;;MAEA;MACA,MAAAQ,GAAA,OAAAC,IAAA;MACA,MAAAM,KAAA,OAAAN,IAAA,CAAAnB,WAAA,CAAAS,cAAA;MACA,MAAAiB,GAAA,OAAAP,IAAA,CAAAnB,WAAA,CAAAU,YAAA;MACA,IAAAQ,GAAA,IAAAO,KAAA,IAAAP,GAAA,IAAAQ,GAAA;QACA;MACA;;MAEA;MACA;MACA;IACA;IAEAiB,cAAA3C,WAAA;MACA;MACA,IAAAA,WAAA,CAAAW,MAAA;QACA,YAAAiC,EAAA;MACA;;MAEA;MACA,IAAArF,oBAAA,CAAAyC,WAAA,CAAAU,YAAA;QACA,YAAAkC,EAAA;MACA;;MAEA;MACA,MAAA1B,GAAA,OAAAC,IAAA;MACA,MAAAM,KAAA,OAAAN,IAAA,CAAAnB,WAAA,CAAAS,cAAA;MACA,MAAAiB,GAAA,OAAAP,IAAA,CAAAnB,WAAA,CAAAU,YAAA;MACA,IAAAQ,GAAA,IAAAO,KAAA,IAAAP,GAAA,IAAAQ,GAAA;QACA,YAAAkB,EAAA;MACA;;MAEA;MACA;MACA,YAAAA,EAAA;IACA;IAEAC,gBAAA7C,WAAA;MACA;MACA,MAAA8C,UAAA,QAAAH,aAAA,CAAA3C,WAAA;MACA,MAAA+C,UAAA,QAAAL,aAAA,CAAA1C,WAAA;MAEAZ,OAAA,CAAAC,GAAA;QACAyD,UAAA;QACAC,UAAA;QACAC,QAAA,EAAAhD,WAAA,CAAAW,MAAA;QACAsC,SAAA,EAAAjD,WAAA,CAAAS,cAAA;QACAyC,OAAA,EAAAlD,WAAA,CAAAU,YAAA;QACAyC,iBAAA,EAAAnD,WAAA,CAAAE;MACA;;MAEA;MACA,MAAAkD,GAAA;QACAC,IAAA,wBAAArD,WAAA,CAAAG,gBAAA;QACAmD,KAAA;UACAL,SAAA,EAAAjD,WAAA,CAAAS,cAAA;UACAyC,OAAA,EAAAlD,WAAA,CAAAU,YAAA;UACA6C,aAAA,EAAAT,UAAA;UACAU,iBAAA,EAAAT,UAAA;UACAI,iBAAA,EAAAnD,WAAA,CAAAE,kBAAA;QACA;MACA;;MAEA;MACAhB,YAAA,CAAAuE,OAAA;;MAEA;MACAvE,YAAA,CAAAwE,UAAA;;MAEA;MACA,IAAA1D,WAAA,CAAAE,kBAAA;QACAhB,YAAA,CAAAuE,OAAA,+BAAAzD,WAAA,CAAAE,kBAAA;QACAd,OAAA,CAAAC,GAAA,yBAAAW,WAAA,CAAAE,kBAAA;;QAEA;QACAhB,YAAA,CAAAuE,OAAA;MACA;MAEA,KAAAE,OAAA,CAAAC,IAAA,CAAAR,GAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}