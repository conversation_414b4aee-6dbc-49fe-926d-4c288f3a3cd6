{"ast": null, "code": "export default {\n  name: 'AdminSettings',\n  data() {\n    return {\n      activeTab: 'base'\n    };\n  }\n};", "map": {"version": 3, "names": ["name", "data", "activeTab"], "sources": ["src/views/admin/AdminSettings.vue"], "sourcesContent": ["<template>\r\n  <div class=\"admin-settings\">\r\n    <el-tabs v-model=\"activeTab\">\r\n      <el-tab-pane label=\"基础设置\" name=\"base\">\r\n        <p>（这里是原有系统设置内容）</p>\r\n      </el-tab-pane>\r\n    </el-tabs>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'AdminSettings',\r\n  data() {\r\n    return {\r\n      activeTab: 'base'\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.admin-settings {\r\n  padding: 20px;\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .admin-settings {\r\n    padding-top: 110px;\r\n    padding-bottom: 100px;\r\n  }\r\n}\r\n</style>"], "mappings": "AAWA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}