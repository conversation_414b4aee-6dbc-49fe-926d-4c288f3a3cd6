{"ast": null, "code": "import { mapGetters } from 'vuex';\nimport { equipmentApi, categoryApi } from '@/api';\nimport RichTextEditor from '@/components/common/RichTextEditor.vue';\nimport axios from 'axios';\nexport default {\n  name: 'AdminEquipment',\n  components: {\n    RichTextEditor\n  },\n  data() {\n    return {\n      loading: false,\n      submitting: false,\n      equipments: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 10,\n      categories: [],\n      filter: {\n        category: '',\n        status: '',\n        search: ''\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n      dialogVisible: false,\n      dialogType: 'add',\n      // 'add' or 'edit'\n      form: {\n        id: null,\n        name: '',\n        category: '',\n        model: '',\n        location: '',\n        status: 'available',\n        description: '',\n        user_guide: '',\n        video_tutorial: '',\n        image_path: '',\n        allow_simultaneous: false,\n        max_simultaneous: 1\n      },\n      rules: {\n        name: [{\n          required: true,\n          message: this.$t('reservation.requiredField'),\n          trigger: 'blur'\n        }],\n        category: [{\n          required: true,\n          message: this.$t('reservation.requiredField'),\n          trigger: 'change'\n        }],\n        status: [{\n          required: true,\n          message: this.$t('reservation.requiredField'),\n          trigger: 'change'\n        }]\n      },\n      uploadDialogVisible: false,\n      currentEquipment: {},\n      imageUrl: '',\n      // 上传相关\n      uploadUrl: axios.defaults.baseURL + '/api/equipment/upload-image',\n      // 视频相关\n      videoType: 'youtube',\n      // 富文本编辑器选项\n      editorOptions: {\n        modules: {\n          toolbar: [['bold', 'italic', 'underline', 'strike'], ['blockquote', 'code-block'], [{\n            'header': 1\n          }, {\n            'header': 2\n          }], [{\n            'list': 'ordered'\n          }, {\n            'list': 'bullet'\n          }], [{\n            'script': 'sub'\n          }, {\n            'script': 'super'\n          }], [{\n            'indent': '-1'\n          }, {\n            'indent': '+1'\n          }], [{\n            'direction': 'rtl'\n          }], [{\n            'size': ['small', false, 'large', 'huge']\n          }], [{\n            'header': [1, 2, 3, 4, 5, 6, false]\n          }], [{\n            'color': []\n          }, {\n            'background': []\n          }], [{\n            'font': []\n          }], [{\n            'align': []\n          }], ['clean'], ['link', 'image', 'video']]\n        },\n        placeholder: '',\n        theme: 'snow'\n      }\n    };\n  },\n  computed: {\n    // 获取token\n    getToken() {\n      return localStorage.getItem('token') || '';\n    },\n    uploadHeaders() {\n      return {\n        Authorization: `Bearer ${localStorage.getItem('token') || ''}`\n      };\n    },\n    // 获取完整的图片URL\n    baseUrl() {\n      return axios.defaults.baseURL || 'http://localhost:8000';\n    },\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile ? 'prev, next' : 'total, prev, pager, next';\n    }\n  },\n  created() {\n    this.fetchData();\n    this.fetchCategories();\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 获取设备列表\n    async fetchData() {\n      try {\n        this.loading = true;\n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize,\n          category: this.filter.category || undefined,\n          status: this.filter.status || undefined,\n          search: this.filter.search || undefined\n        };\n        const response = await equipmentApi.getEquipments(params);\n        this.equipments = response.data.items;\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('获取设备列表失败:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 获取设备类别\n    async fetchCategories() {\n      try {\n        const response = await categoryApi.getAllCategories();\n        this.categories = response.data.map(item => item.name);\n      } catch (error) {\n        console.error('获取设备类别失败:', error);\n      }\n    },\n    // 处理筛选条件变化\n    handleFilterChange() {\n      this.currentPage = 1;\n      this.fetchData();\n    },\n    // 处理页码变化\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchData();\n    },\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    // 添加设备\n    handleAdd() {\n      this.dialogType = 'add';\n      this.form = {\n        id: null,\n        name: '',\n        category: '',\n        model: '',\n        location: '',\n        status: 'available',\n        description: '',\n        user_guide: '',\n        video_tutorial: '',\n        image_path: '',\n        allow_simultaneous: false,\n        max_simultaneous: 1\n      };\n      this.dialogVisible = true;\n    },\n    // 编辑设备\n    handleEdit(row) {\n      this.dialogType = 'edit';\n      this.form = {\n        ...row\n      };\n      this.dialogVisible = true;\n    },\n    // 删除设备\n    handleDelete(row) {\n      this.$confirm(this.$t('admin.confirmDeleteEquipment'), this.$t('common.warning'), {\n        confirmButtonText: this.$t('common.confirm'),\n        cancelButtonText: this.$t('common.cancel'),\n        type: 'warning'\n      }).then(async () => {\n        try {\n          this.loading = true;\n          await equipmentApi.deleteEquipment(row.id);\n          this.$message.success(this.$t('admin.equipmentDeleted'));\n          this.fetchData();\n        } catch (error) {\n          console.error('删除设备失败:', error);\n          this.$message.error(this.$t('error.serverError'));\n        } finally {\n          this.loading = false;\n        }\n      }).catch(() => {\n        // 取消删除，不做任何处理\n      });\n    },\n    // 上传设备图片\n    handleUploadImage(row) {\n      this.currentEquipment = row;\n      this.imageUrl = row.image_path || '';\n      this.uploadDialogVisible = true;\n    },\n    // 提交表单\n    submitForm() {\n      this.$refs.form.validate(async valid => {\n        if (!valid) return;\n        try {\n          this.submitting = true;\n          if (this.dialogType === 'add') {\n            // 创建设备\n            const response = await equipmentApi.createEquipment(this.form);\n            this.$message.success(this.$t('admin.equipmentAdded'));\n          } else {\n            // 更新设备\n            const response = await equipmentApi.updateEquipment(this.form.id, this.form);\n            this.$message.success(this.$t('admin.equipmentUpdated'));\n          }\n          this.dialogVisible = false;\n          this.fetchData();\n        } catch (error) {\n          console.error('保存设备失败:', error);\n          this.$message.error(this.$t('error.serverError'));\n        } finally {\n          this.submitting = false;\n        }\n      });\n    },\n    // 重置表单\n    resetForm() {\n      if (this.$refs.form) {\n        this.$refs.form.resetFields();\n      }\n    },\n    // 上传前验证\n    beforeUpload(file) {\n      const isImage = file.type.startsWith('image/');\n      const isLt8M = file.size / 1024 / 1024 < 8;\n      if (!isImage) {\n        this.$message.error(this.$t('admin.imageTypeError'));\n        return false;\n      }\n      if (!isLt8M) {\n        this.$message.error(this.$t('admin.imageSizeError').replace('2MB', '8MB'));\n        return false;\n      }\n      return true;\n    },\n    // 上传成功（添加/编辑表单中）\n    handleUploadSuccess(response) {\n      this.form.image_path = response.data.image_url;\n      this.$message.success(this.$t('admin.imageUploadSuccess'));\n    },\n    // 上传成功（单独上传图片对话框）\n    handleImageUploadSuccess(response) {\n      this.imageUrl = response.data.image_url;\n      this.currentEquipment.image_path = response.data.image_url;\n\n      // 更新设备列表中的图片URL\n      const index = this.equipments.findIndex(item => item.id === this.currentEquipment.id);\n      if (index !== -1) {\n        this.$set(this.equipments, index, {\n          ...this.currentEquipment\n        });\n      }\n      this.$message.success(this.$t('admin.imageUploadSuccess'));\n\n      // 关闭对话框\n      setTimeout(() => {\n        this.uploadDialogVisible = false;\n      }, 1500);\n    },\n    // 上传失败\n    handleUploadError(error, file) {\n      console.error('上传图片失败:', error);\n      console.log('文件信息:', file ? {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: file.lastModified\n      } : 'No file info');\n\n      // 尝试获取更详细的错误信息\n      let errorMessage = this.$t('admin.imageUploadError');\n      if (error.response && error.response.data) {\n        console.error('错误响应数据:', error.response.data);\n        if (error.response.data.detail) {\n          errorMessage += ': ' + error.response.data.detail;\n        }\n      }\n      this.$message.error(errorMessage);\n    },\n    // 处理视频类型变化\n    handleVideoTypeChange() {\n      // 如果已经有视频URL，则根据新的视频类型进行转换\n      if (this.form.video_tutorial) {\n        // 提取视频ID\n        let videoId = '';\n\n        // 尝试从当前的URL中提取视频ID\n        if (this.form.video_tutorial.includes('youtube.com') || this.form.video_tutorial.includes('youtu.be')) {\n          // 从 YouTube URL 提取视频ID\n          const match = this.form.video_tutorial.match(/(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/ ]{11})/);\n          if (match && match[1]) {\n            videoId = match[1];\n          }\n        } else if (this.form.video_tutorial.includes('bilibili.com')) {\n          // 从 Bilibili URL 提取视频ID\n          const match = this.form.video_tutorial.match(/bilibili\\.com\\/video\\/([^\\/?]+)/);\n          if (match && match[1]) {\n            videoId = match[1].replace('BV', '');\n          }\n        }\n\n        // 如果成功提取到视频ID，则根据新的视频类型生成URL\n        if (videoId) {\n          switch (this.videoType) {\n            case 'youtube':\n              this.form.video_tutorial = `https://www.youtube.com/embed/${videoId}`;\n              break;\n            case 'bilibili':\n              this.form.video_tutorial = `https://player.bilibili.com/player.html?bvid=BV${videoId}`;\n              break;\n            default:\n              // 其他类型不做处理\n              break;\n          }\n        }\n      }\n    },\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '';\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://')) {\n        return url;\n      }\n\n      // 如果是相对路径，添加基础URL\n      if (url.startsWith('/')) {\n        return this.baseUrl + url;\n      }\n\n      // 其他情况，添加基础URL和斜杠\n      return this.baseUrl + '/' + url;\n    },\n    // 触发手动上传\n    triggerManualUpload() {\n      this.$refs.manualFileInput.click();\n    },\n    // 处理手动文件选择\n    async handleManualFileChange(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      // 验证文件\n      const isImage = file.type.startsWith('image/');\n      const isLt8M = file.size / 1024 / 1024 < 8;\n      if (!isImage) {\n        this.$message.error(this.$t('admin.imageTypeError'));\n        return;\n      }\n      if (!isLt8M) {\n        this.$message.error(this.$t('admin.imageSizeError').replace('2MB', '8MB'));\n        return;\n      }\n\n      // 创建 FormData\n      const formData = new FormData();\n      formData.append('file', file);\n      try {\n        this.loading = true;\n\n        // 直接使用 axios 发送请求\n        const response = await axios.post(this.uploadUrl, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`\n          }\n        });\n\n        // 处理成功响应\n        this.form.image_path = response.data.data.image_url;\n        this.$message.success(this.$t('admin.imageUploadSuccess'));\n      } catch (error) {\n        console.error('手动上传图片失败:', error);\n        console.log('文件信息:', {\n          name: file.name,\n          size: file.size,\n          type: file.type,\n          lastModified: file.lastModified\n        });\n\n        // 尝试获取更详细的错误信息\n        let errorMessage = this.$t('admin.imageUploadError');\n        if (error.response && error.response.data) {\n          console.error('错误响应数据:', error.response.data);\n          if (error.response.data.detail) {\n            errorMessage += ': ' + error.response.data.detail;\n          }\n        }\n        this.$message.error(errorMessage);\n      } finally {\n        this.loading = false;\n        // 清空文件输入框，允许再次选择同一文件\n        this.$refs.manualFileInput.value = '';\n      }\n    },\n    // 触发对话框手动上传\n    triggerDialogManualUpload() {\n      this.$refs.dialogManualFileInput.click();\n    },\n    // 处理图片加载失败\n    handleImageLoadError(row) {\n      console.log('图片加载失败，使用默认图片', row);\n      // 如果图片加载失败，将image_path设置为空，这样会显示默认图片\n      if (row && row.image_path) {\n        // 在Vue中安全地更新对象属性\n        this.$set(row, 'image_path', '');\n      }\n    },\n    // 处理对话框手动文件选择\n    async handleDialogManualFileChange(event) {\n      const file = event.target.files[0];\n      if (!file) return;\n\n      // 验证文件\n      const isImage = file.type.startsWith('image/');\n      const isLt8M = file.size / 1024 / 1024 < 8;\n      if (!isImage) {\n        this.$message.error(this.$t('admin.imageTypeError'));\n        return;\n      }\n      if (!isLt8M) {\n        this.$message.error(this.$t('admin.imageSizeError').replace('2MB', '8MB'));\n        return;\n      }\n\n      // 创建 FormData\n      const formData = new FormData();\n      formData.append('file', file);\n      if (this.currentEquipment.id) {\n        formData.append('equipment_id', this.currentEquipment.id);\n      }\n      try {\n        this.loading = true;\n\n        // 直接使用 axios 发送请求\n        const response = await axios.post(this.uploadUrl, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`\n          }\n        });\n\n        // 处理成功响应\n        this.imageUrl = response.data.data.image_url;\n        this.currentEquipment.image_path = response.data.data.image_url;\n\n        // 更新设备列表中的图片URL\n        const index = this.equipments.findIndex(item => item.id === this.currentEquipment.id);\n        if (index !== -1) {\n          this.$set(this.equipments, index, {\n            ...this.currentEquipment\n          });\n        }\n        this.$message.success(this.$t('admin.imageUploadSuccess'));\n\n        // 关闭对话框\n        setTimeout(() => {\n          this.uploadDialogVisible = false;\n        }, 1500);\n      } catch (error) {\n        console.error('手动上传图片失败:', error);\n        console.log('文件信息:', {\n          name: file.name,\n          size: file.size,\n          type: file.type,\n          lastModified: file.lastModified\n        });\n\n        // 尝试获取更详细的错误信息\n        let errorMessage = this.$t('admin.imageUploadError');\n        if (error.response && error.response.data) {\n          console.error('错误响应数据:', error.response.data);\n          if (error.response.data.detail) {\n            errorMessage += ': ' + error.response.data.detail;\n          }\n        }\n        this.$message.error(errorMessage);\n      } finally {\n        this.loading = false;\n        // 清空文件输入框，允许再次选择同一文件\n        this.$refs.dialogManualFileInput.value = '';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "equipmentApi", "categoryApi", "RichTextEditor", "axios", "name", "components", "data", "loading", "submitting", "equipments", "total", "currentPage", "pageSize", "categories", "filter", "category", "status", "search", "isMobile", "window", "innerWidth", "dialogVisible", "dialogType", "form", "id", "model", "location", "description", "user_guide", "video_tutorial", "image_path", "allow_simultaneous", "max_simultaneous", "rules", "required", "message", "$t", "trigger", "uploadDialogVisible", "currentEquipment", "imageUrl", "uploadUrl", "defaults", "baseURL", "videoType", "editorOptions", "modules", "toolbar", "placeholder", "theme", "computed", "getToken", "localStorage", "getItem", "uploadHeaders", "Authorization", "baseUrl", "paginationLayout", "created", "fetchData", "fetchCategories", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "params", "page", "limit", "undefined", "response", "getEquipments", "items", "error", "console", "$message", "getAllCategories", "map", "item", "handleFilterChange", "handlePageChange", "handleAdd", "handleEdit", "row", "handleDelete", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "deleteEquipment", "success", "catch", "handleUploadImage", "submitForm", "$refs", "validate", "valid", "createEquipment", "updateEquipment", "resetForm", "resetFields", "beforeUpload", "file", "isImage", "startsWith", "isLt8M", "size", "replace", "handleUploadSuccess", "image_url", "handleImageUploadSuccess", "index", "findIndex", "$set", "setTimeout", "handleUploadError", "log", "lastModified", "errorMessage", "detail", "handleVideoTypeChange", "videoId", "includes", "match", "getFullImageUrl", "url", "triggerManualUpload", "manualFileInput", "click", "handleManualFileChange", "event", "target", "files", "formData", "FormData", "append", "post", "headers", "value", "triggerDialogManualUpload", "dialogManualFileInput", "handleImageLoadError", "handleDialogManualFileChange"], "sources": ["src/views/admin/AdminEquipment.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-equipment\">\n    <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;\">\n      <h2>设备管理</h2>\n      <el-button\n        type=\"primary\"\n        icon=\"el-icon-plus\"\n        @click=\"handleAdd\"\n      >\n        添加设备\n      </el-button>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <el-card class=\"filter-card\">\n      <el-form :inline=\"true\" :model=\"filter\" class=\"filter-form\">\n        <el-form-item :label=\"$t('equipment.category')\">\n          <el-select\n            v-model=\"filter.category\"\n            :placeholder=\"$t('equipment.allCategories')\"\n            clearable\n            @change=\"handleFilterChange\"\n          >\n            <el-option\n              v-for=\"category in categories\"\n              :key=\"category\"\n              :label=\"category\"\n              :value=\"category\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item :label=\"$t('equipment.status')\">\n          <el-select\n            v-model=\"filter.status\"\n            :placeholder=\"$t('equipment.allStatus')\"\n            clearable\n            @change=\"handleFilterChange\"\n          >\n            <el-option\n              :label=\"$t('equipment.available')\"\n              value=\"available\"\n            ></el-option>\n            <el-option\n              :label=\"$t('equipment.maintenance')\"\n              value=\"maintenance\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item>\n          <el-input\n            v-model=\"filter.search\"\n            :placeholder=\"$t('equipment.searchPlaceholder')\"\n            clearable\n            @keyup.enter.native=\"handleFilterChange\"\n          >\n            <el-button\n              slot=\"append\"\n              icon=\"el-icon-search\"\n              @click=\"handleFilterChange\"\n            ></el-button>\n          </el-input>\n        </el-form-item>\n      </el-form>\n    </el-card>\n\n    <!-- 移动端卡片视图 -->\n    <div v-if=\"isMobile\" class=\"mobile-card-container\" style=\"margin-top: 20px;\">\n      <div\n        v-for=\"equipment in equipments\"\n        :key=\"equipment.id\"\n        class=\"equipment-mobile-card\"\n      >\n        <div class=\"card-header\">\n          <div class=\"equipment-info\">\n            <div class=\"equipment-id\">ID: {{ equipment.id }}</div>\n            <div class=\"equipment-name\">{{ equipment.name }}</div>\n          </div>\n          <el-tag\n            :type=\"equipment.status === 'available' ? 'success' : 'warning'\"\n            size=\"medium\"\n          >\n            {{ equipment.status === 'available' ? '可用' : '维护中' }}\n          </el-tag>\n        </div>\n\n        <div class=\"card-content\">\n          <div class=\"info-row\">\n            <span class=\"label\">类别:</span>\n            <span class=\"value\">{{ equipment.category }}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"label\">位置:</span>\n            <span class=\"value\">{{ equipment.location }}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"label\">描述:</span>\n            <span class=\"value\">{{ equipment.description || '无' }}</span>\n          </div>\n          <div class=\"info-row\">\n            <span class=\"label\">同时预定:</span>\n            <el-tag\n              :type=\"equipment.allow_simultaneous ? 'success' : 'info'\"\n              size=\"small\"\n            >\n              {{ equipment.allow_simultaneous ? `支持(${equipment.max_simultaneous}人)` : '不支持' }}\n            </el-tag>\n          </div>\n          <div class=\"info-row\" v-if=\"equipment.image_path\">\n            <span class=\"label\">图片:</span>\n            <el-image\n              :src=\"getFullImageUrl(equipment.image_path)\"\n              style=\"width: 60px; height: 60px;\"\n              fit=\"contain\"\n              :preview-src-list=\"[getFullImageUrl(equipment.image_path)]\"\n            ></el-image>\n          </div>\n        </div>\n\n        <div class=\"card-actions\">\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"handleEdit(equipment)\"\n          >\n            编辑\n          </el-button>\n          <el-button\n            v-if=\"equipment.image_path\"\n            type=\"info\"\n            size=\"small\"\n            @click=\"handleUploadImage(equipment)\"\n          >\n            更换图片\n          </el-button>\n          <el-button\n            type=\"danger\"\n            size=\"small\"\n            @click=\"handleDelete(equipment)\"\n          >\n            删除\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 桌面端表格视图 -->\n    <el-table\n      v-else\n      v-loading=\"loading\"\n      :data=\"equipments\"\n      border\n      stripe\n      style=\"width: 100%; margin-top: 20px;\"\n      header-align=\"center\"\n      cell-class-name=\"text-center\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        width=\"80\"\n      ></el-table-column>\n\n      <el-table-column\n        prop=\"name\"\n        label=\"设备名称\"\n        width=\"120\"\n      ></el-table-column>\n\n      <el-table-column\n        prop=\"category\"\n        label=\"设备类别\"\n        width=\"120\"\n      ></el-table-column>\n\n      <el-table-column\n        prop=\"location\"\n        label=\"设备位置\"\n        width=\"100\"\n      ></el-table-column>\n\n      <el-table-column\n        prop=\"description\"\n        label=\"设备描述\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">\n          {{ scope.row.description || '无' }}\n        </template>\n      </el-table-column>\n\n      <el-table-column\n        prop=\"status\"\n        label=\"设备状态\"\n        width=\"100\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.status === 'available' ? 'success' : 'warning'\"\n            size=\"small\"\n          >\n            {{ scope.row.status === 'available' ? '可用' : '维护中' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column\n        label=\"可同时预定\"\n        width=\"110\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-tag\n            :type=\"scope.row.allow_simultaneous ? 'success' : 'info'\"\n            size=\"small\"\n          >\n            {{ scope.row.allow_simultaneous ? `支持(${scope.row.max_simultaneous}人)` : '不支持' }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column\n        label=\"设备图片\"\n        width=\"100\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <div>\n            <el-image\n              :src=\"scope.row.image_path ? getFullImageUrl(scope.row.image_path) : require('@/assets/upload.png')\"\n              :preview-src-list=\"scope.row.image_path ? [getFullImageUrl(scope.row.image_path)] : []\"\n              style=\"width: 60px; height: 60px;\"\n              fit=\"contain\"\n              :class=\"scope.row.image_path ? 'preview-image' : 'default-image'\"\n              @error=\"() => handleImageLoadError(scope.row)\"\n            ></el-image>\n          </div>\n        </template>\n      </el-table-column>\n\n      <el-table-column\n        label=\"操作\"\n        width=\"200\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            @click=\"handleEdit(scope.row)\"\n          >\n            编辑\n          </el-button>\n\n          <el-button\n            v-if=\"scope.row.image_path\"\n            type=\"text\"\n            size=\"small\"\n            @click=\"handleUploadImage(scope.row)\"\n          >\n            更换图片\n          </el-button>\n\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            class=\"danger-button\"\n            @click=\"handleDelete(scope.row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        background\n        :layout=\"paginationLayout\"\n        :total=\"total\"\n        :page-size=\"pageSize\"\n        :current-page.sync=\"currentPage\"\n        @current-change=\"handlePageChange\"\n      ></el-pagination>\n    </div>\n\n    <!-- 添加/编辑设备对话框 -->\n    <el-dialog\n      :title=\"dialogType === 'add' ? '添加设备' : '编辑设备'\"\n      :visible.sync=\"dialogVisible\"\n      width=\"50%\"\n      @close=\"resetForm\"\n    >\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"设备名称\" prop=\"name\">\n          <el-input v-model=\"form.name\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"设备类别\" prop=\"category\">\n          <el-select\n            v-model=\"form.category\"\n            filterable\n            allow-create\n            default-first-option\n            style=\"width: 100%\"\n          >\n            <el-option\n              v-for=\"category in categories\"\n              :key=\"category\"\n              :label=\"category\"\n              :value=\"category\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item label=\"设备型号\" prop=\"model\">\n          <el-input v-model=\"form.model\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"设备位置\" prop=\"location\">\n          <el-input v-model=\"form.location\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"设备状态\" prop=\"status\">\n          <el-radio-group v-model=\"form.status\">\n            <el-radio label=\"available\">可用</el-radio>\n            <el-radio label=\"maintenance\">维护中</el-radio>\n          </el-radio-group>\n        </el-form-item>\n\n        <el-form-item label=\"可同时预定\" prop=\"allow_simultaneous\">\n          <el-switch\n            v-model=\"form.allow_simultaneous\"\n            active-text=\"启用\"\n            inactive-text=\"禁用\"\n          ></el-switch>\n\n          <div v-if=\"form.allow_simultaneous\" style=\"margin-top: 10px;\">\n            <el-form-item label=\"最大预定人数\" prop=\"max_simultaneous\">\n              <el-input-number\n                v-model=\"form.max_simultaneous\"\n                :min=\"1\"\n                :max=\"20\"\n                size=\"small\"\n              ></el-input-number>\n              <div class=\"form-tip\">设置可同时预定的最大人数</div>\n            </el-form-item>\n          </div>\n        </el-form-item>\n\n        <el-form-item label=\"设备描述\" prop=\"description\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"form.description\"\n            :rows=\"4\"\n          ></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"使用指南\" prop=\"user_guide\">\n          <rich-text-editor\n            v-model=\"form.user_guide\"\n            placeholder=\"请输入设备的详细使用步骤、注意事项等信息...\"\n          ></rich-text-editor>\n        </el-form-item>\n\n        <el-form-item label=\"视频教程\" prop=\"video_tutorial\">\n          <el-input\n            v-model=\"form.video_tutorial\"\n            placeholder=\"请输入视频链接，支持YouTube、Bilibili等平台\"\n          >\n            <template slot=\"prepend\">\n              <el-select\n                v-model=\"videoType\"\n                style=\"width: 120px;\"\n                @change=\"handleVideoTypeChange\"\n              >\n                <el-option label=\"YouTube\" value=\"youtube\"></el-option>\n                <el-option label=\"Bilibili\" value=\"bilibili\"></el-option>\n                <el-option label=\"其他\" value=\"other\"></el-option>\n              </el-select>\n            </template>\n          </el-input>\n          <div class=\"video-tip\">输入视频链接后可以在设备详情页面查看视频教程</div>\n        </el-form-item>\n\n        <el-form-item label=\"设备图片\">\n          <div class=\"equipment-image-uploader\">\n            <img v-if=\"form.image_path\" :src=\"getFullImageUrl(form.image_path)\" class=\"equipment-image\">\n            <img v-else :src=\"require('@/assets/upload.png')\" class=\"equipment-image default-equipment-image\">\n          </div>\n          <div class=\"image-tip\">建议尺寸：800x600像素，最大8MB</div>\n          <div class=\"manual-upload\">\n            <el-button size=\"small\" type=\"primary\" @click=\"triggerManualUpload\">手动上传</el-button>\n            <input\n              ref=\"manualFileInput\"\n              type=\"file\"\n              accept=\"image/*\"\n              style=\"display: none;\"\n              @change=\"handleManualFileChange\"\n            >\n          </div>\n        </el-form-item>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">保存</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 上传图片对话框 -->\n    <el-dialog\n      title=\"上传设备图片\"\n      :visible.sync=\"uploadDialogVisible\"\n      width=\"30%\"\n    >\n      <div class=\"equipment-image-uploader\">\n        <img v-if=\"imageUrl\" :src=\"getFullImageUrl(imageUrl)\" class=\"equipment-image\">\n        <img v-else :src=\"require('@/assets/upload.png')\" class=\"equipment-image default-equipment-image\">\n      </div>\n      <div class=\"image-tip\">建议尺寸：800x600像素，最大8MB</div>\n      <div class=\"manual-upload\">\n        <el-button size=\"small\" type=\"primary\" @click=\"triggerDialogManualUpload\">手动上传</el-button>\n        <input\n          ref=\"dialogManualFileInput\"\n          type=\"file\"\n          accept=\"image/*\"\n          style=\"display: none;\"\n          @change=\"handleDialogManualFileChange\"\n        >\n      </div>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"uploadDialogVisible = false\">取消</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { equipmentApi, categoryApi } from '@/api'\nimport RichTextEditor from '@/components/common/RichTextEditor.vue'\nimport axios from 'axios'\n\nexport default {\n  name: 'AdminEquipment',\n\n  components: {\n    RichTextEditor\n  },\n\n  data() {\n    return {\n      loading: false,\n      submitting: false,\n      equipments: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 10,\n      categories: [],\n      filter: {\n        category: '',\n        status: '',\n        search: ''\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n\n      dialogVisible: false,\n      dialogType: 'add', // 'add' or 'edit'\n      form: {\n        id: null,\n        name: '',\n        category: '',\n        model: '',\n        location: '',\n        status: 'available',\n        description: '',\n        user_guide: '',\n        video_tutorial: '',\n        image_path: '',\n        allow_simultaneous: false,\n        max_simultaneous: 1\n      },\n      rules: {\n        name: [\n          { required: true, message: this.$t('reservation.requiredField'), trigger: 'blur' }\n        ],\n        category: [\n          { required: true, message: this.$t('reservation.requiredField'), trigger: 'change' }\n        ],\n        status: [\n          { required: true, message: this.$t('reservation.requiredField'), trigger: 'change' }\n        ]\n      },\n\n      uploadDialogVisible: false,\n      currentEquipment: {},\n      imageUrl: '',\n\n      // 上传相关\n      uploadUrl: axios.defaults.baseURL + '/api/equipment/upload-image',\n\n      // 视频相关\n      videoType: 'youtube',\n\n      // 富文本编辑器选项\n      editorOptions: {\n        modules: {\n          toolbar: [\n            ['bold', 'italic', 'underline', 'strike'],\n            ['blockquote', 'code-block'],\n            [{ 'header': 1 }, { 'header': 2 }],\n            [{ 'list': 'ordered' }, { 'list': 'bullet' }],\n            [{ 'script': 'sub' }, { 'script': 'super' }],\n            [{ 'indent': '-1' }, { 'indent': '+1' }],\n            [{ 'direction': 'rtl' }],\n            [{ 'size': ['small', false, 'large', 'huge'] }],\n            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],\n            [{ 'color': [] }, { 'background': [] }],\n            [{ 'font': [] }],\n            [{ 'align': [] }],\n            ['clean'],\n            ['link', 'image', 'video']\n          ]\n        },\n        placeholder: '',\n        theme: 'snow'\n      },\n    }\n  },\n\n  computed: {\n    // 获取token\n    getToken() {\n      return localStorage.getItem('token') || ''\n    },\n\n    uploadHeaders() {\n      return {\n        Authorization: `Bearer ${localStorage.getItem('token') || ''}`\n      }\n    },\n\n    // 获取完整的图片URL\n    baseUrl() {\n      return axios.defaults.baseURL || 'http://localhost:8000';\n    },\n\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile\n        ? 'prev, next'\n        : 'total, prev, pager, next';\n    }\n  },\n\n  created() {\n    this.fetchData()\n    this.fetchCategories()\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize)\n  },\n\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize)\n  },\n\n  methods: {\n    // 获取设备列表\n    async fetchData() {\n      try {\n        this.loading = true\n\n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize,\n          category: this.filter.category || undefined,\n          status: this.filter.status || undefined,\n          search: this.filter.search || undefined\n        }\n\n        const response = await equipmentApi.getEquipments(params)\n        this.equipments = response.data.items\n        this.total = response.data.total\n      } catch (error) {\n        console.error('获取设备列表失败:', error)\n        this.$message.error(this.$t('error.serverError'))\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 获取设备类别\n    async fetchCategories() {\n      try {\n        const response = await categoryApi.getAllCategories()\n        this.categories = response.data.map(item => item.name)\n      } catch (error) {\n        console.error('获取设备类别失败:', error)\n      }\n    },\n\n    // 处理筛选条件变化\n    handleFilterChange() {\n      this.currentPage = 1\n      this.fetchData()\n    },\n\n    // 处理页码变化\n    handlePageChange(page) {\n      this.currentPage = page\n      this.fetchData()\n    },\n\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768\n    },\n\n    // 添加设备\n    handleAdd() {\n      this.dialogType = 'add'\n      this.form = {\n        id: null,\n        name: '',\n        category: '',\n        model: '',\n        location: '',\n        status: 'available',\n        description: '',\n        user_guide: '',\n        video_tutorial: '',\n        image_path: '',\n        allow_simultaneous: false,\n        max_simultaneous: 1\n      }\n      this.dialogVisible = true\n    },\n\n    // 编辑设备\n    handleEdit(row) {\n      this.dialogType = 'edit'\n      this.form = { ...row }\n      this.dialogVisible = true\n    },\n\n    // 删除设备\n    handleDelete(row) {\n      this.$confirm(\n        this.$t('admin.confirmDeleteEquipment'),\n        this.$t('common.warning'),\n        {\n          confirmButtonText: this.$t('common.confirm'),\n          cancelButtonText: this.$t('common.cancel'),\n          type: 'warning'\n        }\n      ).then(async () => {\n        try {\n          this.loading = true\n\n          await equipmentApi.deleteEquipment(row.id)\n\n          this.$message.success(this.$t('admin.equipmentDeleted'))\n          this.fetchData()\n        } catch (error) {\n          console.error('删除设备失败:', error)\n          this.$message.error(this.$t('error.serverError'))\n        } finally {\n          this.loading = false\n        }\n      }).catch(() => {\n        // 取消删除，不做任何处理\n      })\n    },\n\n    // 上传设备图片\n    handleUploadImage(row) {\n      this.currentEquipment = row\n      this.imageUrl = row.image_path || ''\n      this.uploadDialogVisible = true\n    },\n\n    // 提交表单\n    submitForm() {\n      this.$refs.form.validate(async valid => {\n        if (!valid) return\n\n        try {\n          this.submitting = true\n\n          if (this.dialogType === 'add') {\n            // 创建设备\n            const response = await equipmentApi.createEquipment(this.form)\n            this.$message.success(this.$t('admin.equipmentAdded'))\n          } else {\n            // 更新设备\n            const response = await equipmentApi.updateEquipment(this.form.id, this.form)\n            this.$message.success(this.$t('admin.equipmentUpdated'))\n          }\n\n          this.dialogVisible = false\n          this.fetchData()\n        } catch (error) {\n          console.error('保存设备失败:', error)\n          this.$message.error(this.$t('error.serverError'))\n        } finally {\n          this.submitting = false\n        }\n      })\n    },\n\n    // 重置表单\n    resetForm() {\n      if (this.$refs.form) {\n        this.$refs.form.resetFields()\n      }\n    },\n\n    // 上传前验证\n    beforeUpload(file) {\n      const isImage = file.type.startsWith('image/')\n      const isLt8M = file.size / 1024 / 1024 < 8\n\n      if (!isImage) {\n        this.$message.error(this.$t('admin.imageTypeError'))\n        return false\n      }\n\n      if (!isLt8M) {\n        this.$message.error(this.$t('admin.imageSizeError').replace('2MB', '8MB'))\n        return false\n      }\n\n      return true\n    },\n\n    // 上传成功（添加/编辑表单中）\n    handleUploadSuccess(response) {\n      this.form.image_path = response.data.image_url\n      this.$message.success(this.$t('admin.imageUploadSuccess'))\n    },\n\n    // 上传成功（单独上传图片对话框）\n    handleImageUploadSuccess(response) {\n      this.imageUrl = response.data.image_url\n      this.currentEquipment.image_path = response.data.image_url\n\n      // 更新设备列表中的图片URL\n      const index = this.equipments.findIndex(item => item.id === this.currentEquipment.id)\n      if (index !== -1) {\n        this.$set(this.equipments, index, { ...this.currentEquipment })\n      }\n\n      this.$message.success(this.$t('admin.imageUploadSuccess'))\n\n      // 关闭对话框\n      setTimeout(() => {\n        this.uploadDialogVisible = false\n      }, 1500)\n    },\n\n    // 上传失败\n    handleUploadError(error, file) {\n      console.error('上传图片失败:', error)\n      console.log('文件信息:', file ? {\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        lastModified: file.lastModified\n      } : 'No file info')\n\n      // 尝试获取更详细的错误信息\n      let errorMessage = this.$t('admin.imageUploadError')\n      if (error.response && error.response.data) {\n        console.error('错误响应数据:', error.response.data)\n        if (error.response.data.detail) {\n          errorMessage += ': ' + error.response.data.detail\n        }\n      }\n\n      this.$message.error(errorMessage)\n    },\n\n    // 处理视频类型变化\n    handleVideoTypeChange() {\n      // 如果已经有视频URL，则根据新的视频类型进行转换\n      if (this.form.video_tutorial) {\n        // 提取视频ID\n        let videoId = ''\n\n        // 尝试从当前的URL中提取视频ID\n        if (this.form.video_tutorial.includes('youtube.com') || this.form.video_tutorial.includes('youtu.be')) {\n          // 从 YouTube URL 提取视频ID\n          const match = this.form.video_tutorial.match(/(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/ ]{11})/)\n          if (match && match[1]) {\n            videoId = match[1]\n          }\n        } else if (this.form.video_tutorial.includes('bilibili.com')) {\n          // 从 Bilibili URL 提取视频ID\n          const match = this.form.video_tutorial.match(/bilibili\\.com\\/video\\/([^\\/?]+)/)\n          if (match && match[1]) {\n            videoId = match[1].replace('BV', '')\n          }\n        }\n\n        // 如果成功提取到视频ID，则根据新的视频类型生成URL\n        if (videoId) {\n          switch (this.videoType) {\n            case 'youtube':\n              this.form.video_tutorial = `https://www.youtube.com/embed/${videoId}`\n              break\n            case 'bilibili':\n              this.form.video_tutorial = `https://player.bilibili.com/player.html?bvid=BV${videoId}`\n              break\n            default:\n              // 其他类型不做处理\n              break\n          }\n        }\n      }\n    },\n\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '';\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://')) {\n        return url;\n      }\n\n      // 如果是相对路径，添加基础URL\n      if (url.startsWith('/')) {\n        return this.baseUrl + url;\n      }\n\n      // 其他情况，添加基础URL和斜杠\n      return this.baseUrl + '/' + url;\n    },\n\n    // 触发手动上传\n    triggerManualUpload() {\n      this.$refs.manualFileInput.click()\n    },\n\n    // 处理手动文件选择\n    async handleManualFileChange(event) {\n      const file = event.target.files[0]\n      if (!file) return\n\n      // 验证文件\n      const isImage = file.type.startsWith('image/')\n      const isLt8M = file.size / 1024 / 1024 < 8\n\n      if (!isImage) {\n        this.$message.error(this.$t('admin.imageTypeError'))\n        return\n      }\n\n      if (!isLt8M) {\n        this.$message.error(this.$t('admin.imageSizeError').replace('2MB', '8MB'))\n        return\n      }\n\n      // 创建 FormData\n      const formData = new FormData()\n      formData.append('file', file)\n\n      try {\n        this.loading = true\n\n        // 直接使用 axios 发送请求\n        const response = await axios.post(this.uploadUrl, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`\n          }\n        })\n\n        // 处理成功响应\n        this.form.image_path = response.data.data.image_url\n        this.$message.success(this.$t('admin.imageUploadSuccess'))\n      } catch (error) {\n        console.error('手动上传图片失败:', error)\n        console.log('文件信息:', {\n          name: file.name,\n          size: file.size,\n          type: file.type,\n          lastModified: file.lastModified\n        })\n\n        // 尝试获取更详细的错误信息\n        let errorMessage = this.$t('admin.imageUploadError')\n        if (error.response && error.response.data) {\n          console.error('错误响应数据:', error.response.data)\n          if (error.response.data.detail) {\n            errorMessage += ': ' + error.response.data.detail\n          }\n        }\n\n        this.$message.error(errorMessage)\n      } finally {\n        this.loading = false\n        // 清空文件输入框，允许再次选择同一文件\n        this.$refs.manualFileInput.value = ''\n      }\n    },\n\n    // 触发对话框手动上传\n    triggerDialogManualUpload() {\n      this.$refs.dialogManualFileInput.click()\n    },\n\n    // 处理图片加载失败\n    handleImageLoadError(row) {\n      console.log('图片加载失败，使用默认图片', row)\n      // 如果图片加载失败，将image_path设置为空，这样会显示默认图片\n      if (row && row.image_path) {\n        // 在Vue中安全地更新对象属性\n        this.$set(row, 'image_path', '')\n      }\n    },\n\n    // 处理对话框手动文件选择\n    async handleDialogManualFileChange(event) {\n      const file = event.target.files[0]\n      if (!file) return\n\n      // 验证文件\n      const isImage = file.type.startsWith('image/')\n      const isLt8M = file.size / 1024 / 1024 < 8\n\n      if (!isImage) {\n        this.$message.error(this.$t('admin.imageTypeError'))\n        return\n      }\n\n      if (!isLt8M) {\n        this.$message.error(this.$t('admin.imageSizeError').replace('2MB', '8MB'))\n        return\n      }\n\n      // 创建 FormData\n      const formData = new FormData()\n      formData.append('file', file)\n      if (this.currentEquipment.id) {\n        formData.append('equipment_id', this.currentEquipment.id)\n      }\n\n      try {\n        this.loading = true\n\n        // 直接使用 axios 发送请求\n        const response = await axios.post(this.uploadUrl, formData, {\n          headers: {\n            'Content-Type': 'multipart/form-data',\n            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`\n          }\n        })\n\n        // 处理成功响应\n        this.imageUrl = response.data.data.image_url\n        this.currentEquipment.image_path = response.data.data.image_url\n\n        // 更新设备列表中的图片URL\n        const index = this.equipments.findIndex(item => item.id === this.currentEquipment.id)\n        if (index !== -1) {\n          this.$set(this.equipments, index, { ...this.currentEquipment })\n        }\n\n        this.$message.success(this.$t('admin.imageUploadSuccess'))\n\n        // 关闭对话框\n        setTimeout(() => {\n          this.uploadDialogVisible = false\n        }, 1500)\n      } catch (error) {\n        console.error('手动上传图片失败:', error)\n        console.log('文件信息:', {\n          name: file.name,\n          size: file.size,\n          type: file.type,\n          lastModified: file.lastModified\n        })\n\n        // 尝试获取更详细的错误信息\n        let errorMessage = this.$t('admin.imageUploadError')\n        if (error.response && error.response.data) {\n          console.error('错误响应数据:', error.response.data)\n          if (error.response.data.detail) {\n            errorMessage += ': ' + error.response.data.detail\n          }\n        }\n\n        this.$message.error(errorMessage)\n      } finally {\n        this.loading = false\n        // 清空文件输入框，允许再次选择同一文件\n        this.$refs.dialogManualFileInput.value = ''\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-equipment {\n  padding: 20px;\n  width: 100%;\n  max-width: 100%;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 15px 20px;\n  background-color: #FFFFFF;\n  border-radius: 4px;\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\n}\n\n.page-actions {\n  display: flex;\n  gap: 10px;\n  flex-wrap: wrap;\n}\n\n.page-header h2 {\n  margin: 0;\n  font-size: 20px;\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.filter-form .el-form-item {\n  margin-bottom: 0;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  text-align: right;\n}\n\n.quill-editor {\n  height: 300px;\n  margin-bottom: 10px;\n}\n\n.editor-tip {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 5px;\n}\n\n.danger-button {\n  color: #F56C6C;\n}\n\n.equipment-image-uploader {\n  text-align: center;\n}\n\n.equipment-image-uploader .el-upload {\n  border: 1px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  width: 300px;\n  height: 225px;\n  display: inline-block;\n}\n\n.equipment-image-uploader .el-upload:hover {\n  border-color: #409EFF;\n}\n\n.equipment-image-uploader-icon {\n  font-size: 28px;\n  color: #8c939d;\n  width: 300px;\n  height: 225px;\n  line-height: 225px;\n  text-align: center;\n}\n\n.equipment-image {\n  width: 300px;\n  height: 225px;\n  display: block;\n  object-fit: contain;\n}\n\n.default-equipment-image {\n  opacity: 0.7;\n  background-color: #f5f7fa;\n  padding: 20px;\n  box-sizing: border-box;\n}\n\n.image-tip,\n.video-tip {\n  font-size: 12px;\n  color: #909399;\n  margin-top: 5px;\n}\n\n.manual-upload {\n  margin-top: 10px;\n}\n\n.clickable-image {\n  cursor: pointer;\n  transition: all 0.3s;\n}\n\n.clickable-image:hover {\n  transform: scale(1.05);\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n}\n\n.preview-image {\n  cursor: zoom-in;\n  transition: all 0.3s;\n}\n\n.preview-image:hover {\n  transform: scale(1.05);\n  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);\n}\n\n.default-image {\n  cursor: default;\n  opacity: 0.7;\n  transition: all 0.3s;\n}\n\n.default-image:hover {\n  opacity: 1;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n/* 移动端卡片样式 */\n.mobile-card-container {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.equipment-mobile-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 15px;\n  border: 1px solid #e8e8e8;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.equipment-info {\n  flex: 1;\n}\n\n.equipment-id {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n}\n\n.equipment-name {\n  font-weight: bold;\n  font-size: 14px;\n  color: #333;\n}\n\n.card-content {\n  margin-bottom: 15px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 8px;\n  align-items: flex-start;\n}\n\n.info-row .label {\n  min-width: 70px;\n  font-size: 13px;\n  color: #666;\n  margin-right: 10px;\n  flex-shrink: 0;\n}\n\n.info-row .value {\n  font-size: 13px;\n  color: #333;\n  flex: 1;\n  word-break: break-all;\n}\n\n.card-actions {\n  text-align: center;\n  padding-top: 10px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.card-actions .el-button {\n  margin: 0 3px 5px 3px;\n  font-size: 12px;\n}\n\n@media (max-width: 768px) {\n  .admin-equipment {\n    padding: 10px;\n    padding-top: 150px;\n    padding-bottom: 100px;\n  }\n  .admin-equipment h2 {\n    margin-top: 120px !important;\n  }\n  .filter-form {\n    display: flex;\n    flex-direction: column;\n  }\n\n  .filter-form .el-form-item {\n    margin-right: 0;\n    margin-bottom: 10px;\n  }\n}\n</style>\n"], "mappings": "AA8bA,SAAAA,UAAA;AACA,SAAAC,YAAA,EAAAC,WAAA;AACA,OAAAC,cAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EAEAC,UAAA;IACAH;EACA;EAEAI,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,UAAA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,UAAA;MACAC,MAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;MACA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;MAEAC,aAAA;MACAC,UAAA;MAAA;MACAC,IAAA;QACAC,EAAA;QACApB,IAAA;QACAW,QAAA;QACAU,KAAA;QACAC,QAAA;QACAV,MAAA;QACAW,WAAA;QACAC,UAAA;QACAC,cAAA;QACAC,UAAA;QACAC,kBAAA;QACAC,gBAAA;MACA;MACAC,KAAA;QACA7B,IAAA,GACA;UAAA8B,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAtB,QAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACArB,MAAA,GACA;UAAAkB,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MAEAC,mBAAA;MACAC,gBAAA;MACAC,QAAA;MAEA;MACAC,SAAA,EAAAtC,KAAA,CAAAuC,QAAA,CAAAC,OAAA;MAEA;MACAC,SAAA;MAEA;MACAC,aAAA;QACAC,OAAA;UACAC,OAAA,GACA,2CACA,8BACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA;YAAA;UAAA,IACA;YAAA;UAAA,IACA;YAAA;UAAA,IACA,WACA;QAEA;QACAC,WAAA;QACAC,KAAA;MACA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,SAAA;MACA,OAAAC,YAAA,CAAAC,OAAA;IACA;IAEAC,cAAA;MACA;QACAC,aAAA,YAAAH,YAAA,CAAAC,OAAA;MACA;IACA;IAEA;IACAG,QAAA;MACA,OAAArD,KAAA,CAAAuC,QAAA,CAAAC,OAAA;IACA;IAEA;IACAc,iBAAA;MACA,YAAAvC,QAAA,GACA,eACA;IACA;EACA;EAEAwC,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,eAAA;IACA;IACAzC,MAAA,CAAA0C,gBAAA,gBAAAC,YAAA;EACA;EAEAC,cAAA;IACA;IACA5C,MAAA,CAAA6C,mBAAA,gBAAAF,YAAA;EACA;EAEAG,OAAA;IACA;IACA,MAAAN,UAAA;MACA;QACA,KAAApD,OAAA;QAEA,MAAA2D,MAAA;UACAC,IAAA,OAAAxD,WAAA;UACAyD,KAAA,OAAAxD,QAAA;UACAG,QAAA,OAAAD,MAAA,CAAAC,QAAA,IAAAsD,SAAA;UACArD,MAAA,OAAAF,MAAA,CAAAE,MAAA,IAAAqD,SAAA;UACApD,MAAA,OAAAH,MAAA,CAAAG,MAAA,IAAAoD;QACA;QAEA,MAAAC,QAAA,SAAAtE,YAAA,CAAAuE,aAAA,CAAAL,MAAA;QACA,KAAAzD,UAAA,GAAA6D,QAAA,CAAAhE,IAAA,CAAAkE,KAAA;QACA,KAAA9D,KAAA,GAAA4D,QAAA,CAAAhE,IAAA,CAAAI,KAAA;MACA,SAAA+D,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA,MAAArC,EAAA;MACA;QACA,KAAA7B,OAAA;MACA;IACA;IAEA;IACA,MAAAqD,gBAAA;MACA;QACA,MAAAU,QAAA,SAAArE,WAAA,CAAA2E,gBAAA;QACA,KAAA/D,UAAA,GAAAyD,QAAA,CAAAhE,IAAA,CAAAuE,GAAA,CAAAC,IAAA,IAAAA,IAAA,CAAA1E,IAAA;MACA,SAAAqE,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;MACA;IACA;IAEA;IACAM,mBAAA;MACA,KAAApE,WAAA;MACA,KAAAgD,SAAA;IACA;IAEA;IACAqB,iBAAAb,IAAA;MACA,KAAAxD,WAAA,GAAAwD,IAAA;MACA,KAAAR,SAAA;IACA;IAEA;IACAG,aAAA;MACA,KAAA5C,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IAEA;IACA6D,UAAA;MACA,KAAA3D,UAAA;MACA,KAAAC,IAAA;QACAC,EAAA;QACApB,IAAA;QACAW,QAAA;QACAU,KAAA;QACAC,QAAA;QACAV,MAAA;QACAW,WAAA;QACAC,UAAA;QACAC,cAAA;QACAC,UAAA;QACAC,kBAAA;QACAC,gBAAA;MACA;MACA,KAAAX,aAAA;IACA;IAEA;IACA6D,WAAAC,GAAA;MACA,KAAA7D,UAAA;MACA,KAAAC,IAAA;QAAA,GAAA4D;MAAA;MACA,KAAA9D,aAAA;IACA;IAEA;IACA+D,aAAAD,GAAA;MACA,KAAAE,QAAA,CACA,KAAAjD,EAAA,kCACA,KAAAA,EAAA,oBACA;QACAkD,iBAAA,OAAAlD,EAAA;QACAmD,gBAAA,OAAAnD,EAAA;QACAoD,IAAA;MACA,CACA,EAAAC,IAAA;QACA;UACA,KAAAlF,OAAA;UAEA,MAAAP,YAAA,CAAA0F,eAAA,CAAAP,GAAA,CAAA3D,EAAA;UAEA,KAAAmD,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;UACA,KAAAuB,SAAA;QACA,SAAAc,KAAA;UACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA,MAAArC,EAAA;QACA;UACA,KAAA7B,OAAA;QACA;MACA,GAAAqF,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAC,kBAAAV,GAAA;MACA,KAAA5C,gBAAA,GAAA4C,GAAA;MACA,KAAA3C,QAAA,GAAA2C,GAAA,CAAArD,UAAA;MACA,KAAAQ,mBAAA;IACA;IAEA;IACAwD,WAAA;MACA,KAAAC,KAAA,CAAAxE,IAAA,CAAAyE,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;QAEA;UACA,KAAAzF,UAAA;UAEA,SAAAc,UAAA;YACA;YACA,MAAAgD,QAAA,SAAAtE,YAAA,CAAAkG,eAAA,MAAA3E,IAAA;YACA,KAAAoD,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;UACA;YACA;YACA,MAAAkC,QAAA,SAAAtE,YAAA,CAAAmG,eAAA,MAAA5E,IAAA,CAAAC,EAAA,OAAAD,IAAA;YACA,KAAAoD,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;UACA;UAEA,KAAAf,aAAA;UACA,KAAAsC,SAAA;QACA,SAAAc,KAAA;UACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA,MAAArC,EAAA;QACA;UACA,KAAA5B,UAAA;QACA;MACA;IACA;IAEA;IACA4F,UAAA;MACA,SAAAL,KAAA,CAAAxE,IAAA;QACA,KAAAwE,KAAA,CAAAxE,IAAA,CAAA8E,WAAA;MACA;IACA;IAEA;IACAC,aAAAC,IAAA;MACA,MAAAC,OAAA,GAAAD,IAAA,CAAAf,IAAA,CAAAiB,UAAA;MACA,MAAAC,MAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,OAAA;QACA,KAAA7B,QAAA,CAAAF,KAAA,MAAArC,EAAA;QACA;MACA;MAEA,KAAAsE,MAAA;QACA,KAAA/B,QAAA,CAAAF,KAAA,MAAArC,EAAA,yBAAAwE,OAAA;QACA;MACA;MAEA;IACA;IAEA;IACAC,oBAAAvC,QAAA;MACA,KAAA/C,IAAA,CAAAO,UAAA,GAAAwC,QAAA,CAAAhE,IAAA,CAAAwG,SAAA;MACA,KAAAnC,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;IACA;IAEA;IACA2E,yBAAAzC,QAAA;MACA,KAAA9B,QAAA,GAAA8B,QAAA,CAAAhE,IAAA,CAAAwG,SAAA;MACA,KAAAvE,gBAAA,CAAAT,UAAA,GAAAwC,QAAA,CAAAhE,IAAA,CAAAwG,SAAA;;MAEA;MACA,MAAAE,KAAA,QAAAvG,UAAA,CAAAwG,SAAA,CAAAnC,IAAA,IAAAA,IAAA,CAAAtD,EAAA,UAAAe,gBAAA,CAAAf,EAAA;MACA,IAAAwF,KAAA;QACA,KAAAE,IAAA,MAAAzG,UAAA,EAAAuG,KAAA;UAAA,QAAAzE;QAAA;MACA;MAEA,KAAAoC,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;;MAEA;MACA+E,UAAA;QACA,KAAA7E,mBAAA;MACA;IACA;IAEA;IACA8E,kBAAA3C,KAAA,EAAA8B,IAAA;MACA7B,OAAA,CAAAD,KAAA,YAAAA,KAAA;MACAC,OAAA,CAAA2C,GAAA,UAAAd,IAAA;QACAnG,IAAA,EAAAmG,IAAA,CAAAnG,IAAA;QACAuG,IAAA,EAAAJ,IAAA,CAAAI,IAAA;QACAnB,IAAA,EAAAe,IAAA,CAAAf,IAAA;QACA8B,YAAA,EAAAf,IAAA,CAAAe;MACA;;MAEA;MACA,IAAAC,YAAA,QAAAnF,EAAA;MACA,IAAAqC,KAAA,CAAAH,QAAA,IAAAG,KAAA,CAAAH,QAAA,CAAAhE,IAAA;QACAoE,OAAA,CAAAD,KAAA,YAAAA,KAAA,CAAAH,QAAA,CAAAhE,IAAA;QACA,IAAAmE,KAAA,CAAAH,QAAA,CAAAhE,IAAA,CAAAkH,MAAA;UACAD,YAAA,WAAA9C,KAAA,CAAAH,QAAA,CAAAhE,IAAA,CAAAkH,MAAA;QACA;MACA;MAEA,KAAA7C,QAAA,CAAAF,KAAA,CAAA8C,YAAA;IACA;IAEA;IACAE,sBAAA;MACA;MACA,SAAAlG,IAAA,CAAAM,cAAA;QACA;QACA,IAAA6F,OAAA;;QAEA;QACA,SAAAnG,IAAA,CAAAM,cAAA,CAAA8F,QAAA,wBAAApG,IAAA,CAAAM,cAAA,CAAA8F,QAAA;UACA;UACA,MAAAC,KAAA,QAAArG,IAAA,CAAAM,cAAA,CAAA+F,KAAA;UACA,IAAAA,KAAA,IAAAA,KAAA;YACAF,OAAA,GAAAE,KAAA;UACA;QACA,gBAAArG,IAAA,CAAAM,cAAA,CAAA8F,QAAA;UACA;UACA,MAAAC,KAAA,QAAArG,IAAA,CAAAM,cAAA,CAAA+F,KAAA;UACA,IAAAA,KAAA,IAAAA,KAAA;YACAF,OAAA,GAAAE,KAAA,IAAAhB,OAAA;UACA;QACA;;QAEA;QACA,IAAAc,OAAA;UACA,aAAA9E,SAAA;YACA;cACA,KAAArB,IAAA,CAAAM,cAAA,oCAAA6F,OAAA;cACA;YACA;cACA,KAAAnG,IAAA,CAAAM,cAAA,qDAAA6F,OAAA;cACA;YACA;cACA;cACA;UACA;QACA;MACA;IACA;IAEA;IACAG,gBAAAC,GAAA;MACA,KAAAA,GAAA;;MAEA;MACA,IAAAA,GAAA,CAAArB,UAAA,eAAAqB,GAAA,CAAArB,UAAA;QACA,OAAAqB,GAAA;MACA;;MAEA;MACA,IAAAA,GAAA,CAAArB,UAAA;QACA,YAAAjD,OAAA,GAAAsE,GAAA;MACA;;MAEA;MACA,YAAAtE,OAAA,SAAAsE,GAAA;IACA;IAEA;IACAC,oBAAA;MACA,KAAAhC,KAAA,CAAAiC,eAAA,CAAAC,KAAA;IACA;IAEA;IACA,MAAAC,uBAAAC,KAAA;MACA,MAAA5B,IAAA,GAAA4B,KAAA,CAAAC,MAAA,CAAAC,KAAA;MACA,KAAA9B,IAAA;;MAEA;MACA,MAAAC,OAAA,GAAAD,IAAA,CAAAf,IAAA,CAAAiB,UAAA;MACA,MAAAC,MAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,OAAA;QACA,KAAA7B,QAAA,CAAAF,KAAA,MAAArC,EAAA;QACA;MACA;MAEA,KAAAsE,MAAA;QACA,KAAA/B,QAAA,CAAAF,KAAA,MAAArC,EAAA,yBAAAwE,OAAA;QACA;MACA;;MAEA;MACA,MAAA0B,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAjC,IAAA;MAEA;QACA,KAAAhG,OAAA;;QAEA;QACA,MAAA+D,QAAA,SAAAnE,KAAA,CAAAsI,IAAA,MAAAhG,SAAA,EAAA6F,QAAA;UACAI,OAAA;YACA;YACA,2BAAAtF,YAAA,CAAAC,OAAA;UACA;QACA;;QAEA;QACA,KAAA9B,IAAA,CAAAO,UAAA,GAAAwC,QAAA,CAAAhE,IAAA,CAAAA,IAAA,CAAAwG,SAAA;QACA,KAAAnC,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;MACA,SAAAqC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAC,OAAA,CAAA2C,GAAA;UACAjH,IAAA,EAAAmG,IAAA,CAAAnG,IAAA;UACAuG,IAAA,EAAAJ,IAAA,CAAAI,IAAA;UACAnB,IAAA,EAAAe,IAAA,CAAAf,IAAA;UACA8B,YAAA,EAAAf,IAAA,CAAAe;QACA;;QAEA;QACA,IAAAC,YAAA,QAAAnF,EAAA;QACA,IAAAqC,KAAA,CAAAH,QAAA,IAAAG,KAAA,CAAAH,QAAA,CAAAhE,IAAA;UACAoE,OAAA,CAAAD,KAAA,YAAAA,KAAA,CAAAH,QAAA,CAAAhE,IAAA;UACA,IAAAmE,KAAA,CAAAH,QAAA,CAAAhE,IAAA,CAAAkH,MAAA;YACAD,YAAA,WAAA9C,KAAA,CAAAH,QAAA,CAAAhE,IAAA,CAAAkH,MAAA;UACA;QACA;QAEA,KAAA7C,QAAA,CAAAF,KAAA,CAAA8C,YAAA;MACA;QACA,KAAAhH,OAAA;QACA;QACA,KAAAwF,KAAA,CAAAiC,eAAA,CAAAW,KAAA;MACA;IACA;IAEA;IACAC,0BAAA;MACA,KAAA7C,KAAA,CAAA8C,qBAAA,CAAAZ,KAAA;IACA;IAEA;IACAa,qBAAA3D,GAAA;MACAT,OAAA,CAAA2C,GAAA,kBAAAlC,GAAA;MACA;MACA,IAAAA,GAAA,IAAAA,GAAA,CAAArD,UAAA;QACA;QACA,KAAAoF,IAAA,CAAA/B,GAAA;MACA;IACA;IAEA;IACA,MAAA4D,6BAAAZ,KAAA;MACA,MAAA5B,IAAA,GAAA4B,KAAA,CAAAC,MAAA,CAAAC,KAAA;MACA,KAAA9B,IAAA;;MAEA;MACA,MAAAC,OAAA,GAAAD,IAAA,CAAAf,IAAA,CAAAiB,UAAA;MACA,MAAAC,MAAA,GAAAH,IAAA,CAAAI,IAAA;MAEA,KAAAH,OAAA;QACA,KAAA7B,QAAA,CAAAF,KAAA,MAAArC,EAAA;QACA;MACA;MAEA,KAAAsE,MAAA;QACA,KAAA/B,QAAA,CAAAF,KAAA,MAAArC,EAAA,yBAAAwE,OAAA;QACA;MACA;;MAEA;MACA,MAAA0B,QAAA,OAAAC,QAAA;MACAD,QAAA,CAAAE,MAAA,SAAAjC,IAAA;MACA,SAAAhE,gBAAA,CAAAf,EAAA;QACA8G,QAAA,CAAAE,MAAA,sBAAAjG,gBAAA,CAAAf,EAAA;MACA;MAEA;QACA,KAAAjB,OAAA;;QAEA;QACA,MAAA+D,QAAA,SAAAnE,KAAA,CAAAsI,IAAA,MAAAhG,SAAA,EAAA6F,QAAA;UACAI,OAAA;YACA;YACA,2BAAAtF,YAAA,CAAAC,OAAA;UACA;QACA;;QAEA;QACA,KAAAb,QAAA,GAAA8B,QAAA,CAAAhE,IAAA,CAAAA,IAAA,CAAAwG,SAAA;QACA,KAAAvE,gBAAA,CAAAT,UAAA,GAAAwC,QAAA,CAAAhE,IAAA,CAAAA,IAAA,CAAAwG,SAAA;;QAEA;QACA,MAAAE,KAAA,QAAAvG,UAAA,CAAAwG,SAAA,CAAAnC,IAAA,IAAAA,IAAA,CAAAtD,EAAA,UAAAe,gBAAA,CAAAf,EAAA;QACA,IAAAwF,KAAA;UACA,KAAAE,IAAA,MAAAzG,UAAA,EAAAuG,KAAA;YAAA,QAAAzE;UAAA;QACA;QAEA,KAAAoC,QAAA,CAAAgB,OAAA,MAAAvD,EAAA;;QAEA;QACA+E,UAAA;UACA,KAAA7E,mBAAA;QACA;MACA,SAAAmC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACAC,OAAA,CAAA2C,GAAA;UACAjH,IAAA,EAAAmG,IAAA,CAAAnG,IAAA;UACAuG,IAAA,EAAAJ,IAAA,CAAAI,IAAA;UACAnB,IAAA,EAAAe,IAAA,CAAAf,IAAA;UACA8B,YAAA,EAAAf,IAAA,CAAAe;QACA;;QAEA;QACA,IAAAC,YAAA,QAAAnF,EAAA;QACA,IAAAqC,KAAA,CAAAH,QAAA,IAAAG,KAAA,CAAAH,QAAA,CAAAhE,IAAA;UACAoE,OAAA,CAAAD,KAAA,YAAAA,KAAA,CAAAH,QAAA,CAAAhE,IAAA;UACA,IAAAmE,KAAA,CAAAH,QAAA,CAAAhE,IAAA,CAAAkH,MAAA;YACAD,YAAA,WAAA9C,KAAA,CAAAH,QAAA,CAAAhE,IAAA,CAAAkH,MAAA;UACA;QACA;QAEA,KAAA7C,QAAA,CAAAF,KAAA,CAAA8C,YAAA;MACA;QACA,KAAAhH,OAAA;QACA;QACA,KAAAwF,KAAA,CAAA8C,qBAAA,CAAAF,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}