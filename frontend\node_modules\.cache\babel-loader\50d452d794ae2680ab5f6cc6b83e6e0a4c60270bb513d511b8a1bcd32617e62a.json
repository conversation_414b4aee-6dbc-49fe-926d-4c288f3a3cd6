{"ast": null, "code": "import { reservationApi } from '@/api';\nimport { isReservationExpired } from '@/utils/date';\nexport default {\n  name: 'AdminReservationDetail',\n  data() {\n    return {\n      loading: false,\n      submitting: false,\n      modifying: false,\n      reservation: null,\n      cancelDialogVisible: false,\n      returnDialogVisible: false,\n      modifyDialogVisible: false,\n      // 历史记录相关\n      historyDialogVisible: false,\n      historyRecords: [],\n      loadingHistory: false,\n      // 强制显示状态值 - 用于覆盖计算属性的显示\n      forcedStatusText: null,\n      forcedStatusType: null,\n      // 添加日期匹配标志\n      dateMatches: false,\n      // 用于记录状态变更\n      statusUpdated: false,\n      forcedStatus: null,\n      // 添加特定预约状态缓存标识\n      reservationStatusCacheKey: '',\n      // 修改预定表单数据\n      modifyForm: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      // 修改预定表单验证规则\n      modifyRules: {\n        startDateTime: [{\n          required: true,\n          message: '请选择开始时间',\n          trigger: 'change'\n        }],\n        endDateTime: [{\n          required: true,\n          message: '请选择结束时间',\n          trigger: 'change'\n        }],\n        userEmail: [{\n          required: true,\n          message: '请输入邮箱',\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: '请输入正确的邮箱格式',\n          trigger: 'blur'\n        }]\n      },\n      // 日期时间选择器配置\n      dateTimePickerOptions: {\n        disabledDate: time => {\n          return time.getTime() < Date.now() - 8.64e7; // 8.64e7是一天的毫秒数\n        }\n      }\n    };\n  },\n  created() {\n    this.fetchReservation();\n\n    // 注册页面刷新事件监听器\n    window.addEventListener('beforeunload', this.saveState);\n  },\n  destroyed() {\n    // 移除事件监听器，避免内存泄漏\n    window.removeEventListener('beforeunload', this.saveState);\n  },\n  mounted() {\n    this.loadReservation();\n  },\n  // 监听路由参数变化，当路由参数变化时重新获取数据\n  watch: {\n    '$route': {\n      handler: function (to, from) {\n        // 如果路由参数发生变化，重新获取数据\n        if (to.params.code !== from.params.code || to.query.reservationNumber !== from.query.reservationNumber) {\n          console.log('路由参数变化，重新获取数据');\n          this.fetchReservation();\n        }\n      },\n      deep: true\n    }\n  },\n  // 在路由参数变化时调用\n  beforeRouteUpdate(to, from, next) {\n    console.log('路由参数更新，重新获取数据');\n    this.fetchReservation();\n    next();\n  },\n  computed: {\n    // 判断预约是否已开始\n    isReservationStarted() {\n      if (!this.reservation) return false;\n      const now = new Date();\n      const startTime = new Date(this.reservation.start_datetime);\n      return now >= startTime;\n    },\n    getStatusTagText() {\n      if (!this.reservation) return '';\n\n      // 尝试恢复存储的状态\n      const savedState = this.getSavedState();\n      if (savedState && savedState.statusText) {\n        console.log('Using saved status text:', savedState.statusText);\n        return savedState.statusText;\n      }\n\n      // 调用方法获取状态文本\n      const statusText = this.getStatusText(this.reservation);\n      console.log('Computed status text:', statusText);\n      return statusText;\n    },\n    getStatusTagType() {\n      if (!this.reservation) return '';\n\n      // 尝试恢复存储的状态\n      const savedState = this.getSavedState();\n      if (savedState && savedState.statusType) {\n        console.log('Using saved status type:', savedState.statusType);\n        return savedState.statusType;\n      }\n\n      // 调用方法获取状态类型\n      const statusType = this.getStatusType(this.reservation);\n      console.log('Computed status type:', statusType);\n      return statusType;\n    },\n    // 获取显示的状态文本\n    displayStatusText() {\n      // 如果有URL传递的状态参数，最优先使用它\n      if (this.$route.query.displayStatus) {\n        console.log('使用URL传递的状态文本:', this.$route.query.displayStatus);\n        return this.$route.query.displayStatus;\n      }\n\n      // 次高优先级：使用强制状态（针对操作后立即更新）\n      if (this.forcedStatusText) {\n        console.log('使用强制状态文本:', this.forcedStatusText);\n        return this.forcedStatusText;\n      }\n\n      // 再次优先级：使用本地存储的状态\n      const savedState = this.getSavedState();\n      if (savedState && savedState.statusText) {\n        console.log('使用本地存储的状态文本:', savedState.statusText);\n        return savedState.statusText;\n      }\n\n      // 最低优先级：动态计算状态（实时计算）\n      if (!this.reservation) return '';\n\n      // 在这里添加实时计算逻辑，确保已过期和使用中状态能立即更新\n      // 检查是否已过期\n      const now = new Date();\n      const endTime = new Date(this.reservation.end_datetime);\n      if (endTime < now) {\n        console.log('实时计算：预定已过期');\n        return this.$t('reservation.expired');\n      }\n\n      // 检查是否使用中\n      const startTime = new Date(this.reservation.start_datetime);\n      if (now >= startTime && now <= endTime) {\n        console.log('实时计算：预定使用中');\n        return this.$t('reservation.inUse');\n      }\n\n      // 默认为已确认，但如果数据库中确实标记为已取消，则显示取消状态\n      if (this.reservation.status === 'cancelled') {\n        console.log('数据库标记为已取消');\n        return this.$t('reservation.cancelled');\n      }\n      console.log('实时计算：预定已确认');\n      return this.$t('reservation.confirmed');\n    },\n    // 获取显示的状态类型（用于样式）\n    displayStatusType() {\n      // 如果有URL传递的状态类型参数，最优先使用它\n      if (this.$route.query.displayStatusType) {\n        console.log('使用URL传递的状态类型:', this.$route.query.displayStatusType);\n        return this.$route.query.displayStatusType;\n      }\n\n      // 次高优先级：使用强制状态类型（针对操作后立即更新）\n      if (this.forcedStatusType) {\n        console.log('使用强制状态类型:', this.forcedStatusType);\n        return this.forcedStatusType;\n      }\n\n      // 再次优先级：使用本地存储的状态\n      const savedState = this.getSavedState();\n      if (savedState && savedState.statusType) {\n        console.log('使用本地存储的状态类型:', savedState.statusType);\n        return savedState.statusType;\n      }\n\n      // 最低优先级：动态计算状态类型（实时计算）\n      if (!this.reservation) return '';\n\n      // 实时计算逻辑，与状态文本保持一致\n      const now = new Date();\n      const endTime = new Date(this.reservation.end_datetime);\n      if (endTime < now) {\n        return 'warning';\n      }\n      const startTime = new Date(this.reservation.start_datetime);\n      if (now >= startTime && now <= endTime) {\n        return 'primary';\n      }\n\n      // 如果数据库中确实标记为已取消，则显示取消状态类型\n      if (this.reservation.status === 'cancelled') {\n        return 'danger';\n      }\n      return 'success';\n    },\n    formattedStartTime() {\n      if (!this.reservation) return '';\n      return this.formatDateTime(this.reservation.start_datetime);\n    },\n    formattedEndTime() {\n      if (!this.reservation) return '';\n      return this.formatDateTime(this.reservation.end_datetime);\n    },\n    // 处理历史记录，按时间分组并过滤掉不需要显示的字段\n    processedHistoryRecords() {\n      if (!this.historyRecords || this.historyRecords.length === 0) {\n        return [];\n      }\n\n      // 过滤掉 lang 字段的修改记录\n      const filteredRecords = this.historyRecords.filter(record => record.field_name !== 'lang');\n\n      // 按照修改时间分组\n      const groupedRecords = {};\n      filteredRecords.forEach(record => {\n        const timestamp = record.created_at;\n        if (!groupedRecords[timestamp]) {\n          groupedRecords[timestamp] = {\n            timestamp: timestamp,\n            user_type: record.user_type,\n            user_id: record.user_id,\n            records: []\n          };\n        }\n        groupedRecords[timestamp].records.push(record);\n      });\n\n      // 转换为数组并按时间倒序排序\n      return Object.values(groupedRecords).sort((a, b) => {\n        return new Date(b.timestamp) - new Date(a.timestamp);\n      });\n    }\n  },\n  methods: {\n    isReservationExpired,\n    async fetchReservation() {\n      this.loading = true;\n      try {\n        const code = this.$route.params.code;\n        console.log('Fetching reservation with code:', code);\n\n        // 获取URL中的查询参数（用于时间和状态）\n        const startTime = this.$route.query.startTime;\n        const endTime = this.$route.query.endTime;\n        let reservationNumber = this.$route.query.reservationNumber;\n\n        // 如果URL中没有预约序号，尝试从localStorage中获取\n        if (!reservationNumber) {\n          const savedReservationNumber = localStorage.getItem('current_reservation_number');\n          if (savedReservationNumber) {\n            console.log('从localStorage中获取预约序号:', savedReservationNumber);\n            reservationNumber = savedReservationNumber;\n          }\n        }\n\n        // 检查是否强制使用预约序号查询\n        const forceUseReservationNumber = localStorage.getItem('force_use_reservation_number');\n        if (forceUseReservationNumber === 'true') {\n          console.log('强制使用预约序号查询');\n          // 使用后清除标记\n          localStorage.removeItem('force_use_reservation_number');\n        }\n        console.log('URL 时间参数:', startTime, endTime);\n        console.log('预约序号:', reservationNumber);\n\n        // 构建API请求参数\n        let params = {};\n\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime();\n        params._t = timestamp;\n        console.log('添加时间戳参数:', timestamp);\n\n        // 只有当同时提供了开始和结束时间才添加到请求参数中\n        if (startTime && endTime) {\n          params.start_time = startTime;\n          params.end_time = endTime;\n          console.log('Including time parameters in API request:', params);\n        }\n\n        // 添加预约序号参数\n        if (reservationNumber) {\n          params.reservation_number = reservationNumber;\n          console.log('Including reservation number in API request:', params);\n        }\n\n        // 如果有预约序号，优先使用预约序号查询\n        if (reservationNumber) {\n          console.log('使用预约序号查询:', reservationNumber);\n          try {\n            const response = await reservationApi.getReservationByNumber(reservationNumber);\n            console.log('通过预约序号查询结果:', response);\n\n            // 如果通过预约序号查询成功，直接使用结果\n            if (response.data && response.data.success) {\n              console.log('通过预约序号查询成功');\n\n              // 获取原始数据\n              this.reservation = response.data.data;\n              console.log('Original reservation data:', this.reservation);\n\n              // 检查是否需要显示预约序号通知\n              const showNotification = localStorage.getItem('show_reservation_number_notification');\n              if (showNotification === 'true') {\n                // 显示预约序号信息\n                this.$notify({\n                  title: '预约详情',\n                  message: `当前查看的是预约序号: ${this.reservation.reservation_number}`,\n                  type: 'info',\n                  duration: 5000\n                });\n                // 使用后清除标记\n                localStorage.removeItem('show_reservation_number_notification');\n              }\n\n              // 重要：从URL参数中获取时间覆盖预约显示时间（这确保显示的时间与列表页一致）\n              if (startTime && endTime) {\n                console.log('使用URL参数覆盖显示时间 - 原始时间:', this.reservation.start_datetime, this.reservation.end_datetime);\n\n                // 保存原始时间以备后用\n                this.originalStartTime = this.reservation.start_datetime;\n                this.originalEndTime = this.reservation.end_datetime;\n\n                // 覆盖显示时间\n                this.reservation.start_datetime = startTime;\n                this.reservation.end_datetime = endTime;\n                console.log('覆盖后的显示时间:', this.reservation.start_datetime, this.reservation.end_datetime);\n              }\n\n              // 添加详细日志，帮助调试状态判断\n              console.log('Status from API:', this.reservation.status);\n              console.log('Current time:', new Date());\n\n              // 确保状态字段正确\n              if (!this.reservation.status) {\n                // 如果API返回的状态为空，默认设置为confirmed\n                console.warn('API returned empty status, setting default to confirmed');\n                this.reservation.status = 'confirmed';\n              }\n\n              // 重要：确保状态字段是正确的\n              console.log(`最终数据库状态: ${this.reservation.status}，展示状态为: ${this.getStatusText(this.reservation)}`);\n              this.loading = false;\n              return;\n            } else {\n              console.warn('通过预约序号查询失败，将使用预约码查询');\n            }\n          } catch (error) {\n            console.error('通过预约序号查询出错:', error);\n            console.warn('将使用预约码查询');\n          }\n        }\n\n        // 使用API进行请求 - 注意：getReservationByCode的第二个参数应该是预约序号，不是params对象\n        console.log('Calling API with code and reservationNumber:', code, reservationNumber);\n        const response = await reservationApi.getReservationByCode(code, reservationNumber);\n        console.log('API Response:', response);\n        if (response.data && response.data.success) {\n          // 获取原始数据\n          this.reservation = response.data.data;\n          console.log('Original reservation data:', this.reservation);\n\n          // 显示预约序号信息（无论通过何种方式查询）\n          if (this.reservation.reservation_number) {\n            this.$notify({\n              title: '预约详情',\n              message: `当前查看的是预约序号: ${this.reservation.reservation_number}`,\n              type: 'info',\n              duration: 5000\n            });\n          }\n\n          // 重要：从URL参数中获取时间覆盖预约显示时间（这确保显示的时间与列表页一致）\n          if (startTime && endTime) {\n            console.log('使用URL参数覆盖显示时间 - 原始时间:', this.reservation.start_datetime, this.reservation.end_datetime);\n\n            // 保存原始时间以备后用\n            this.originalStartTime = this.reservation.start_datetime;\n            this.originalEndTime = this.reservation.end_datetime;\n\n            // 覆盖显示时间\n            this.reservation.start_datetime = startTime;\n            this.reservation.end_datetime = endTime;\n            console.log('覆盖后的显示时间:', this.reservation.start_datetime, this.reservation.end_datetime);\n          }\n\n          // 添加详细日志，帮助调试状态判断\n          console.log('Status from API:', this.reservation.status);\n          console.log('Current time:', new Date());\n\n          // 确保状态字段正确\n          if (!this.reservation.status) {\n            // 如果API返回的状态为空，默认设置为confirmed\n            console.warn('API returned empty status, setting default to confirmed');\n            this.reservation.status = 'confirmed';\n          }\n\n          // 重要：现在只检查当前预约序号对应的缓存状态，不再通用应用\n          if (this.reservation.reservation_number) {\n            // 使用当前预约序号创建缓存键\n            const cacheKey = `reservation_status_${this.reservation.reservation_number}`;\n            const cachedStatus = localStorage.getItem(cacheKey);\n            if (cachedStatus) {\n              try {\n                const statusData = JSON.parse(cachedStatus);\n                console.log('找到预约序号缓存状态:', statusData);\n\n                // 验证是否为当前预约序号的状态\n                if (statusData.reservationNumber === this.reservation.reservation_number) {\n                  // 如果缓存中标记为已取消，则覆盖API返回的状态\n                  if (statusData.dbStatus === 'cancelled' || statusData.forcedStatus === 'cancelled') {\n                    console.log('使用缓存中的已取消状态覆盖API返回状态，仅适用于预约序号:', this.reservation.reservation_number);\n                    this.reservation.status = 'cancelled';\n                    this.forcedStatus = 'cancelled';\n                    this.forcedStatusText = this.$t('reservation.cancelled');\n                    this.forcedStatusType = 'danger';\n                  }\n                } else {\n                  console.log('缓存状态预约序号不匹配，不应用缓存状态');\n                }\n              } catch (e) {\n                console.error('解析缓存状态出错:', e);\n              }\n            }\n          }\n\n          // 重要：确保状态字段是正确的\n          console.log(`最终数据库状态: ${this.reservation.status}，展示状态为: ${this.getStatusText(this.reservation)}`);\n        } else {\n          const errorMsg = response.data ? response.data.message : this.$t('reservation.reservationNotFound');\n          this.$message.error(errorMsg);\n          this.reservation = null;\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservation:', error);\n        this.$message.error(this.$t('error.serverError'));\n        this.reservation = null;\n      } finally {\n        this.loading = false;\n      }\n    },\n    async loadReservation() {\n      try {\n        this.loading = true;\n        // 获取预约码\n        const code = this.$route.params.code;\n        if (!code) {\n          this.error = '预约码不存在';\n          this.loading = false;\n          return;\n        }\n\n        // 构建查询参数，确保传递日期时间参数\n        const params = {};\n\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime();\n        params._t = timestamp;\n        console.log('添加时间戳参数:', timestamp);\n\n        // 从URL获取日期时间参数\n        const startTime = this.$route.query.startTime;\n        const endTime = this.$route.query.endTime;\n        if (startTime) {\n          params.start_time = startTime;\n          console.log('查询开始时间:', startTime);\n        }\n        if (endTime) {\n          params.end_time = endTime;\n          console.log('查询结束时间:', endTime);\n        }\n\n        // 获取预约信息 - 使用新的API方法来正确处理查询参数\n        let response;\n\n        // 检查是否有预约序号参数\n        const reservationNumber = this.$route.query.reservationNumber;\n        if (reservationNumber) {\n          console.log('使用预约序号查询:', reservationNumber);\n          // 使用预约序号查询\n          response = await this.$api.reservation.getReservationByCode(code, reservationNumber);\n        } else if (startTime || endTime) {\n          console.log('使用时间参数查询:', {\n            start_time: startTime,\n            end_time: endTime\n          });\n          // 使用时间参数查询\n          response = await this.$api.reservation.getReservationByCodeWithParams(code, params);\n        } else {\n          console.log('使用预约码查询，不传递额外参数');\n          // 只使用预约码查询\n          response = await this.$api.reservation.getReservationByCode(code);\n        }\n        console.log('预约API响应:', response);\n        if (response.success) {\n          this.reservation = response.data;\n\n          // 设置日期匹配标志\n          this.dateMatches = !!response.data.date_matches;\n          console.log('日期是否匹配:', this.dateMatches);\n\n          // 设置预约状态信息\n          if (this.reservation.status === 'confirmed') {\n            this.statusText = this.$t('reservation.confirmed');\n            this.statusType = 'success';\n          } else if (this.reservation.status === 'cancelled') {\n            this.statusText = this.$t('reservation.cancelled');\n            this.statusType = 'danger';\n\n            // 如果日期匹配且状态为cancelled，强制使用取消状态\n            if (this.dateMatches) {\n              this.forcedStatus = 'cancelled';\n              console.log('强制使用取消状态');\n            }\n          }\n\n          // 如果有设备ID，获取设备详情\n          if (this.reservation.equipment_id) {\n            this.loadEquipment(this.reservation.equipment_id);\n          }\n        } else {\n          this.error = response.message || '获取预约信息失败';\n        }\n      } catch (error) {\n        console.error('加载预约详情出错:', error);\n        this.error = '加载预约详情出错: ' + (error.message || error);\n      } finally {\n        this.loading = false;\n      }\n    },\n    formatDateTime(dateString) {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n    // 获取状态文本\n    getStatusText(reservation) {\n      // 首先检查状态是否直接来自API响应\n      console.log('Checking status from API:', reservation.status);\n\n      // 如果API明确返回了cancelled状态，显示已取消\n      if (reservation.status === 'cancelled') {\n        console.log('Using cancelled status from API');\n        return this.$t('reservation.cancelled');\n      }\n\n      // 其他状态根据时间动态计算\n      if (isReservationExpired(reservation.end_datetime)) {\n        console.log('Calculated status: expired');\n        return this.$t('reservation.expired');\n      }\n\n      // 如果预约正在进行中，显示\"使用中\"\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        console.log('Calculated status: in use');\n        return this.$t('reservation.inUse');\n      }\n\n      // 如果预约已确认且未开始，显示\"已确认\"\n      console.log('Calculated status: confirmed');\n      return this.$t('reservation.confirmed');\n    },\n    // 获取状态类型（样式）\n    getStatusType(reservation) {\n      // 首先检查状态是否直接来自API响应\n      if (reservation.status === 'cancelled') {\n        return 'danger';\n      }\n\n      // 如果预约已过期，返回橙色\n      if (isReservationExpired(reservation.end_datetime)) {\n        return 'warning';\n      }\n\n      // 如果预约正在进行中，返回蓝色\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        return 'primary';\n      }\n\n      // 如果预约已确认且未开始，返回绿色\n      return 'success';\n    },\n    // 判断预约是否正在进行中\n    isReservationInProgress(reservation) {\n      if (!reservation) return false;\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n\n      // 当前时间在开始时间和结束时间之间\n      return now >= start && now <= end;\n    },\n    // 处理修改预约按钮点击\n    handleModify() {\n      // 初始化修改表单数据\n      this.modifyForm = {\n        startDateTime: this.reservation.start_datetime,\n        endDateTime: this.reservation.end_datetime,\n        purpose: this.reservation.purpose || '',\n        userEmail: this.reservation.user_email || ''\n      };\n\n      // 显示修改对话框\n      this.modifyDialogVisible = true;\n    },\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyForm.startDateTime);\n      const endTime = new Date(this.modifyForm.endDateTime);\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'));\n        return false;\n      }\n      return true;\n    },\n    // 提交修改表单\n    async submitModifyForm() {\n      try {\n        // 验证表单\n        await this.$refs.modifyForm.validate();\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return;\n        this.modifying = true;\n\n        // 构建更新数据\n        const updateData = {\n          start_datetime: this.modifyForm.startDateTime,\n          end_datetime: this.modifyForm.endDateTime,\n          purpose: this.modifyForm.purpose || undefined,\n          user_email: this.modifyForm.userEmail || undefined,\n          lang: this.$i18n.locale\n        };\n\n        // 调用更新API - 传递预约序号以确保修改正确的子预约\n        const response = await reservationApi.updateReservation(this.reservation.reservation_code, updateData, this.reservation.reservation_number // 传递预约序号\n        );\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.updateSuccess'));\n          this.modifyDialogVisible = false;\n          // 重新获取预定信息\n          await this.fetchReservation();\n\n          // 设置强制刷新标记，以便在返回列表页时刷新数据\n          localStorage.setItem('force_refresh_reservation_list', 'true');\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.updateFailed'));\n        }\n      } catch (error) {\n        console.error('修改预约失败:', error);\n        this.$message.error(this.$t('error.serverError'));\n      } finally {\n        this.modifying = false;\n      }\n    },\n    handleCancel() {\n      this.cancelDialogVisible = true;\n    },\n    handleReturn() {\n      this.returnDialogVisible = true;\n    },\n    async confirmCancel() {\n      this.submitting = true;\n      try {\n        // 检查是否是循环预约的子预约\n        if (this.reservation.recurring_reservation_id) {\n          console.log('Cancelling a child reservation of recurring reservation:', this.reservation.recurring_reservation_id);\n\n          // 获取当前预约的详细信息\n          const reservationCode = this.reservation.reservation_code;\n          const reservationNumber = this.reservation.reservation_number;\n\n          // 准备取消请求数据\n          const data = {};\n\n          // 添加预约序号参数，确保只取消特定的子预约\n          if (reservationNumber) {\n            data.reservation_number = reservationNumber;\n            console.log('循环子预约取消 - 预约序号参数存在:', reservationNumber);\n          } else {\n            console.warn('循环子预约取消 - 预约序号参数不存在，将取消所有具有相同预约码的预约');\n          }\n\n          // 取消单个子预约\n          const response = await reservationApi.cancelReservation(reservationCode, data);\n          console.log('Cancel child reservation response:', response);\n\n          // 无论API响应成功与否，检查返回消息以确定实际状态\n          if (response.data) {\n            // 特殊处理：如果消息表明预定已经取消，视为成功\n            if (response.data.message === '预定已取消' || response.data.message.includes('已取消')) {\n              console.log('预定已经处于取消状态，视为取消成功');\n\n              // 关闭取消对话框\n              this.cancelDialogVisible = false;\n\n              // 立即更新预约状态，不等待API重新获取\n              this.reservation.status = 'cancelled';\n\n              // 立即更新UI显示的状态\n              this.forceUpdateStatus('cancelled');\n\n              // 关键改进：同时保存预约序号的状态\n              if (reservationNumber) {\n                this.saveStatusByReservationNumber(reservationNumber, 'cancelled');\n              }\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'));\n\n              // 提示用户返回循环预约详情页面\n              this.$confirm('已成功取消此子预约。是否查看循环预约详情？', '操作成功', {\n                confirmButtonText: '查看循环预约',\n                cancelButtonText: '留在当前页面',\n                type: 'success'\n              }).then(() => {\n                // 跳转到循环预约详情页面\n                this.$router.push(`/recurring-reservation/${this.reservation.recurring_reservation_id}`);\n              }).catch(() => {\n                // 用户选择留在当前页面，直接重新获取预定信息\n                this.fetchReservation();\n              });\n              return;\n            } else if (response.data.success) {\n              // 常规成功响应处理\n              // 关闭取消对话框\n              this.cancelDialogVisible = false;\n\n              // 立即更新预约状态，不等待API重新获取\n              this.reservation.status = 'cancelled';\n\n              // 立即更新UI显示的状态\n              this.forceUpdateStatus('cancelled');\n\n              // 关键改进：同时保存预约序号的状态\n              if (reservationNumber) {\n                this.saveStatusByReservationNumber(reservationNumber, 'cancelled');\n              }\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'));\n\n              // 提示用户返回循环预约详情页面\n              this.$confirm('已成功取消此子预约。是否查看循环预约详情？', '操作成功', {\n                confirmButtonText: '查看循环预约',\n                cancelButtonText: '留在当前页面',\n                type: 'success'\n              }).then(() => {\n                // 跳转到循环预约详情页面\n                this.$router.push(`/recurring-reservation/${this.reservation.recurring_reservation_id}`);\n              }).catch(() => {\n                // 用户选择留在当前页面，直接重新获取预定信息\n                this.fetchReservation();\n              });\n            } else {\n              // 真正的错误消息\n              const errorMsg = response.data.message || this.$t('reservation.cancelFailed');\n              this.$message.error(errorMsg);\n              // 关闭取消对话框\n              this.cancelDialogVisible = false;\n            }\n          }\n        } else {\n          // 普通预约的取消逻辑\n          // 添加预约序号参数，确保只取消特定的子预约\n          const data = {};\n\n          // 添加预约序号参数，确保只取消特定的子预约\n          if (this.reservation.reservation_number) {\n            data.reservation_number = this.reservation.reservation_number;\n            console.log('预约序号参数存在:', this.reservation.reservation_number);\n          } else {\n            console.warn('预约序号参数不存在，将取消所有具有相同预约码的预约');\n          }\n          console.log('取消预约请求参数:', data);\n          const response = await reservationApi.cancelReservation(this.reservation.reservation_code, data);\n          console.log('Cancel response:', response);\n\n          // 无论API响应成功与否，检查返回消息以确定实际状态\n          if (response.data) {\n            // 特殊处理：如果消息表明预定已经取消，视为成功\n            if (response.data.message === '预定已取消' || response.data.message.includes('已取消')) {\n              console.log('预定已经处于取消状态，视为取消成功');\n\n              // 关闭取消对话框\n              this.cancelDialogVisible = false;\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'));\n\n              // 直接刷新整个页面，确保获取最新数据\n              console.log('预约已取消，即将刷新页面...');\n\n              // 设置一个短暂的延迟，让用户看到成功消息\n              setTimeout(() => {\n                // 强制刷新整个页面，包括所有资源\n                window.location.href = '#/admin/reservation';\n                window.location.reload(true);\n              }, 1000);\n              return;\n            } else if (response.data.success) {\n              // 常规成功响应处理\n              // 关闭取消对话框\n              this.cancelDialogVisible = false;\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'));\n\n              // 直接刷新整个页面，确保获取最新数据\n              console.log('预约已取消，即将刷新页面...');\n\n              // 设置一个短暂的延迟，让用户看到成功消息\n              setTimeout(() => {\n                // 强制刷新整个页面，包括所有资源\n                window.location.href = '#/admin/reservation';\n                window.location.reload(true);\n              }, 1000);\n            } else {\n              // 真正的错误消息\n              const errorMsg = response.data.message || this.$t('reservation.cancelFailed');\n              this.$message.error(errorMsg);\n              // 关闭取消对话框\n              this.cancelDialogVisible = false;\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error);\n        this.$message.error(this.$t('error.serverError'));\n        // 关闭取消对话框\n        this.cancelDialogVisible = false;\n      } finally {\n        this.submitting = false;\n      }\n    },\n    async confirmReturn() {\n      this.submitting = true;\n      try {\n        // 使用取消预定的API，但添加early_return参数和预约序号参数\n        const data = {\n          early_return: true\n        };\n\n        // 添加预约序号参数，确保只取消特定的子预约\n        if (this.reservation.reservation_number) {\n          data.reservation_number = this.reservation.reservation_number;\n          console.log('提前归还 - 预约序号参数存在:', this.reservation.reservation_number);\n        } else {\n          console.warn('提前归还 - 预约序号参数不存在，将取消所有具有相同预约码的预约');\n        }\n        console.log('提前归还 - 请求参数:', data);\n        const response = await reservationApi.cancelReservation(this.reservation.reservation_code, data);\n        console.log('Return response:', response);\n        if (response.data && response.data.success) {\n          // 关闭对话框\n          this.returnDialogVisible = false;\n\n          // 显示成功消息\n          this.$message.success(this.$t('reservation.returnSuccess'));\n\n          // 直接重新获取预定信息\n          this.fetchReservation();\n        } else {\n          const errorMsg = response.data ? response.data.message : this.$t('reservation.returnFailed');\n          this.$message.error(errorMsg);\n          this.returnDialogVisible = false;\n        }\n      } catch (error) {\n        console.error('Failed to return equipment:', error);\n        this.$message.error(this.$t('error.serverError'));\n        this.returnDialogVisible = false;\n      } finally {\n        this.submitting = false;\n      }\n    },\n    goBack() {\n      // 检查是否是从循环预约详情页面返回\n      const fromRecurring = this.$route.query.fromRecurring === 'true';\n      if (fromRecurring) {\n        // 如果是从循环预约详情页面返回，直接跳转到预约管理页面\n        this.$router.push('/admin/reservation');\n      } else if (window.history.length > 1) {\n        // 在返回前，设置一个标记，表示这个页面是从预约详情页面返回的\n        // 这样可以防止在返回后再次进入循环预约详情页面\n        localStorage.setItem('returning_from_detail', 'true');\n\n        // 使用浏览器的历史记录返回\n        this.$router.go(-1);\n\n        // 设置一个定时器，在一段时间后清除标记\n        setTimeout(() => {\n          localStorage.removeItem('returning_from_detail');\n        }, 2000);\n      } else {\n        // 如果没有历史记录，则导航到预定管理页面\n        this.$router.push('/admin/reservation');\n      }\n    },\n    // 状态保存相关方法\n    saveState() {\n      if (!this.reservation) return;\n\n      // 计算当前状态\n      const statusText = this.getStatusText(this.reservation);\n      const statusType = this.getStatusType(this.reservation);\n\n      // 将状态保存到localStorage\n      const stateKey = `reservation_status_${this.reservation.reservation_code}`;\n      const state = {\n        statusText,\n        statusType,\n        timestamp: new Date().getTime()\n      };\n      console.log('Saving state to localStorage:', state);\n      localStorage.setItem(stateKey, JSON.stringify(state));\n    },\n    getSavedState() {\n      if (!this.reservation) return null;\n\n      // 从localStorage获取状态\n      const stateKey = `reservation_status_${this.reservation.reservation_code}`;\n      const savedStateStr = localStorage.getItem(stateKey);\n      if (!savedStateStr) return null;\n      try {\n        const savedState = JSON.parse(savedStateStr);\n        console.log('Retrieved saved state:', savedState);\n\n        // 检查保存的状态是否过期（超过5分钟）\n        const now = new Date().getTime();\n        const fiveMinutes = 5 * 60 * 1000;\n        if (now - savedState.timestamp > fiveMinutes) {\n          console.log('Saved state is expired, removing it');\n          localStorage.removeItem(stateKey);\n          return null;\n        }\n        return savedState;\n      } catch (e) {\n        console.error('Error parsing saved state:', e);\n        return null;\n      }\n    },\n    // 更新URL中的状态参数\n    updateUrlWithNewStatus(newStatus) {\n      console.log('更新URL状态为:', newStatus);\n\n      // 获取当前状态对应的文本和类型\n      let statusText = '';\n      let statusType = '';\n      if (newStatus === 'cancelled') {\n        statusText = this.$t('reservation.cancelled');\n        statusType = 'danger';\n      } else if (newStatus === 'confirmed') {\n        const now = new Date();\n        const start = new Date(this.reservation.start_datetime);\n        const end = new Date(this.reservation.end_datetime);\n        if (now >= start && now <= end) {\n          // 使用中\n          statusText = this.$t('reservation.inUse');\n          statusType = 'primary';\n        } else if (isReservationExpired(this.reservation.end_datetime)) {\n          // 已过期\n          statusText = this.$t('reservation.expired');\n          statusType = 'warning';\n        } else {\n          // 已确认\n          statusText = this.$t('reservation.confirmed');\n          statusType = 'success';\n        }\n      }\n      console.log('新状态文本和类型:', statusText, statusType);\n\n      // 更新路由参数，但不触发路由变化\n      const query = {\n        ...this.$route.query,\n        displayStatus: statusText,\n        displayStatusType: statusType\n      };\n\n      // 更新URL但不重新加载页面\n      this.$router.replace({\n        path: this.$route.path,\n        query\n      }).catch(err => {\n        // 忽略重复导航错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err;\n        }\n      });\n    },\n    // 强制更新状态显示（用于操作后立即更新UI）\n    forceUpdateStatus(newStatus) {\n      console.log('强制更新状态为:', newStatus);\n\n      // 更新状态文本和类型\n      let statusText = '';\n      let statusType = '';\n      if (newStatus === 'cancelled') {\n        statusText = this.$t('reservation.cancelled');\n        statusType = 'danger';\n\n        // 只为当前操作的预约添加永久状态标记，不影响其他预约\n        if (this.reservation && this.reservation.reservation_number) {\n          // 仅使用预约序号作为键保存状态，不再使用预约代码\n          const stateKey = `reservation_status_${this.reservation.reservation_number}`;\n          const state = {\n            statusText: this.$t('reservation.cancelled'),\n            statusType: 'danger',\n            dbStatus: 'cancelled',\n            forcedStatus: 'cancelled',\n            timestamp: new Date().getTime(),\n            permanent: true,\n            reservationNumber: this.reservation.reservation_number\n          };\n          console.log('永久保存特定预约序号的已取消状态:', state);\n          localStorage.setItem(stateKey, JSON.stringify(state));\n        }\n\n        // 确保数据模型中的状态也是正确的\n        if (this.reservation) {\n          this.reservation.status = 'cancelled';\n        }\n\n        // 强制状态文本和类型\n        this.forcedStatusText = statusText;\n        this.forcedStatusType = statusType;\n        this.forcedStatus = 'cancelled';\n      } else if (newStatus === 'confirmed') {\n        const now = new Date();\n        const start = new Date(this.reservation.start_datetime);\n        const end = new Date(this.reservation.end_datetime);\n        if (now >= start && now <= end) {\n          // 使用中\n          statusText = this.$t('reservation.inUse');\n          statusType = 'primary';\n        } else if (isReservationExpired(this.reservation.end_datetime)) {\n          // 已过期\n          statusText = this.$t('reservation.expired');\n          statusType = 'warning';\n        } else {\n          // 已确认\n          statusText = this.$t('reservation.confirmed');\n          statusType = 'success';\n        }\n\n        // 确保数据模型中的状态也是正确的\n        if (this.reservation) {\n          this.reservation.status = 'confirmed';\n\n          // 如果当前预约曾被标记为取消，则移除该标记\n          if (this.reservation.reservation_number) {\n            const stateKey = `reservation_status_${this.reservation.reservation_number}`;\n            localStorage.removeItem(stateKey);\n          }\n        }\n\n        // 强制状态文本和类型\n        this.forcedStatusText = statusText;\n        this.forcedStatusType = statusType;\n        this.forcedStatus = 'confirmed';\n      }\n\n      // 更新路由状态参数\n      this.updateUrlWithNewStatus(newStatus);\n    },\n    // 修改方法：通过预约序号保存状态\n    saveStatusByReservationNumber(reservationNumber, status) {\n      if (!reservationNumber) return;\n      console.log('通过预约序号保存状态:', reservationNumber, status);\n\n      // 创建缓存键\n      const cacheKey = `reservation_status_${reservationNumber}`;\n      let statusText = '';\n      let statusType = '';\n      if (status === 'cancelled') {\n        statusText = this.$t('reservation.cancelled');\n        statusType = 'danger';\n      } else if (status === 'confirmed') {\n        statusText = this.$t('reservation.confirmed');\n        statusType = 'success';\n      }\n\n      // 保存状态到本地存储，确保包含预约序号信息\n      const state = {\n        statusText,\n        statusType,\n        dbStatus: status,\n        forcedStatus: status,\n        timestamp: new Date().getTime(),\n        permanent: true,\n        reservationNumber: reservationNumber\n      };\n      console.log('保存预约序号状态到缓存:', cacheKey, state);\n      localStorage.setItem(cacheKey, JSON.stringify(state));\n    },\n    // 检查URL时间参数是否与预定时间匹配\n    isTimeMatching(urlStartTime, urlEndTime, resStartTime, resEndTime) {\n      if (!urlStartTime || !urlEndTime || !resStartTime || !resEndTime) {\n        return false;\n      }\n      console.log('比较时间参数:', {\n        urlStartTime,\n        urlEndTime,\n        resStartTime,\n        resEndTime\n      });\n\n      // 将所有时间转换为字符串以便比较\n      const formatTime = timeStr => {\n        try {\n          // 处理可能的日期格式\n          const date = new Date(timeStr);\n          if (isNaN(date.getTime())) {\n            // 如果无法解析为日期，直接使用原始字符串\n            return timeStr;\n          }\n\n          // 将日期格式化为 YYYY-MM-DD 的形式\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n\n          // 返回日期部分，用于匹配同一天的不同预定\n          return `${year}-${month}-${day}`;\n        } catch (e) {\n          console.error('格式化时间出错:', e);\n          return timeStr;\n        }\n      };\n      const urlStartFormatted = formatTime(urlStartTime);\n      const urlEndFormatted = formatTime(urlEndTime);\n      const resStartFormatted = formatTime(resStartTime);\n      const resEndFormatted = formatTime(resEndTime);\n      console.log('格式化后的时间比较:', {\n        urlStartFormatted,\n        urlEndFormatted,\n        resStartFormatted,\n        resEndFormatted\n      });\n\n      // 判断日期是否匹配（只比较日期部分）\n      const isMatch = urlStartFormatted === resStartFormatted && urlEndFormatted === resEndFormatted;\n      console.log('时间匹配结果:', isMatch);\n      return isMatch;\n    },\n    // 检查URL时间参数是否与预定时间精确匹配（包括时间部分）\n    isExactTimeMatching(urlStartTime, urlEndTime, resStartTime, resEndTime) {\n      if (!urlStartTime || !urlEndTime || !resStartTime || !resEndTime) {\n        return false;\n      }\n      console.log('精确比较时间参数:', {\n        urlStartTime,\n        urlEndTime,\n        resStartTime,\n        resEndTime\n      });\n      try {\n        // 尝试解析日期时间，转换为ISO格式进行精确比较\n        // 注意：我们需要比较的是时间精度而不仅仅是日期\n\n        // 处理URL中的时间参数\n        let urlStart, urlEnd;\n        if (typeof urlStartTime === 'string') {\n          // 如果是ISO格式字符串，直接创建Date对象\n          if (urlStartTime.includes('T')) {\n            urlStart = new Date(urlStartTime);\n          } else {\n            // 如果是\"YYYY-MM-DD HH:MM\"格式，手动解析\n            const parts = urlStartTime.split(' ');\n            if (parts.length === 2) {\n              const dateParts = parts[0].split('-');\n              const timeParts = parts[1].split(':');\n              urlStart = new Date(parseInt(dateParts[0]), parseInt(dateParts[1]) - 1,\n              // 月份是0-11\n              parseInt(dateParts[2]), parseInt(timeParts[0]), parseInt(timeParts[1]));\n            } else {\n              urlStart = new Date(urlStartTime);\n            }\n          }\n        } else {\n          urlStart = new Date(urlStartTime);\n        }\n        if (typeof urlEndTime === 'string') {\n          if (urlEndTime.includes('T')) {\n            urlEnd = new Date(urlEndTime);\n          } else {\n            const parts = urlEndTime.split(' ');\n            if (parts.length === 2) {\n              const dateParts = parts[0].split('-');\n              const timeParts = parts[1].split(':');\n              urlEnd = new Date(parseInt(dateParts[0]), parseInt(dateParts[1]) - 1, parseInt(dateParts[2]), parseInt(timeParts[0]), parseInt(timeParts[1]));\n            } else {\n              urlEnd = new Date(urlEndTime);\n            }\n          }\n        } else {\n          urlEnd = new Date(urlEndTime);\n        }\n\n        // 处理预约中的时间\n        let resStart = new Date(resStartTime);\n        let resEnd = new Date(resEndTime);\n\n        // 将所有时间转换为ISO字符串进行比较（不包括毫秒和时区信息）\n        const formatForCompare = date => {\n          if (isNaN(date.getTime())) {\n            console.error('无效的日期对象:', date);\n            return '';\n          }\n          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n        };\n        const urlStartFormatted = formatForCompare(urlStart);\n        const urlEndFormatted = formatForCompare(urlEnd);\n        const resStartFormatted = formatForCompare(resStart);\n        const resEndFormatted = formatForCompare(resEnd);\n        console.log('格式化后的精确时间比较:', {\n          urlStartFormatted,\n          urlEndFormatted,\n          resStartFormatted,\n          resEndFormatted\n        });\n\n        // 判断时间是否精确匹配\n        const isMatch = urlStartFormatted === resStartFormatted && urlEndFormatted === resEndFormatted;\n        console.log('精确时间匹配结果:', isMatch);\n        return isMatch;\n      } catch (e) {\n        console.error('精确比较时间出错:', e);\n        // 出错时保守返回不匹配\n        return false;\n      }\n    },\n    // 显示历史记录\n    async showHistory() {\n      if (!this.reservation) return;\n      this.historyDialogVisible = true;\n      this.loadingHistory = true;\n      try {\n        // 传递预约码和预约序号\n        const response = await this.$api.reservation.getReservationHistory(this.reservation.reservation_code, this.reservation.reservation_number);\n        if (response.data && response.data.success) {\n          this.historyRecords = response.data.data;\n        } else {\n          const errorMsg = response.data ? response.data.message : this.$t('reservation.historyFetchFailed');\n          this.$message.error(errorMsg);\n          this.historyRecords = [];\n        }\n      } catch (error) {\n        console.error('获取历史记录失败:', error);\n        this.$message.error(this.$t('error.serverError'));\n        this.historyRecords = [];\n      } finally {\n        this.loadingHistory = false;\n      }\n    },\n    // 获取字段显示名称\n    getFieldDisplayName(fieldName) {\n      const fieldMap = {\n        'start_datetime': this.$t('reservation.startTime'),\n        'end_datetime': this.$t('reservation.endTime'),\n        'purpose': this.$t('reservation.purpose'),\n        'user_name': this.$t('reservation.userName'),\n        'user_contact': this.$t('reservation.userContact'),\n        'user_department': this.$t('reservation.userDepartment'),\n        'user_email': this.$t('reservation.userEmail'),\n        'status': this.$t('reservation.status'),\n        'lang': this.$t('common.language')\n      };\n      return fieldMap[fieldName] || fieldName;\n    },\n    // 获取历史记录操作文本\n    getHistoryActionText(action) {\n      const actionMap = {\n        'update': this.$t('reservation.modified'),\n        'create': this.$t('reservation.created'),\n        'delete': this.$t('reservation.deleted'),\n        'status_change': this.$t('reservation.statusChanged')\n      };\n      return actionMap[action] || action;\n    },\n    // 格式化历史记录值\n    formatHistoryValue(fieldName, value) {\n      if (!value) return '-';\n      if (fieldName === 'start_datetime' || fieldName === 'end_datetime') {\n        return this.formatDateTime(value);\n      } else if (fieldName === 'status') {\n        const statusMap = {\n          'confirmed': this.$t('reservation.confirmed'),\n          'cancelled': this.$t('reservation.cancelled'),\n          'in_use': this.$t('reservation.inUse'),\n          'expired': this.$t('reservation.expired')\n        };\n        return statusMap[value] || value;\n      }\n      return value;\n    },\n    // 查看循环预约详情\n    viewRecurringReservation() {\n      if (!this.reservation || !this.reservation.recurring_reservation_id) return;\n\n      // 检查是否有返回标记，如果有则不进行跳转\n      if (localStorage.getItem('returning_from_detail') === 'true') {\n        console.log('检测到从详情页返回，阻止再次进入循环预约详情');\n        return;\n      }\n\n      // 跳转到循环预约详情页面，并传递来源信息和预约码\n      this.$router.push({\n        path: `/recurring-reservation/${this.reservation.recurring_reservation_id}`,\n        query: {\n          fromAdmin: 'true',\n          reservationCode: this.reservation.reservation_code,\n          fromRecurring: 'false' // 标记不是从循环预约详情页面来的\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["reservationApi", "isReservationExpired", "name", "data", "loading", "submitting", "modifying", "reservation", "cancelDialogVisible", "returnDialogVisible", "modifyDialogVisible", "historyDialogVisible", "historyRecords", "loadingHistory", "forcedStatusText", "forcedStatusType", "dateMatches", "statusUpdated", "<PERSON><PERSON><PERSON>us", "reservationStatusCacheKey", "modifyForm", "startDateTime", "endDateTime", "purpose", "userEmail", "modifyRules", "required", "message", "trigger", "type", "dateTimePickerOptions", "disabledDate", "time", "getTime", "Date", "now", "created", "fetchReservation", "window", "addEventListener", "saveState", "destroyed", "removeEventListener", "mounted", "loadReservation", "watch", "handler", "to", "from", "params", "code", "query", "reservationNumber", "console", "log", "deep", "beforeRouteUpdate", "next", "computed", "isReservationStarted", "startTime", "start_datetime", "getStatusTagText", "savedState", "getSavedState", "statusText", "getStatusText", "getStatusTagType", "statusType", "getStatusType", "displayStatusText", "$route", "displayStatus", "endTime", "end_datetime", "$t", "status", "displayStatusType", "formattedStartTime", "formatDateTime", "formattedEndTime", "processedHistoryRecords", "length", "filteredRecords", "filter", "record", "field_name", "groupedRecords", "for<PERSON>ach", "timestamp", "created_at", "user_type", "user_id", "records", "push", "Object", "values", "sort", "a", "b", "methods", "savedReservationNumber", "localStorage", "getItem", "forceUseReservationNumber", "removeItem", "_t", "start_time", "end_time", "reservation_number", "response", "getReservationByNumber", "success", "showNotification", "$notify", "title", "duration", "originalStartTime", "originalEndTime", "warn", "error", "getReservationByCode", "cache<PERSON>ey", "cachedStatus", "statusData", "JSON", "parse", "db<PERSON><PERSON>us", "e", "errorMsg", "$message", "$api", "getReservationByCodeWithParams", "date_matches", "equipment_id", "loadEquipment", "dateString", "date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "start", "end", "isReservationInProgress", "handleModify", "user_email", "validateTimeRange", "submitModifyForm", "$refs", "validate", "updateData", "undefined", "lang", "$i18n", "locale", "updateReservation", "reservation_code", "setItem", "handleCancel", "handleReturn", "confirmCancel", "recurring_reservation_id", "reservationCode", "cancelReservation", "includes", "forceUpdateStatus", "saveStatusByReservationNumber", "$confirm", "confirmButtonText", "cancelButtonText", "then", "$router", "catch", "setTimeout", "location", "href", "reload", "confirmReturn", "early_return", "goBack", "fromRecurring", "history", "go", "stateKey", "state", "stringify", "savedStateStr", "fiveMinutes", "updateUrlWithNewStatus", "newStatus", "replace", "path", "err", "permanent", "isTimeMatching", "urlStartTime", "urlEndTime", "resStartTime", "resEndTime", "formatTime", "timeStr", "isNaN", "year", "month", "day", "urlStartFormatted", "urlEndFormatted", "resStartFormatted", "resEndFormatted", "isMatch", "isExactTimeMatching", "urlStart", "urlEnd", "parts", "split", "dateParts", "timeParts", "parseInt", "resStart", "resEnd", "formatForCompare", "showHistory", "getReservationHistory", "getFieldDisplayName", "fieldName", "fieldMap", "getHistoryActionText", "action", "actionMap", "formatHistoryValue", "value", "statusMap", "viewRecurringReservation", "fromAdmin"], "sources": ["src/views/admin/AdminReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-reservation-detail\">\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">{{ $t('reservation.detail') }}</h1>\n      <el-button @click=\"goBack\" icon=\"el-icon-back\">\n        {{ $t('common.back') }}\n      </el-button>\n    </div>\n\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <el-card v-else-if=\"!reservation\" class=\"error-card\">\n      <div class=\"error-message\">\n        <i class=\"el-icon-warning-outline\"></i>\n        <p>{{ $t('reservation.reservationNotFound') }}</p>\n      </div>\n      <el-button type=\"primary\" @click=\"goBack\">\n        {{ $t('common.back') }}\n      </el-button>\n    </el-card>\n\n    <div v-else>\n      <!-- 预定详情卡片 -->\n      <el-card shadow=\"hover\" class=\"detail-card\">\n        <div slot=\"header\" class=\"card-header\">\n          <span>{{ $t('reservation.detail') }}</span>\n          <el-tag :type=\"displayStatusType\">\n            {{ displayStatusText }}\n          </el-tag>\n        </div>\n\n        <!-- 桌面端描述列表 -->\n        <el-descriptions :column=\"2\" border class=\"desktop-descriptions\">\n          <el-descriptions-item :label=\"$t('reservation.number')\">\n            {{ reservation.reservation_number || '-' }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.reservationType')\">\n            <el-tag\n              size=\"medium\"\n              :type=\"reservation.recurring_reservation_id ? 'primary' : 'success'\"\n              effect=\"plain\"\n            >\n              {{ reservation.recurring_reservation_id ? $t('reservation.recurringReservation') : $t('reservation.singleReservation') }}\n            </el-tag>\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.code')\">\n            {{ reservation.reservation_code }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.status')\">\n            <el-tag :type=\"displayStatusType\">\n              {{ displayStatusText }}\n            </el-tag>\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.equipmentName')\">\n            <router-link :to=\"`/equipment/${reservation.equipment_id}`\">\n              {{ reservation.equipment_name }}\n            </router-link>\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('common.createTime')\">\n            {{ formatDateTime(reservation.created_at) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.startTime')\">\n            {{ formatDateTime(reservation.start_datetime) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.endTime')\">\n            {{ formatDateTime(reservation.end_datetime) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userName')\">\n            {{ reservation.user_name }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userDepartment')\">\n            {{ reservation.user_department }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userContact')\">\n            {{ reservation.user_contact }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userEmail')\">\n            {{ reservation.user_email || '-' }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.purpose')\" :span=\"2\">\n            {{ reservation.purpose || '-' }}\n          </el-descriptions-item>\n        </el-descriptions>\n\n        <!-- 移动端自定义信息显示 -->\n        <div class=\"mobile-info-container\">\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.number') }}</span>\n            <span class=\"mobile-info-content\">\n              <span class=\"reservation-number\">{{ reservation.reservation_number || '-' }}</span>\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.reservationType') }}</span>\n            <span class=\"mobile-info-content\">\n              <el-tag\n                size=\"small\"\n                :type=\"reservation.recurring_reservation_id ? 'primary' : 'success'\"\n                effect=\"plain\"\n              >\n                {{ reservation.recurring_reservation_id ? $t('reservation.recurringReservation') : $t('reservation.singleReservation') }}\n              </el-tag>\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.code') }}</span>\n            <span class=\"mobile-info-content\">\n              <span class=\"reservation-code\">{{ reservation.reservation_code }}</span>\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.status') }}</span>\n            <span class=\"mobile-info-content\">\n              <el-tag :type=\"displayStatusType\" size=\"small\">\n                {{ displayStatusText }}\n              </el-tag>\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.equipmentName') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ reservation.equipment_name }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('common.createTime') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ formatDateTime(reservation.created_at) }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.startTime') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ formatDateTime(reservation.start_datetime) }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.endTime') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ formatDateTime(reservation.end_datetime) }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.userName') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ reservation.user_name }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.userDepartment') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ reservation.user_department }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.userContact') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ reservation.user_contact }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.userEmail') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ reservation.user_email || '-' }}\n            </span>\n          </div>\n\n          <div class=\"mobile-info-row\">\n            <span class=\"mobile-info-label\">{{ $t('reservation.purpose') }}</span>\n            <span class=\"mobile-info-content\">\n              {{ reservation.purpose || '-' }}\n            </span>\n          </div>\n        </div>\n\n        <div class=\"actions\">\n          <!-- 已确认且未开始的预约才显示修改按钮 -->\n          <el-button\n            v-if=\"displayStatusText === $t('reservation.confirmed') && !isReservationStarted\"\n            type=\"primary\"\n            @click=\"handleModify\"\n            style=\"margin-right: 10px;\"\n          >\n            {{ $t('reservation.modifyReservation') }}\n          </el-button>\n\n          <!-- 已确认且未开始的预约才显示取消按钮 -->\n          <el-button\n            v-if=\"displayStatusText === $t('reservation.confirmed')\"\n            type=\"danger\"\n            @click=\"handleCancel\"\n            style=\"margin-right: 10px;\"\n          >\n            {{ $t('reservation.cancelReservation') }}\n          </el-button>\n\n          <!-- 使用中的预约才显示提前归还按钮 -->\n          <el-button\n            v-if=\"displayStatusText === $t('reservation.inUse')\"\n            type=\"primary\"\n            @click=\"handleReturn\"\n            style=\"margin-right: 10px;\"\n          >\n            {{ $t('reservation.earlyReturn') }}\n          </el-button>\n\n          <!-- 查看循环预约按钮，只有循环预约才显示 -->\n          <el-button\n            v-if=\"reservation.recurring_reservation_id\"\n            type=\"warning\"\n            @click=\"viewRecurringReservation\"\n            style=\"margin-right: 10px;\"\n          >\n            {{ $t('reservation.viewRecurringReservation') }}\n          </el-button>\n\n          <!-- 查看历史记录按钮，所有状态都显示 -->\n          <el-button\n            type=\"info\"\n            @click=\"showHistory\"\n          >\n            {{ $t('reservation.viewHistory') }}\n          </el-button>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 取消预定确认对话框 -->\n    <el-dialog\n      :title=\"$t('common.warning')\"\n      :visible.sync=\"cancelDialogVisible\"\n      :modal=\"false\"\n      width=\"30%\"\n    >\n      <span>{{ $t('reservation.confirmCancel') }}</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"cancelDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"danger\" @click=\"confirmCancel\" :loading=\"submitting\">{{ $t('common.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 提前归还确认对话框 -->\n    <el-dialog\n      :title=\"$t('reservation.earlyReturn')\"\n      :visible.sync=\"returnDialogVisible\"\n      :modal=\"false\"\n      width=\"30%\"\n    >\n      <span>{{ $t('reservation.confirmEarlyReturn') }}</span>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"returnDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"primary\" @click=\"confirmReturn\" :loading=\"submitting\">{{ $t('common.confirm') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 修改预定对话框 -->\n    <el-dialog\n      :title=\"$t('reservation.modifyReservation')\"\n      :visible.sync=\"modifyDialogVisible\"\n      :modal=\"false\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"modifyForm\"\n        :model=\"modifyForm\"\n        :rules=\"modifyRules\"\n        label-width=\"120px\"\n        v-loading=\"modifying\"\n      >\n        <!-- 开始时间 -->\n        <el-form-item :label=\"$t('reservation.startTime')\" prop=\"startDateTime\">\n          <el-date-picker\n            v-model=\"modifyForm.startDateTime\"\n            type=\"datetime\"\n            :placeholder=\"$t('reservation.selectStartTime')\"\n            style=\"width: 100%\"\n            :picker-options=\"dateTimePickerOptions\"\n            value-format=\"yyyy-MM-ddTHH:mm:ss\"\n            format=\"yyyy-MM-dd HH:mm:ss\"\n          ></el-date-picker>\n        </el-form-item>\n\n        <!-- 结束时间 -->\n        <el-form-item :label=\"$t('reservation.endTime')\" prop=\"endDateTime\">\n          <el-date-picker\n            v-model=\"modifyForm.endDateTime\"\n            type=\"datetime\"\n            :placeholder=\"$t('reservation.selectEndTime')\"\n            style=\"width: 100%\"\n            :picker-options=\"dateTimePickerOptions\"\n            value-format=\"yyyy-MM-ddTHH:mm:ss\"\n            format=\"yyyy-MM-dd HH:mm:ss\"\n          ></el-date-picker>\n        </el-form-item>\n\n        <!-- 使用目的 -->\n        <el-form-item :label=\"$t('reservation.purpose')\">\n          <el-input\n            v-model=\"modifyForm.purpose\"\n            :placeholder=\"$t('reservation.purposePlaceholder')\"\n          ></el-input>\n        </el-form-item>\n\n        <!-- 用户邮箱 -->\n        <el-form-item :label=\"$t('reservation.userEmail')\" prop=\"userEmail\">\n          <el-input\n            v-model=\"modifyForm.userEmail\"\n            :placeholder=\"$t('reservation.emailPlaceholder')\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"modifyDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n        <el-button type=\"primary\" @click=\"submitModifyForm\" :loading=\"modifying\">{{ $t('common.submit') }}</el-button>\n      </span>\n    </el-dialog>\n\n    <!-- 修改历史记录对话框 -->\n    <el-dialog\n      :title=\"$t('reservation.modificationHistory')\"\n      :visible.sync=\"historyDialogVisible\"\n      :modal=\"false\"\n      width=\"700px\"\n    >\n      <div v-loading=\"loadingHistory\">\n        <el-empty v-if=\"processedHistoryRecords.length === 0\" :description=\"$t('reservation.noHistory')\"></el-empty>\n        <el-timeline v-else>\n          <el-timeline-item\n            v-for=\"(group, index) in processedHistoryRecords\"\n            :key=\"index\"\n            type=\"primary\"\n          >\n            <el-card class=\"history-card\">\n              <!-- 修改时间显示在最上面 -->\n              <div class=\"history-time\">\n                <i class=\"el-icon-time\"></i> {{ formatDateTime(group.timestamp) }}\n              </div>\n\n              <div class=\"history-user\">\n                {{ group.user_type === 'admin' ? $t('reservation.admin') : $t('reservation.user') }}\n                {{ group.user_id ? ': ' + group.user_id : '' }}\n              </div>\n\n              <div v-for=\"(record, recordIndex) in group.records\" :key=\"recordIndex\" class=\"history-item\">\n                <div class=\"history-action\">\n                  {{ getHistoryActionText(record.action) }}\n                  <span class=\"history-field\">{{ getFieldDisplayName(record.field_name) }}</span>\n                </div>\n                <div class=\"history-values\">\n                  <div class=\"history-old-value\">\n                    <span class=\"history-label\">{{ $t('reservation.oldValue') }}:</span>\n                    <span>{{ formatHistoryValue(record.field_name, record.old_value) }}</span>\n                  </div>\n                  <div class=\"history-new-value\">\n                    <span class=\"history-label\">{{ $t('reservation.newValue') }}:</span>\n                    <span>{{ formatHistoryValue(record.field_name, record.new_value) }}</span>\n                  </div>\n                </div>\n              </div>\n            </el-card>\n          </el-timeline-item>\n        </el-timeline>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport { reservationApi } from '@/api'\nimport { isReservationExpired } from '@/utils/date'\n\nexport default {\n  name: 'AdminReservationDetail',\n\n  data() {\n    return {\n      loading: false,\n      submitting: false,\n      modifying: false,\n      reservation: null,\n      cancelDialogVisible: false,\n      returnDialogVisible: false,\n      modifyDialogVisible: false,\n      // 历史记录相关\n      historyDialogVisible: false,\n      historyRecords: [],\n      loadingHistory: false,\n      // 强制显示状态值 - 用于覆盖计算属性的显示\n      forcedStatusText: null,\n      forcedStatusType: null,\n      // 添加日期匹配标志\n      dateMatches: false,\n      // 用于记录状态变更\n      statusUpdated: false,\n      forcedStatus: null,\n      // 添加特定预约状态缓存标识\n      reservationStatusCacheKey: '',\n      // 修改预定表单数据\n      modifyForm: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n\n      // 修改预定表单验证规则\n      modifyRules: {\n        startDateTime: [\n          { required: true, message: '请选择开始时间', trigger: 'change' }\n        ],\n        endDateTime: [\n          { required: true, message: '请选择结束时间', trigger: 'change' }\n        ],\n        userEmail: [\n          { required: true, message: '请输入邮箱', trigger: 'blur' },\n          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n        ]\n      },\n      // 日期时间选择器配置\n      dateTimePickerOptions: {\n        disabledDate: (time) => {\n          return time.getTime() < Date.now() - 8.64e7; // 8.64e7是一天的毫秒数\n        }\n      }\n    }\n  },\n\n  created() {\n    this.fetchReservation()\n\n    // 注册页面刷新事件监听器\n    window.addEventListener('beforeunload', this.saveState)\n  },\n\n  destroyed() {\n    // 移除事件监听器，避免内存泄漏\n    window.removeEventListener('beforeunload', this.saveState)\n  },\n\n  mounted() {\n    this.loadReservation();\n  },\n\n  // 监听路由参数变化，当路由参数变化时重新获取数据\n  watch: {\n    '$route': {\n      handler: function(to, from) {\n        // 如果路由参数发生变化，重新获取数据\n        if (to.params.code !== from.params.code ||\n            to.query.reservationNumber !== from.query.reservationNumber) {\n          console.log('路由参数变化，重新获取数据')\n          this.fetchReservation()\n        }\n      },\n      deep: true\n    }\n  },\n\n  // 在路由参数变化时调用\n  beforeRouteUpdate(to, from, next) {\n    console.log('路由参数更新，重新获取数据')\n    this.fetchReservation()\n    next()\n  },\n\n  computed: {\n    // 判断预约是否已开始\n    isReservationStarted() {\n      if (!this.reservation) return false\n\n      const now = new Date()\n      const startTime = new Date(this.reservation.start_datetime)\n\n      return now >= startTime\n    },\n\n    getStatusTagText() {\n      if (!this.reservation) return ''\n\n      // 尝试恢复存储的状态\n      const savedState = this.getSavedState()\n      if (savedState && savedState.statusText) {\n        console.log('Using saved status text:', savedState.statusText)\n        return savedState.statusText\n      }\n\n      // 调用方法获取状态文本\n      const statusText = this.getStatusText(this.reservation)\n      console.log('Computed status text:', statusText)\n      return statusText\n    },\n\n    getStatusTagType() {\n      if (!this.reservation) return ''\n\n      // 尝试恢复存储的状态\n      const savedState = this.getSavedState()\n      if (savedState && savedState.statusType) {\n        console.log('Using saved status type:', savedState.statusType)\n        return savedState.statusType\n      }\n\n      // 调用方法获取状态类型\n      const statusType = this.getStatusType(this.reservation)\n      console.log('Computed status type:', statusType)\n      return statusType\n    },\n    // 获取显示的状态文本\n    displayStatusText() {\n      // 如果有URL传递的状态参数，最优先使用它\n      if (this.$route.query.displayStatus) {\n        console.log('使用URL传递的状态文本:', this.$route.query.displayStatus);\n        return this.$route.query.displayStatus;\n      }\n\n      // 次高优先级：使用强制状态（针对操作后立即更新）\n      if (this.forcedStatusText) {\n        console.log('使用强制状态文本:', this.forcedStatusText);\n        return this.forcedStatusText;\n      }\n\n      // 再次优先级：使用本地存储的状态\n      const savedState = this.getSavedState();\n      if (savedState && savedState.statusText) {\n        console.log('使用本地存储的状态文本:', savedState.statusText);\n        return savedState.statusText;\n      }\n\n      // 最低优先级：动态计算状态（实时计算）\n      if (!this.reservation) return '';\n\n      // 在这里添加实时计算逻辑，确保已过期和使用中状态能立即更新\n      // 检查是否已过期\n      const now = new Date();\n      const endTime = new Date(this.reservation.end_datetime);\n      if (endTime < now) {\n        console.log('实时计算：预定已过期');\n        return this.$t('reservation.expired');\n      }\n\n      // 检查是否使用中\n      const startTime = new Date(this.reservation.start_datetime);\n      if (now >= startTime && now <= endTime) {\n        console.log('实时计算：预定使用中');\n        return this.$t('reservation.inUse');\n      }\n\n      // 默认为已确认，但如果数据库中确实标记为已取消，则显示取消状态\n      if (this.reservation.status === 'cancelled') {\n        console.log('数据库标记为已取消');\n        return this.$t('reservation.cancelled');\n      }\n\n      console.log('实时计算：预定已确认');\n      return this.$t('reservation.confirmed');\n    },\n\n    // 获取显示的状态类型（用于样式）\n    displayStatusType() {\n      // 如果有URL传递的状态类型参数，最优先使用它\n      if (this.$route.query.displayStatusType) {\n        console.log('使用URL传递的状态类型:', this.$route.query.displayStatusType);\n        return this.$route.query.displayStatusType;\n      }\n\n      // 次高优先级：使用强制状态类型（针对操作后立即更新）\n      if (this.forcedStatusType) {\n        console.log('使用强制状态类型:', this.forcedStatusType);\n        return this.forcedStatusType;\n      }\n\n      // 再次优先级：使用本地存储的状态\n      const savedState = this.getSavedState();\n      if (savedState && savedState.statusType) {\n        console.log('使用本地存储的状态类型:', savedState.statusType);\n        return savedState.statusType;\n      }\n\n      // 最低优先级：动态计算状态类型（实时计算）\n      if (!this.reservation) return '';\n\n      // 实时计算逻辑，与状态文本保持一致\n      const now = new Date();\n      const endTime = new Date(this.reservation.end_datetime);\n\n      if (endTime < now) {\n        return 'warning';\n      }\n\n      const startTime = new Date(this.reservation.start_datetime);\n      if (now >= startTime && now <= endTime) {\n        return 'primary';\n      }\n\n      // 如果数据库中确实标记为已取消，则显示取消状态类型\n      if (this.reservation.status === 'cancelled') {\n        return 'danger';\n      }\n\n      return 'success';\n    },\n    formattedStartTime() {\n      if (!this.reservation) return '';\n      return this.formatDateTime(this.reservation.start_datetime);\n    },\n    formattedEndTime() {\n      if (!this.reservation) return '';\n      return this.formatDateTime(this.reservation.end_datetime);\n    },\n\n    // 处理历史记录，按时间分组并过滤掉不需要显示的字段\n    processedHistoryRecords() {\n      if (!this.historyRecords || this.historyRecords.length === 0) {\n        return []\n      }\n\n      // 过滤掉 lang 字段的修改记录\n      const filteredRecords = this.historyRecords.filter(record =>\n        record.field_name !== 'lang'\n      )\n\n      // 按照修改时间分组\n      const groupedRecords = {}\n      filteredRecords.forEach(record => {\n        const timestamp = record.created_at\n        if (!groupedRecords[timestamp]) {\n          groupedRecords[timestamp] = {\n            timestamp: timestamp,\n            user_type: record.user_type,\n            user_id: record.user_id,\n            records: []\n          }\n        }\n        groupedRecords[timestamp].records.push(record)\n      })\n\n      // 转换为数组并按时间倒序排序\n      return Object.values(groupedRecords).sort((a, b) => {\n        return new Date(b.timestamp) - new Date(a.timestamp)\n      })\n    }\n  },\n\n  methods: {\n    isReservationExpired,\n\n    async fetchReservation() {\n      this.loading = true\n\n      try {\n        const code = this.$route.params.code\n        console.log('Fetching reservation with code:', code)\n\n        // 获取URL中的查询参数（用于时间和状态）\n        const startTime = this.$route.query.startTime\n        const endTime = this.$route.query.endTime\n        let reservationNumber = this.$route.query.reservationNumber\n\n        // 如果URL中没有预约序号，尝试从localStorage中获取\n        if (!reservationNumber) {\n          const savedReservationNumber = localStorage.getItem('current_reservation_number')\n          if (savedReservationNumber) {\n            console.log('从localStorage中获取预约序号:', savedReservationNumber)\n            reservationNumber = savedReservationNumber\n          }\n        }\n\n        // 检查是否强制使用预约序号查询\n        const forceUseReservationNumber = localStorage.getItem('force_use_reservation_number')\n        if (forceUseReservationNumber === 'true') {\n          console.log('强制使用预约序号查询')\n          // 使用后清除标记\n          localStorage.removeItem('force_use_reservation_number')\n        }\n\n        console.log('URL 时间参数:', startTime, endTime)\n        console.log('预约序号:', reservationNumber)\n\n        // 构建API请求参数\n        let params = {}\n\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime()\n        params._t = timestamp\n        console.log('添加时间戳参数:', timestamp)\n\n        // 只有当同时提供了开始和结束时间才添加到请求参数中\n        if (startTime && endTime) {\n          params.start_time = startTime\n          params.end_time = endTime\n          console.log('Including time parameters in API request:', params)\n        }\n\n        // 添加预约序号参数\n        if (reservationNumber) {\n          params.reservation_number = reservationNumber\n          console.log('Including reservation number in API request:', params)\n        }\n\n        // 如果有预约序号，优先使用预约序号查询\n        if (reservationNumber) {\n          console.log('使用预约序号查询:', reservationNumber)\n          try {\n            const response = await reservationApi.getReservationByNumber(reservationNumber)\n            console.log('通过预约序号查询结果:', response)\n\n            // 如果通过预约序号查询成功，直接使用结果\n            if (response.data && response.data.success) {\n              console.log('通过预约序号查询成功')\n\n              // 获取原始数据\n              this.reservation = response.data.data\n              console.log('Original reservation data:', this.reservation)\n\n              // 检查是否需要显示预约序号通知\n              const showNotification = localStorage.getItem('show_reservation_number_notification')\n              if (showNotification === 'true') {\n                // 显示预约序号信息\n                this.$notify({\n                  title: '预约详情',\n                  message: `当前查看的是预约序号: ${this.reservation.reservation_number}`,\n                  type: 'info',\n                  duration: 5000\n                })\n                // 使用后清除标记\n                localStorage.removeItem('show_reservation_number_notification')\n              }\n\n              // 重要：从URL参数中获取时间覆盖预约显示时间（这确保显示的时间与列表页一致）\n              if (startTime && endTime) {\n                console.log('使用URL参数覆盖显示时间 - 原始时间:', this.reservation.start_datetime, this.reservation.end_datetime)\n\n                // 保存原始时间以备后用\n                this.originalStartTime = this.reservation.start_datetime\n                this.originalEndTime = this.reservation.end_datetime\n\n                // 覆盖显示时间\n                this.reservation.start_datetime = startTime\n                this.reservation.end_datetime = endTime\n\n                console.log('覆盖后的显示时间:', this.reservation.start_datetime, this.reservation.end_datetime)\n              }\n\n              // 添加详细日志，帮助调试状态判断\n              console.log('Status from API:', this.reservation.status)\n              console.log('Current time:', new Date())\n\n              // 确保状态字段正确\n              if (!this.reservation.status) {\n                // 如果API返回的状态为空，默认设置为confirmed\n                console.warn('API returned empty status, setting default to confirmed')\n                this.reservation.status = 'confirmed'\n              }\n\n              // 重要：确保状态字段是正确的\n              console.log(`最终数据库状态: ${this.reservation.status}，展示状态为: ${this.getStatusText(this.reservation)}`)\n\n              this.loading = false\n              return\n            } else {\n              console.warn('通过预约序号查询失败，将使用预约码查询')\n            }\n          } catch (error) {\n            console.error('通过预约序号查询出错:', error)\n            console.warn('将使用预约码查询')\n          }\n        }\n\n        // 使用API进行请求 - 注意：getReservationByCode的第二个参数应该是预约序号，不是params对象\n        console.log('Calling API with code and reservationNumber:', code, reservationNumber)\n        const response = await reservationApi.getReservationByCode(code, reservationNumber)\n\n        console.log('API Response:', response)\n\n        if (response.data && response.data.success) {\n          // 获取原始数据\n          this.reservation = response.data.data\n          console.log('Original reservation data:', this.reservation)\n\n          // 显示预约序号信息（无论通过何种方式查询）\n          if (this.reservation.reservation_number) {\n            this.$notify({\n              title: '预约详情',\n              message: `当前查看的是预约序号: ${this.reservation.reservation_number}`,\n              type: 'info',\n              duration: 5000\n            })\n          }\n\n          // 重要：从URL参数中获取时间覆盖预约显示时间（这确保显示的时间与列表页一致）\n          if (startTime && endTime) {\n            console.log('使用URL参数覆盖显示时间 - 原始时间:', this.reservation.start_datetime, this.reservation.end_datetime)\n\n            // 保存原始时间以备后用\n            this.originalStartTime = this.reservation.start_datetime\n            this.originalEndTime = this.reservation.end_datetime\n\n            // 覆盖显示时间\n            this.reservation.start_datetime = startTime\n            this.reservation.end_datetime = endTime\n\n            console.log('覆盖后的显示时间:', this.reservation.start_datetime, this.reservation.end_datetime)\n          }\n\n          // 添加详细日志，帮助调试状态判断\n          console.log('Status from API:', this.reservation.status)\n          console.log('Current time:', new Date())\n\n          // 确保状态字段正确\n          if (!this.reservation.status) {\n            // 如果API返回的状态为空，默认设置为confirmed\n            console.warn('API returned empty status, setting default to confirmed')\n            this.reservation.status = 'confirmed'\n          }\n\n          // 重要：现在只检查当前预约序号对应的缓存状态，不再通用应用\n          if (this.reservation.reservation_number) {\n            // 使用当前预约序号创建缓存键\n            const cacheKey = `reservation_status_${this.reservation.reservation_number}`\n            const cachedStatus = localStorage.getItem(cacheKey)\n\n            if (cachedStatus) {\n              try {\n                const statusData = JSON.parse(cachedStatus)\n                console.log('找到预约序号缓存状态:', statusData)\n\n                // 验证是否为当前预约序号的状态\n                if (statusData.reservationNumber === this.reservation.reservation_number) {\n                  // 如果缓存中标记为已取消，则覆盖API返回的状态\n                  if (statusData.dbStatus === 'cancelled' || statusData.forcedStatus === 'cancelled') {\n                    console.log('使用缓存中的已取消状态覆盖API返回状态，仅适用于预约序号:', this.reservation.reservation_number)\n                    this.reservation.status = 'cancelled'\n                    this.forcedStatus = 'cancelled'\n                    this.forcedStatusText = this.$t('reservation.cancelled')\n                    this.forcedStatusType = 'danger'\n                  }\n                } else {\n                  console.log('缓存状态预约序号不匹配，不应用缓存状态')\n                }\n              } catch (e) {\n                console.error('解析缓存状态出错:', e)\n              }\n            }\n          }\n\n          // 重要：确保状态字段是正确的\n          console.log(`最终数据库状态: ${this.reservation.status}，展示状态为: ${this.getStatusText(this.reservation)}`)\n        } else {\n          const errorMsg = response.data ? response.data.message : this.$t('reservation.reservationNotFound')\n          this.$message.error(errorMsg)\n          this.reservation = null\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservation:', error)\n        this.$message.error(this.$t('error.serverError'))\n        this.reservation = null\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async loadReservation() {\n      try {\n        this.loading = true;\n        // 获取预约码\n        const code = this.$route.params.code;\n        if (!code) {\n          this.error = '预约码不存在';\n          this.loading = false;\n          return;\n        }\n\n        // 构建查询参数，确保传递日期时间参数\n        const params = {};\n\n        // 添加时间戳参数，确保每次都获取最新数据\n        const timestamp = new Date().getTime()\n        params._t = timestamp\n        console.log('添加时间戳参数:', timestamp)\n\n        // 从URL获取日期时间参数\n        const startTime = this.$route.query.startTime;\n        const endTime = this.$route.query.endTime;\n\n        if (startTime) {\n          params.start_time = startTime;\n          console.log('查询开始时间:', startTime);\n        }\n        if (endTime) {\n          params.end_time = endTime;\n          console.log('查询结束时间:', endTime);\n        }\n\n        // 获取预约信息 - 使用新的API方法来正确处理查询参数\n        let response;\n\n        // 检查是否有预约序号参数\n        const reservationNumber = this.$route.query.reservationNumber;\n        if (reservationNumber) {\n          console.log('使用预约序号查询:', reservationNumber);\n          // 使用预约序号查询\n          response = await this.$api.reservation.getReservationByCode(code, reservationNumber);\n        } else if (startTime || endTime) {\n          console.log('使用时间参数查询:', { start_time: startTime, end_time: endTime });\n          // 使用时间参数查询\n          response = await this.$api.reservation.getReservationByCodeWithParams(code, params);\n        } else {\n          console.log('使用预约码查询，不传递额外参数');\n          // 只使用预约码查询\n          response = await this.$api.reservation.getReservationByCode(code);\n        }\n\n        console.log('预约API响应:', response);\n\n        if (response.success) {\n          this.reservation = response.data;\n\n          // 设置日期匹配标志\n          this.dateMatches = !!response.data.date_matches;\n          console.log('日期是否匹配:', this.dateMatches);\n\n          // 设置预约状态信息\n          if (this.reservation.status === 'confirmed') {\n            this.statusText = this.$t('reservation.confirmed');\n            this.statusType = 'success';\n          } else if (this.reservation.status === 'cancelled') {\n            this.statusText = this.$t('reservation.cancelled');\n            this.statusType = 'danger';\n\n            // 如果日期匹配且状态为cancelled，强制使用取消状态\n            if (this.dateMatches) {\n              this.forcedStatus = 'cancelled';\n              console.log('强制使用取消状态');\n            }\n          }\n\n          // 如果有设备ID，获取设备详情\n          if (this.reservation.equipment_id) {\n            this.loadEquipment(this.reservation.equipment_id);\n          }\n        } else {\n          this.error = response.message || '获取预约信息失败';\n        }\n      } catch (error) {\n        console.error('加载预约详情出错:', error);\n        this.error = '加载预约详情出错: ' + (error.message || error);\n      } finally {\n        this.loading = false;\n      }\n    },\n\n    formatDateTime(dateString) {\n      if (!dateString) return '-'\n\n      const date = new Date(dateString)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n    },\n\n    // 获取状态文本\n    getStatusText(reservation) {\n      // 首先检查状态是否直接来自API响应\n      console.log('Checking status from API:', reservation.status);\n\n      // 如果API明确返回了cancelled状态，显示已取消\n      if (reservation.status === 'cancelled') {\n        console.log('Using cancelled status from API');\n        return this.$t('reservation.cancelled');\n      }\n\n      // 其他状态根据时间动态计算\n      if (isReservationExpired(reservation.end_datetime)) {\n        console.log('Calculated status: expired');\n        return this.$t('reservation.expired');\n      }\n\n      // 如果预约正在进行中，显示\"使用中\"\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        console.log('Calculated status: in use');\n        return this.$t('reservation.inUse');\n      }\n\n      // 如果预约已确认且未开始，显示\"已确认\"\n      console.log('Calculated status: confirmed');\n      return this.$t('reservation.confirmed');\n    },\n\n    // 获取状态类型（样式）\n    getStatusType(reservation) {\n      // 首先检查状态是否直接来自API响应\n      if (reservation.status === 'cancelled') {\n        return 'danger';\n      }\n\n      // 如果预约已过期，返回橙色\n      if (isReservationExpired(reservation.end_datetime)) {\n        return 'warning';\n      }\n\n      // 如果预约正在进行中，返回蓝色\n      const now = new Date();\n      const start = new Date(reservation.start_datetime);\n      const end = new Date(reservation.end_datetime);\n      if (now >= start && now <= end) {\n        return 'primary';\n      }\n\n      // 如果预约已确认且未开始，返回绿色\n      return 'success';\n    },\n\n    // 判断预约是否正在进行中\n    isReservationInProgress(reservation) {\n      if (!reservation) return false\n\n      const now = new Date()\n      const start = new Date(reservation.start_datetime)\n      const end = new Date(reservation.end_datetime)\n\n      // 当前时间在开始时间和结束时间之间\n      return now >= start && now <= end\n    },\n\n    // 处理修改预约按钮点击\n    handleModify() {\n      // 初始化修改表单数据\n      this.modifyForm = {\n        startDateTime: this.reservation.start_datetime,\n        endDateTime: this.reservation.end_datetime,\n        purpose: this.reservation.purpose || '',\n        userEmail: this.reservation.user_email || ''\n      }\n\n      // 显示修改对话框\n      this.modifyDialogVisible = true\n    },\n\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyForm.startDateTime)\n      const endTime = new Date(this.modifyForm.endDateTime)\n\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'))\n        return false\n      }\n\n      return true\n    },\n\n    // 提交修改表单\n    async submitModifyForm() {\n      try {\n        // 验证表单\n        await this.$refs.modifyForm.validate()\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return\n\n        this.modifying = true\n\n        // 构建更新数据\n        const updateData = {\n          start_datetime: this.modifyForm.startDateTime,\n          end_datetime: this.modifyForm.endDateTime,\n          purpose: this.modifyForm.purpose || undefined,\n          user_email: this.modifyForm.userEmail || undefined,\n          lang: this.$i18n.locale\n        }\n\n        // 调用更新API - 传递预约序号以确保修改正确的子预约\n        const response = await reservationApi.updateReservation(\n          this.reservation.reservation_code,\n          updateData,\n          this.reservation.reservation_number  // 传递预约序号\n        )\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.updateSuccess'))\n          this.modifyDialogVisible = false\n          // 重新获取预定信息\n          await this.fetchReservation()\n\n          // 设置强制刷新标记，以便在返回列表页时刷新数据\n          localStorage.setItem('force_refresh_reservation_list', 'true')\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.updateFailed'))\n        }\n      } catch (error) {\n        console.error('修改预约失败:', error)\n        this.$message.error(this.$t('error.serverError'))\n      } finally {\n        this.modifying = false\n      }\n    },\n\n    handleCancel() {\n      this.cancelDialogVisible = true\n    },\n\n    handleReturn() {\n      this.returnDialogVisible = true\n    },\n\n    async confirmCancel() {\n      this.submitting = true\n\n      try {\n        // 检查是否是循环预约的子预约\n        if (this.reservation.recurring_reservation_id) {\n          console.log('Cancelling a child reservation of recurring reservation:', this.reservation.recurring_reservation_id)\n\n          // 获取当前预约的详细信息\n          const reservationCode = this.reservation.reservation_code\n          const reservationNumber = this.reservation.reservation_number\n\n          // 准备取消请求数据\n          const data = {}\n\n          // 添加预约序号参数，确保只取消特定的子预约\n          if (reservationNumber) {\n            data.reservation_number = reservationNumber\n            console.log('循环子预约取消 - 预约序号参数存在:', reservationNumber)\n          } else {\n            console.warn('循环子预约取消 - 预约序号参数不存在，将取消所有具有相同预约码的预约')\n          }\n\n          // 取消单个子预约\n          const response = await reservationApi.cancelReservation(reservationCode, data)\n\n          console.log('Cancel child reservation response:', response)\n\n          // 无论API响应成功与否，检查返回消息以确定实际状态\n          if (response.data) {\n            // 特殊处理：如果消息表明预定已经取消，视为成功\n            if (response.data.message === '预定已取消' || response.data.message.includes('已取消')) {\n              console.log('预定已经处于取消状态，视为取消成功');\n\n              // 关闭取消对话框\n              this.cancelDialogVisible = false\n\n              // 立即更新预约状态，不等待API重新获取\n              this.reservation.status = 'cancelled'\n\n              // 立即更新UI显示的状态\n              this.forceUpdateStatus('cancelled')\n\n              // 关键改进：同时保存预约序号的状态\n              if (reservationNumber) {\n                this.saveStatusByReservationNumber(reservationNumber, 'cancelled')\n              }\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'))\n\n              // 提示用户返回循环预约详情页面\n              this.$confirm(\n                '已成功取消此子预约。是否查看循环预约详情？',\n                '操作成功',\n                {\n                  confirmButtonText: '查看循环预约',\n                  cancelButtonText: '留在当前页面',\n                  type: 'success'\n                }\n              ).then(() => {\n                // 跳转到循环预约详情页面\n                this.$router.push(`/recurring-reservation/${this.reservation.recurring_reservation_id}`)\n              }).catch(() => {\n                // 用户选择留在当前页面，直接重新获取预定信息\n                this.fetchReservation()\n              })\n              return;\n            } else if (response.data.success) {\n              // 常规成功响应处理\n              // 关闭取消对话框\n              this.cancelDialogVisible = false\n\n              // 立即更新预约状态，不等待API重新获取\n              this.reservation.status = 'cancelled'\n\n              // 立即更新UI显示的状态\n              this.forceUpdateStatus('cancelled')\n\n              // 关键改进：同时保存预约序号的状态\n              if (reservationNumber) {\n                this.saveStatusByReservationNumber(reservationNumber, 'cancelled')\n              }\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'))\n\n              // 提示用户返回循环预约详情页面\n              this.$confirm(\n                '已成功取消此子预约。是否查看循环预约详情？',\n                '操作成功',\n                {\n                  confirmButtonText: '查看循环预约',\n                  cancelButtonText: '留在当前页面',\n                  type: 'success'\n                }\n              ).then(() => {\n                // 跳转到循环预约详情页面\n                this.$router.push(`/recurring-reservation/${this.reservation.recurring_reservation_id}`)\n              }).catch(() => {\n                // 用户选择留在当前页面，直接重新获取预定信息\n                this.fetchReservation()\n              })\n            } else {\n              // 真正的错误消息\n              const errorMsg = response.data.message || this.$t('reservation.cancelFailed')\n              this.$message.error(errorMsg)\n              // 关闭取消对话框\n              this.cancelDialogVisible = false\n            }\n          }\n        } else {\n          // 普通预约的取消逻辑\n          // 添加预约序号参数，确保只取消特定的子预约\n          const data = {}\n\n          // 添加预约序号参数，确保只取消特定的子预约\n          if (this.reservation.reservation_number) {\n            data.reservation_number = this.reservation.reservation_number\n            console.log('预约序号参数存在:', this.reservation.reservation_number)\n          } else {\n            console.warn('预约序号参数不存在，将取消所有具有相同预约码的预约')\n          }\n\n          console.log('取消预约请求参数:', data)\n\n          const response = await reservationApi.cancelReservation(this.reservation.reservation_code, data)\n\n          console.log('Cancel response:', response)\n\n          // 无论API响应成功与否，检查返回消息以确定实际状态\n          if (response.data) {\n            // 特殊处理：如果消息表明预定已经取消，视为成功\n            if (response.data.message === '预定已取消' || response.data.message.includes('已取消')) {\n              console.log('预定已经处于取消状态，视为取消成功');\n\n              // 关闭取消对话框\n              this.cancelDialogVisible = false\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'))\n\n              // 直接刷新整个页面，确保获取最新数据\n              console.log('预约已取消，即将刷新页面...')\n\n              // 设置一个短暂的延迟，让用户看到成功消息\n              setTimeout(() => {\n                // 强制刷新整个页面，包括所有资源\n                window.location.href = '#/admin/reservation'\n                window.location.reload(true)\n              }, 1000)\n              return;\n            } else if (response.data.success) {\n              // 常规成功响应处理\n              // 关闭取消对话框\n              this.cancelDialogVisible = false\n\n              // 显示成功消息\n              this.$message.success(this.$t('reservation.cancelSuccess'))\n\n              // 直接刷新整个页面，确保获取最新数据\n              console.log('预约已取消，即将刷新页面...')\n\n              // 设置一个短暂的延迟，让用户看到成功消息\n              setTimeout(() => {\n                // 强制刷新整个页面，包括所有资源\n                window.location.href = '#/admin/reservation'\n                window.location.reload(true)\n              }, 1000)\n            } else {\n              // 真正的错误消息\n              const errorMsg = response.data.message || this.$t('reservation.cancelFailed')\n              this.$message.error(errorMsg)\n              // 关闭取消对话框\n              this.cancelDialogVisible = false\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error)\n        this.$message.error(this.$t('error.serverError'))\n        // 关闭取消对话框\n        this.cancelDialogVisible = false\n      } finally {\n        this.submitting = false\n      }\n    },\n\n    async confirmReturn() {\n      this.submitting = true\n\n      try {\n        // 使用取消预定的API，但添加early_return参数和预约序号参数\n        const data = {\n          early_return: true\n        }\n\n        // 添加预约序号参数，确保只取消特定的子预约\n        if (this.reservation.reservation_number) {\n          data.reservation_number = this.reservation.reservation_number\n          console.log('提前归还 - 预约序号参数存在:', this.reservation.reservation_number)\n        } else {\n          console.warn('提前归还 - 预约序号参数不存在，将取消所有具有相同预约码的预约')\n        }\n\n        console.log('提前归还 - 请求参数:', data)\n\n        const response = await reservationApi.cancelReservation(this.reservation.reservation_code, data)\n\n        console.log('Return response:', response)\n\n        if (response.data && response.data.success) {\n          // 关闭对话框\n          this.returnDialogVisible = false\n\n          // 显示成功消息\n          this.$message.success(this.$t('reservation.returnSuccess'))\n\n          // 直接重新获取预定信息\n          this.fetchReservation()\n        } else {\n          const errorMsg = response.data ? response.data.message : this.$t('reservation.returnFailed')\n          this.$message.error(errorMsg)\n          this.returnDialogVisible = false\n        }\n      } catch (error) {\n        console.error('Failed to return equipment:', error)\n        this.$message.error(this.$t('error.serverError'))\n        this.returnDialogVisible = false\n      } finally {\n        this.submitting = false\n      }\n    },\n\n    goBack() {\n      // 检查是否是从循环预约详情页面返回\n      const fromRecurring = this.$route.query.fromRecurring === 'true';\n\n      if (fromRecurring) {\n        // 如果是从循环预约详情页面返回，直接跳转到预约管理页面\n        this.$router.push('/admin/reservation');\n      } else if (window.history.length > 1) {\n        // 在返回前，设置一个标记，表示这个页面是从预约详情页面返回的\n        // 这样可以防止在返回后再次进入循环预约详情页面\n        localStorage.setItem('returning_from_detail', 'true');\n\n        // 使用浏览器的历史记录返回\n        this.$router.go(-1);\n\n        // 设置一个定时器，在一段时间后清除标记\n        setTimeout(() => {\n          localStorage.removeItem('returning_from_detail');\n        }, 2000);\n      } else {\n        // 如果没有历史记录，则导航到预定管理页面\n        this.$router.push('/admin/reservation');\n      }\n    },\n\n    // 状态保存相关方法\n    saveState() {\n      if (!this.reservation) return\n\n      // 计算当前状态\n      const statusText = this.getStatusText(this.reservation)\n      const statusType = this.getStatusType(this.reservation)\n\n      // 将状态保存到localStorage\n      const stateKey = `reservation_status_${this.reservation.reservation_code}`\n      const state = {\n        statusText,\n        statusType,\n        timestamp: new Date().getTime()\n      }\n\n      console.log('Saving state to localStorage:', state)\n      localStorage.setItem(stateKey, JSON.stringify(state))\n    },\n\n    getSavedState() {\n      if (!this.reservation) return null\n\n      // 从localStorage获取状态\n      const stateKey = `reservation_status_${this.reservation.reservation_code}`\n      const savedStateStr = localStorage.getItem(stateKey)\n\n      if (!savedStateStr) return null\n\n      try {\n        const savedState = JSON.parse(savedStateStr)\n        console.log('Retrieved saved state:', savedState)\n\n        // 检查保存的状态是否过期（超过5分钟）\n        const now = new Date().getTime()\n        const fiveMinutes = 5 * 60 * 1000\n        if (now - savedState.timestamp > fiveMinutes) {\n          console.log('Saved state is expired, removing it')\n          localStorage.removeItem(stateKey)\n          return null\n        }\n\n        return savedState\n      } catch (e) {\n        console.error('Error parsing saved state:', e)\n        return null\n      }\n    },\n\n    // 更新URL中的状态参数\n    updateUrlWithNewStatus(newStatus) {\n      console.log('更新URL状态为:', newStatus);\n\n      // 获取当前状态对应的文本和类型\n      let statusText = '';\n      let statusType = '';\n\n      if (newStatus === 'cancelled') {\n        statusText = this.$t('reservation.cancelled');\n        statusType = 'danger';\n      } else if (newStatus === 'confirmed') {\n        const now = new Date();\n        const start = new Date(this.reservation.start_datetime);\n        const end = new Date(this.reservation.end_datetime);\n\n        if (now >= start && now <= end) {\n          // 使用中\n          statusText = this.$t('reservation.inUse');\n          statusType = 'primary';\n        } else if (isReservationExpired(this.reservation.end_datetime)) {\n          // 已过期\n          statusText = this.$t('reservation.expired');\n          statusType = 'warning';\n        } else {\n          // 已确认\n          statusText = this.$t('reservation.confirmed');\n          statusType = 'success';\n        }\n      }\n\n      console.log('新状态文本和类型:', statusText, statusType);\n\n      // 更新路由参数，但不触发路由变化\n      const query = { ...this.$route.query, displayStatus: statusText, displayStatusType: statusType };\n\n      // 更新URL但不重新加载页面\n      this.$router.replace({\n        path: this.$route.path,\n        query\n      }).catch(err => {\n        // 忽略重复导航错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err;\n        }\n      });\n    },\n\n    // 强制更新状态显示（用于操作后立即更新UI）\n    forceUpdateStatus(newStatus) {\n      console.log('强制更新状态为:', newStatus);\n\n      // 更新状态文本和类型\n      let statusText = '';\n      let statusType = '';\n\n      if (newStatus === 'cancelled') {\n        statusText = this.$t('reservation.cancelled');\n        statusType = 'danger';\n\n        // 只为当前操作的预约添加永久状态标记，不影响其他预约\n        if (this.reservation && this.reservation.reservation_number) {\n          // 仅使用预约序号作为键保存状态，不再使用预约代码\n          const stateKey = `reservation_status_${this.reservation.reservation_number}`;\n          const state = {\n            statusText: this.$t('reservation.cancelled'),\n            statusType: 'danger',\n            dbStatus: 'cancelled',\n            forcedStatus: 'cancelled',\n            timestamp: new Date().getTime(),\n            permanent: true,\n            reservationNumber: this.reservation.reservation_number\n          };\n          console.log('永久保存特定预约序号的已取消状态:', state);\n          localStorage.setItem(stateKey, JSON.stringify(state));\n        }\n\n        // 确保数据模型中的状态也是正确的\n        if (this.reservation) {\n          this.reservation.status = 'cancelled';\n        }\n\n        // 强制状态文本和类型\n        this.forcedStatusText = statusText;\n        this.forcedStatusType = statusType;\n        this.forcedStatus = 'cancelled';\n      } else if (newStatus === 'confirmed') {\n        const now = new Date();\n        const start = new Date(this.reservation.start_datetime);\n        const end = new Date(this.reservation.end_datetime);\n\n        if (now >= start && now <= end) {\n          // 使用中\n          statusText = this.$t('reservation.inUse');\n          statusType = 'primary';\n        } else if (isReservationExpired(this.reservation.end_datetime)) {\n          // 已过期\n          statusText = this.$t('reservation.expired');\n          statusType = 'warning';\n        } else {\n          // 已确认\n          statusText = this.$t('reservation.confirmed');\n          statusType = 'success';\n        }\n\n        // 确保数据模型中的状态也是正确的\n        if (this.reservation) {\n          this.reservation.status = 'confirmed';\n\n          // 如果当前预约曾被标记为取消，则移除该标记\n          if (this.reservation.reservation_number) {\n            const stateKey = `reservation_status_${this.reservation.reservation_number}`;\n            localStorage.removeItem(stateKey);\n          }\n        }\n\n        // 强制状态文本和类型\n        this.forcedStatusText = statusText;\n        this.forcedStatusType = statusType;\n        this.forcedStatus = 'confirmed';\n      }\n\n      // 更新路由状态参数\n      this.updateUrlWithNewStatus(newStatus);\n    },\n\n    // 修改方法：通过预约序号保存状态\n    saveStatusByReservationNumber(reservationNumber, status) {\n      if (!reservationNumber) return;\n\n      console.log('通过预约序号保存状态:', reservationNumber, status);\n\n      // 创建缓存键\n      const cacheKey = `reservation_status_${reservationNumber}`;\n\n      let statusText = '';\n      let statusType = '';\n\n      if (status === 'cancelled') {\n        statusText = this.$t('reservation.cancelled');\n        statusType = 'danger';\n      } else if (status === 'confirmed') {\n        statusText = this.$t('reservation.confirmed');\n        statusType = 'success';\n      }\n\n      // 保存状态到本地存储，确保包含预约序号信息\n      const state = {\n        statusText,\n        statusType,\n        dbStatus: status,\n        forcedStatus: status,\n        timestamp: new Date().getTime(),\n        permanent: true,\n        reservationNumber: reservationNumber\n      };\n\n      console.log('保存预约序号状态到缓存:', cacheKey, state);\n      localStorage.setItem(cacheKey, JSON.stringify(state));\n    },\n\n    // 检查URL时间参数是否与预定时间匹配\n    isTimeMatching(urlStartTime, urlEndTime, resStartTime, resEndTime) {\n      if (!urlStartTime || !urlEndTime || !resStartTime || !resEndTime) {\n        return false;\n      }\n\n      console.log('比较时间参数:', {\n        urlStartTime,\n        urlEndTime,\n        resStartTime,\n        resEndTime\n      });\n\n      // 将所有时间转换为字符串以便比较\n      const formatTime = (timeStr) => {\n        try {\n          // 处理可能的日期格式\n          const date = new Date(timeStr);\n          if (isNaN(date.getTime())) {\n            // 如果无法解析为日期，直接使用原始字符串\n            return timeStr;\n          }\n\n          // 将日期格式化为 YYYY-MM-DD 的形式\n          const year = date.getFullYear();\n          const month = String(date.getMonth() + 1).padStart(2, '0');\n          const day = String(date.getDate()).padStart(2, '0');\n\n          // 返回日期部分，用于匹配同一天的不同预定\n          return `${year}-${month}-${day}`;\n        } catch (e) {\n          console.error('格式化时间出错:', e);\n          return timeStr;\n        }\n      };\n\n      const urlStartFormatted = formatTime(urlStartTime);\n      const urlEndFormatted = formatTime(urlEndTime);\n      const resStartFormatted = formatTime(resStartTime);\n      const resEndFormatted = formatTime(resEndTime);\n\n      console.log('格式化后的时间比较:', {\n        urlStartFormatted,\n        urlEndFormatted,\n        resStartFormatted,\n        resEndFormatted\n      });\n\n      // 判断日期是否匹配（只比较日期部分）\n      const isMatch = urlStartFormatted === resStartFormatted && urlEndFormatted === resEndFormatted;\n\n      console.log('时间匹配结果:', isMatch);\n\n      return isMatch;\n    },\n\n    // 检查URL时间参数是否与预定时间精确匹配（包括时间部分）\n    isExactTimeMatching(urlStartTime, urlEndTime, resStartTime, resEndTime) {\n      if (!urlStartTime || !urlEndTime || !resStartTime || !resEndTime) {\n        return false;\n      }\n\n      console.log('精确比较时间参数:', {\n        urlStartTime,\n        urlEndTime,\n        resStartTime,\n        resEndTime\n      });\n\n      try {\n        // 尝试解析日期时间，转换为ISO格式进行精确比较\n        // 注意：我们需要比较的是时间精度而不仅仅是日期\n\n        // 处理URL中的时间参数\n        let urlStart, urlEnd;\n        if (typeof urlStartTime === 'string') {\n          // 如果是ISO格式字符串，直接创建Date对象\n          if (urlStartTime.includes('T')) {\n            urlStart = new Date(urlStartTime);\n          } else {\n            // 如果是\"YYYY-MM-DD HH:MM\"格式，手动解析\n            const parts = urlStartTime.split(' ');\n            if (parts.length === 2) {\n              const dateParts = parts[0].split('-');\n              const timeParts = parts[1].split(':');\n              urlStart = new Date(\n                parseInt(dateParts[0]),\n                parseInt(dateParts[1]) - 1, // 月份是0-11\n                parseInt(dateParts[2]),\n                parseInt(timeParts[0]),\n                parseInt(timeParts[1])\n              );\n            } else {\n              urlStart = new Date(urlStartTime);\n            }\n          }\n        } else {\n          urlStart = new Date(urlStartTime);\n        }\n\n        if (typeof urlEndTime === 'string') {\n          if (urlEndTime.includes('T')) {\n            urlEnd = new Date(urlEndTime);\n          } else {\n            const parts = urlEndTime.split(' ');\n            if (parts.length === 2) {\n              const dateParts = parts[0].split('-');\n              const timeParts = parts[1].split(':');\n              urlEnd = new Date(\n                parseInt(dateParts[0]),\n                parseInt(dateParts[1]) - 1,\n                parseInt(dateParts[2]),\n                parseInt(timeParts[0]),\n                parseInt(timeParts[1])\n              );\n            } else {\n              urlEnd = new Date(urlEndTime);\n            }\n          }\n        } else {\n          urlEnd = new Date(urlEndTime);\n        }\n\n        // 处理预约中的时间\n        let resStart = new Date(resStartTime);\n        let resEnd = new Date(resEndTime);\n\n        // 将所有时间转换为ISO字符串进行比较（不包括毫秒和时区信息）\n        const formatForCompare = (date) => {\n          if (isNaN(date.getTime())) {\n            console.error('无效的日期对象:', date);\n            return '';\n          }\n          return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n        };\n\n        const urlStartFormatted = formatForCompare(urlStart);\n        const urlEndFormatted = formatForCompare(urlEnd);\n        const resStartFormatted = formatForCompare(resStart);\n        const resEndFormatted = formatForCompare(resEnd);\n\n        console.log('格式化后的精确时间比较:', {\n          urlStartFormatted,\n          urlEndFormatted,\n          resStartFormatted,\n          resEndFormatted\n        });\n\n        // 判断时间是否精确匹配\n        const isMatch = urlStartFormatted === resStartFormatted && urlEndFormatted === resEndFormatted;\n\n        console.log('精确时间匹配结果:', isMatch);\n\n        return isMatch;\n      } catch (e) {\n        console.error('精确比较时间出错:', e);\n        // 出错时保守返回不匹配\n        return false;\n      }\n    },\n    // 显示历史记录\n    async showHistory() {\n      if (!this.reservation) return\n\n      this.historyDialogVisible = true\n      this.loadingHistory = true\n\n      try {\n        // 传递预约码和预约序号\n        const response = await this.$api.reservation.getReservationHistory(\n          this.reservation.reservation_code,\n          this.reservation.reservation_number\n        )\n\n        if (response.data && response.data.success) {\n          this.historyRecords = response.data.data\n        } else {\n          const errorMsg = response.data ? response.data.message : this.$t('reservation.historyFetchFailed')\n          this.$message.error(errorMsg)\n          this.historyRecords = []\n        }\n      } catch (error) {\n        console.error('获取历史记录失败:', error)\n        this.$message.error(this.$t('error.serverError'))\n        this.historyRecords = []\n      } finally {\n        this.loadingHistory = false\n      }\n    },\n\n    // 获取字段显示名称\n    getFieldDisplayName(fieldName) {\n      const fieldMap = {\n        'start_datetime': this.$t('reservation.startTime'),\n        'end_datetime': this.$t('reservation.endTime'),\n        'purpose': this.$t('reservation.purpose'),\n        'user_name': this.$t('reservation.userName'),\n        'user_contact': this.$t('reservation.userContact'),\n        'user_department': this.$t('reservation.userDepartment'),\n        'user_email': this.$t('reservation.userEmail'),\n        'status': this.$t('reservation.status'),\n        'lang': this.$t('common.language')\n      }\n\n      return fieldMap[fieldName] || fieldName\n    },\n\n    // 获取历史记录操作文本\n    getHistoryActionText(action) {\n      const actionMap = {\n        'update': this.$t('reservation.modified'),\n        'create': this.$t('reservation.created'),\n        'delete': this.$t('reservation.deleted'),\n        'status_change': this.$t('reservation.statusChanged')\n      }\n\n      return actionMap[action] || action\n    },\n\n    // 格式化历史记录值\n    formatHistoryValue(fieldName, value) {\n      if (!value) return '-'\n\n      if (fieldName === 'start_datetime' || fieldName === 'end_datetime') {\n        return this.formatDateTime(value)\n      } else if (fieldName === 'status') {\n        const statusMap = {\n          'confirmed': this.$t('reservation.confirmed'),\n          'cancelled': this.$t('reservation.cancelled'),\n          'in_use': this.$t('reservation.inUse'),\n          'expired': this.$t('reservation.expired')\n        }\n        return statusMap[value] || value\n      }\n\n      return value\n    },\n\n    // 查看循环预约详情\n    viewRecurringReservation() {\n      if (!this.reservation || !this.reservation.recurring_reservation_id) return;\n\n      // 检查是否有返回标记，如果有则不进行跳转\n      if (localStorage.getItem('returning_from_detail') === 'true') {\n        console.log('检测到从详情页返回，阻止再次进入循环预约详情');\n        return;\n      }\n\n      // 跳转到循环预约详情页面，并传递来源信息和预约码\n      this.$router.push({\n        path: `/recurring-reservation/${this.reservation.recurring_reservation_id}`,\n        query: {\n          fromAdmin: 'true',\n          reservationCode: this.reservation.reservation_code,\n          fromRecurring: 'false' // 标记不是从循环预约详情页面来的\n        }\n      });\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-reservation-detail {\n  padding: 20px;\n}\n\n.page-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.page-title {\n  margin: 0;\n  font-size: 24px;\n  color: #303133;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.error-card {\n  text-align: center;\n  padding: 40px 0;\n}\n\n.error-message {\n  margin-bottom: 20px;\n}\n\n.error-message i {\n  font-size: 48px;\n  color: #E6A23C;\n  margin-bottom: 10px;\n}\n\n.error-message p {\n  font-size: 18px;\n  color: #606266;\n}\n\n.detail-card {\n  margin-bottom: 20px;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.actions {\n  margin-top: 20px;\n  text-align: right;\n}\n\n/* 桌面端布局 */\n.desktop-descriptions {\n  display: block; /* 桌面端显示描述列表 */\n}\n\n.mobile-info-container {\n  display: none; /* 桌面端隐藏移动端自定义布局 */\n}\n\n/* 移动端响应式样式 */\n@media (max-width: 768px) {\n  .admin-reservation-detail {\n    padding: 10px 4px 20px 4px !important; /* 减少左右边距，正常的底部边距 */\n    padding-top: 20px !important; /* 为固定header留出空间 */\n    max-width: 100%;\n    overflow-x: hidden; /* 防止水平滚动 */\n  }\n\n  /* 页面头部移动端优化 */\n  .page-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 15px;\n    margin-bottom: 20px;\n    padding: 15px 10px;\n    background-color: #f8f9fa;\n    border-radius: 8px;\n    margin: 0 -2px 20px -2px; /* 让头部更宽 */\n  }\n\n  .page-title {\n    font-size: 20px !important;\n    color: #303133 !important;\n    font-weight: 600;\n    margin: 0;\n  }\n\n  /* 返回按钮移动端优化 */\n  .page-header .el-button {\n    width: 100%;\n    padding: 12px 20px;\n    font-size: 16px;\n  }\n\n  /* 卡片在移动端的优化 */\n  .detail-card {\n    margin: 0 -2px 20px -2px; /* 让卡片更宽，与其他页面保持一致 */\n    border-radius: 8px;\n  }\n\n  /* 操作按钮移动端优化 */\n  .actions {\n    text-align: center;\n    margin-top: 20px;\n    display: flex;\n    flex-direction: column;\n    gap: 10px;\n    align-items: center;\n  }\n\n  .actions .el-button {\n    width: 100%;\n    max-width: 300px;\n    padding: 12px 20px;\n    font-size: 16px;\n    margin: 0 !important;\n  }\n\n  /* 桌面端和移动端布局切换 */\n  .desktop-descriptions {\n    display: none; /* 移动端隐藏桌面端描述列表 */\n  }\n\n  .mobile-info-container {\n    display: flex; /* 移动端显示自定义布局 */\n  }\n\n  /* 为移动端创建自定义的信息显示布局 */\n  .detail-card .el-card__body {\n    padding: 0;\n  }\n\n  /* 移动端信息行样式 */\n  .mobile-info-container {\n    display: flex;\n    flex-direction: column;\n    gap: 0;\n  }\n\n  .mobile-info-row {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n    padding: 15px 20px;\n    border-bottom: 1px solid #f0f0f0;\n    min-height: 60px;\n  }\n\n  .mobile-info-row:last-child {\n    border-bottom: none;\n  }\n\n  .mobile-info-label {\n    font-weight: 600;\n    color: #606266;\n    font-size: 14px;\n    min-width: 80px;\n    flex-shrink: 0;\n  }\n\n  .mobile-info-content {\n    flex: 1;\n    text-align: right;\n    font-size: 14px;\n    color: #303133;\n    word-break: break-all;\n    margin-left: 10px;\n  }\n\n  /* 特殊内容样式 */\n  .mobile-info-content .el-tag {\n    margin-left: 0;\n  }\n\n  .mobile-info-content .reservation-number {\n    color: #f56c6c;\n    font-weight: 600;\n  }\n\n  .mobile-info-content .reservation-code {\n    color: #409eff;\n    font-weight: 600;\n  }\n\n  /* 弹窗移动端适配 */\n  :deep(.el-dialog) {\n    width: 95% !important;\n    margin: 5vh auto !important;\n    max-height: 90vh;\n    overflow-y: auto;\n  }\n\n  :deep(.el-dialog__header) {\n    padding: 15px 20px 10px !important;\n    background-color: #f8f9fa;\n    border-radius: 8px 8px 0 0;\n  }\n\n  :deep(.el-dialog__title) {\n    font-size: 18px !important;\n    font-weight: 600;\n  }\n\n  :deep(.el-dialog__body) {\n    padding: 20px !important;\n    font-size: 16px !important;\n    max-height: 60vh;\n    overflow-y: auto;\n  }\n\n  :deep(.el-dialog__footer) {\n    padding: 15px 20px !important;\n    background-color: #f8f9fa;\n    border-radius: 0 0 8px 8px;\n    text-align: center;\n  }\n\n  /* 弹窗按钮移动端优化 */\n  :deep(.dialog-footer .el-button) {\n    width: 45%;\n    margin: 0 2.5% !important;\n    padding: 12px 20px;\n    font-size: 16px;\n  }\n\n  /* 修改预定表单移动端优化 */\n  :deep(.el-form-item__label) {\n    font-size: 14px !important;\n    font-weight: 600;\n    line-height: 1.5;\n  }\n\n  :deep(.el-input__inner),\n  :deep(.el-textarea__inner) {\n    font-size: 16px !important;\n    padding: 12px 15px;\n  }\n\n  :deep(.el-date-editor) {\n    width: 100% !important;\n  }\n\n  :deep(.el-date-editor .el-input__inner) {\n    padding: 12px 30px 12px 15px;\n  }\n\n  /* 历史记录弹窗移动端优化 */\n  .history-card {\n    margin: 0 -5px 15px -5px;\n    padding: 15px;\n    border-radius: 8px;\n    background-color: #f8f9fa;\n  }\n\n  .history-time {\n    font-size: 14px;\n    margin-bottom: 8px;\n  }\n\n  .history-user {\n    font-size: 13px;\n    margin-bottom: 8px;\n  }\n\n  .history-action {\n    font-size: 13px;\n    margin-bottom: 6px;\n  }\n\n  .history-values {\n    font-size: 12px;\n    padding-left: 8px;\n  }\n\n  /* 遮罩层优化 - 完全移除遮罩 */\n  :deep(.v-modal) {\n    display: none !important;\n  }\n\n  /* 确保弹窗可以正常交互 */\n  :deep(.el-dialog__wrapper) {\n    background-color: transparent !important;\n    pointer-events: auto !important;\n  }\n\n  /* 弹窗容器优化 */\n  :deep(.el-dialog__wrapper .el-dialog) {\n    position: relative;\n    z-index: 2000;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;\n  }\n}\n\n/* 历史记录样式 */\n.history-card {\n  margin-bottom: 10px;\n}\n\n.history-time {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #ff7640;\n  font-size: 16px;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.history-user {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #606266;\n}\n\n.history-item {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #ebeef5;\n}\n\n.history-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.history-action {\n  font-weight: bold;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.history-field {\n  color: #409eff;\n}\n\n.history-values {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  padding-left: 10px;\n  font-size: 13px;\n}\n\n.history-old-value, .history-new-value {\n  display: flex;\n  align-items: flex-start;\n}\n\n.history-old-value {\n  color: #F56C6C;\n}\n\n.history-new-value {\n  color: #67C23A;\n}\n\n.history-label {\n  font-weight: bold;\n  margin-right: 10px;\n  min-width: 80px;\n  color: #606266;\n}\n</style>\n"], "mappings": "AA2YA,SAAAA,cAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,SAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,mBAAA;MACAC,mBAAA;MACA;MACAC,oBAAA;MACAC,cAAA;MACAC,cAAA;MACA;MACAC,gBAAA;MACAC,gBAAA;MACA;MACAC,WAAA;MACA;MACAC,aAAA;MACAC,YAAA;MACA;MACAC,yBAAA;MACA;MACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;MACA;MAEA;MACAC,WAAA;QACAJ,aAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAJ,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,IAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAE,qBAAA;QACAC,YAAA,EAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,gBAAA;;IAEA;IACAC,MAAA,CAAAC,gBAAA,sBAAAC,SAAA;EACA;EAEAC,UAAA;IACA;IACAH,MAAA,CAAAI,mBAAA,sBAAAF,SAAA;EACA;EAEAG,QAAA;IACA,KAAAC,eAAA;EACA;EAEA;EACAC,KAAA;IACA;MACAC,OAAA,WAAAA,CAAAC,EAAA,EAAAC,IAAA;QACA;QACA,IAAAD,EAAA,CAAAE,MAAA,CAAAC,IAAA,KAAAF,IAAA,CAAAC,MAAA,CAAAC,IAAA,IACAH,EAAA,CAAAI,KAAA,CAAAC,iBAAA,KAAAJ,IAAA,CAAAG,KAAA,CAAAC,iBAAA;UACAC,OAAA,CAAAC,GAAA;UACA,KAAAjB,gBAAA;QACA;MACA;MACAkB,IAAA;IACA;EACA;EAEA;EACAC,kBAAAT,EAAA,EAAAC,IAAA,EAAAS,IAAA;IACAJ,OAAA,CAAAC,GAAA;IACA,KAAAjB,gBAAA;IACAoB,IAAA;EACA;EAEAC,QAAA;IACA;IACAC,qBAAA;MACA,UAAApD,WAAA;MAEA,MAAA4B,GAAA,OAAAD,IAAA;MACA,MAAA0B,SAAA,OAAA1B,IAAA,MAAA3B,WAAA,CAAAsD,cAAA;MAEA,OAAA1B,GAAA,IAAAyB,SAAA;IACA;IAEAE,iBAAA;MACA,UAAAvD,WAAA;;MAEA;MACA,MAAAwD,UAAA,QAAAC,aAAA;MACA,IAAAD,UAAA,IAAAA,UAAA,CAAAE,UAAA;QACAZ,OAAA,CAAAC,GAAA,6BAAAS,UAAA,CAAAE,UAAA;QACA,OAAAF,UAAA,CAAAE,UAAA;MACA;;MAEA;MACA,MAAAA,UAAA,QAAAC,aAAA,MAAA3D,WAAA;MACA8C,OAAA,CAAAC,GAAA,0BAAAW,UAAA;MACA,OAAAA,UAAA;IACA;IAEAE,iBAAA;MACA,UAAA5D,WAAA;;MAEA;MACA,MAAAwD,UAAA,QAAAC,aAAA;MACA,IAAAD,UAAA,IAAAA,UAAA,CAAAK,UAAA;QACAf,OAAA,CAAAC,GAAA,6BAAAS,UAAA,CAAAK,UAAA;QACA,OAAAL,UAAA,CAAAK,UAAA;MACA;;MAEA;MACA,MAAAA,UAAA,QAAAC,aAAA,MAAA9D,WAAA;MACA8C,OAAA,CAAAC,GAAA,0BAAAc,UAAA;MACA,OAAAA,UAAA;IACA;IACA;IACAE,kBAAA;MACA;MACA,SAAAC,MAAA,CAAApB,KAAA,CAAAqB,aAAA;QACAnB,OAAA,CAAAC,GAAA,uBAAAiB,MAAA,CAAApB,KAAA,CAAAqB,aAAA;QACA,YAAAD,MAAA,CAAApB,KAAA,CAAAqB,aAAA;MACA;;MAEA;MACA,SAAA1D,gBAAA;QACAuC,OAAA,CAAAC,GAAA,mBAAAxC,gBAAA;QACA,YAAAA,gBAAA;MACA;;MAEA;MACA,MAAAiD,UAAA,QAAAC,aAAA;MACA,IAAAD,UAAA,IAAAA,UAAA,CAAAE,UAAA;QACAZ,OAAA,CAAAC,GAAA,iBAAAS,UAAA,CAAAE,UAAA;QACA,OAAAF,UAAA,CAAAE,UAAA;MACA;;MAEA;MACA,UAAA1D,WAAA;;MAEA;MACA;MACA,MAAA4B,GAAA,OAAAD,IAAA;MACA,MAAAuC,OAAA,OAAAvC,IAAA,MAAA3B,WAAA,CAAAmE,YAAA;MACA,IAAAD,OAAA,GAAAtC,GAAA;QACAkB,OAAA,CAAAC,GAAA;QACA,YAAAqB,EAAA;MACA;;MAEA;MACA,MAAAf,SAAA,OAAA1B,IAAA,MAAA3B,WAAA,CAAAsD,cAAA;MACA,IAAA1B,GAAA,IAAAyB,SAAA,IAAAzB,GAAA,IAAAsC,OAAA;QACApB,OAAA,CAAAC,GAAA;QACA,YAAAqB,EAAA;MACA;;MAEA;MACA,SAAApE,WAAA,CAAAqE,MAAA;QACAvB,OAAA,CAAAC,GAAA;QACA,YAAAqB,EAAA;MACA;MAEAtB,OAAA,CAAAC,GAAA;MACA,YAAAqB,EAAA;IACA;IAEA;IACAE,kBAAA;MACA;MACA,SAAAN,MAAA,CAAApB,KAAA,CAAA0B,iBAAA;QACAxB,OAAA,CAAAC,GAAA,uBAAAiB,MAAA,CAAApB,KAAA,CAAA0B,iBAAA;QACA,YAAAN,MAAA,CAAApB,KAAA,CAAA0B,iBAAA;MACA;;MAEA;MACA,SAAA9D,gBAAA;QACAsC,OAAA,CAAAC,GAAA,mBAAAvC,gBAAA;QACA,YAAAA,gBAAA;MACA;;MAEA;MACA,MAAAgD,UAAA,QAAAC,aAAA;MACA,IAAAD,UAAA,IAAAA,UAAA,CAAAK,UAAA;QACAf,OAAA,CAAAC,GAAA,iBAAAS,UAAA,CAAAK,UAAA;QACA,OAAAL,UAAA,CAAAK,UAAA;MACA;;MAEA;MACA,UAAA7D,WAAA;;MAEA;MACA,MAAA4B,GAAA,OAAAD,IAAA;MACA,MAAAuC,OAAA,OAAAvC,IAAA,MAAA3B,WAAA,CAAAmE,YAAA;MAEA,IAAAD,OAAA,GAAAtC,GAAA;QACA;MACA;MAEA,MAAAyB,SAAA,OAAA1B,IAAA,MAAA3B,WAAA,CAAAsD,cAAA;MACA,IAAA1B,GAAA,IAAAyB,SAAA,IAAAzB,GAAA,IAAAsC,OAAA;QACA;MACA;;MAEA;MACA,SAAAlE,WAAA,CAAAqE,MAAA;QACA;MACA;MAEA;IACA;IACAE,mBAAA;MACA,UAAAvE,WAAA;MACA,YAAAwE,cAAA,MAAAxE,WAAA,CAAAsD,cAAA;IACA;IACAmB,iBAAA;MACA,UAAAzE,WAAA;MACA,YAAAwE,cAAA,MAAAxE,WAAA,CAAAmE,YAAA;IACA;IAEA;IACAO,wBAAA;MACA,UAAArE,cAAA,SAAAA,cAAA,CAAAsE,MAAA;QACA;MACA;;MAEA;MACA,MAAAC,eAAA,QAAAvE,cAAA,CAAAwE,MAAA,CAAAC,MAAA,IACAA,MAAA,CAAAC,UAAA,WACA;;MAEA;MACA,MAAAC,cAAA;MACAJ,eAAA,CAAAK,OAAA,CAAAH,MAAA;QACA,MAAAI,SAAA,GAAAJ,MAAA,CAAAK,UAAA;QACA,KAAAH,cAAA,CAAAE,SAAA;UACAF,cAAA,CAAAE,SAAA;YACAA,SAAA,EAAAA,SAAA;YACAE,SAAA,EAAAN,MAAA,CAAAM,SAAA;YACAC,OAAA,EAAAP,MAAA,CAAAO,OAAA;YACAC,OAAA;UACA;QACA;QACAN,cAAA,CAAAE,SAAA,EAAAI,OAAA,CAAAC,IAAA,CAAAT,MAAA;MACA;;MAEA;MACA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,cAAA,EAAAU,IAAA,EAAAC,CAAA,EAAAC,CAAA;QACA,WAAAjE,IAAA,CAAAiE,CAAA,CAAAV,SAAA,QAAAvD,IAAA,CAAAgE,CAAA,CAAAT,SAAA;MACA;IACA;EACA;EAEAW,OAAA;IACAnG,oBAAA;IAEA,MAAAoC,iBAAA;MACA,KAAAjC,OAAA;MAEA;QACA,MAAA8C,IAAA,QAAAqB,MAAA,CAAAtB,MAAA,CAAAC,IAAA;QACAG,OAAA,CAAAC,GAAA,oCAAAJ,IAAA;;QAEA;QACA,MAAAU,SAAA,QAAAW,MAAA,CAAApB,KAAA,CAAAS,SAAA;QACA,MAAAa,OAAA,QAAAF,MAAA,CAAApB,KAAA,CAAAsB,OAAA;QACA,IAAArB,iBAAA,QAAAmB,MAAA,CAAApB,KAAA,CAAAC,iBAAA;;QAEA;QACA,KAAAA,iBAAA;UACA,MAAAiD,sBAAA,GAAAC,YAAA,CAAAC,OAAA;UACA,IAAAF,sBAAA;YACAhD,OAAA,CAAAC,GAAA,0BAAA+C,sBAAA;YACAjD,iBAAA,GAAAiD,sBAAA;UACA;QACA;;QAEA;QACA,MAAAG,yBAAA,GAAAF,YAAA,CAAAC,OAAA;QACA,IAAAC,yBAAA;UACAnD,OAAA,CAAAC,GAAA;UACA;UACAgD,YAAA,CAAAG,UAAA;QACA;QAEApD,OAAA,CAAAC,GAAA,cAAAM,SAAA,EAAAa,OAAA;QACApB,OAAA,CAAAC,GAAA,UAAAF,iBAAA;;QAEA;QACA,IAAAH,MAAA;;QAEA;QACA,MAAAwC,SAAA,OAAAvD,IAAA,GAAAD,OAAA;QACAgB,MAAA,CAAAyD,EAAA,GAAAjB,SAAA;QACApC,OAAA,CAAAC,GAAA,aAAAmC,SAAA;;QAEA;QACA,IAAA7B,SAAA,IAAAa,OAAA;UACAxB,MAAA,CAAA0D,UAAA,GAAA/C,SAAA;UACAX,MAAA,CAAA2D,QAAA,GAAAnC,OAAA;UACApB,OAAA,CAAAC,GAAA,8CAAAL,MAAA;QACA;;QAEA;QACA,IAAAG,iBAAA;UACAH,MAAA,CAAA4D,kBAAA,GAAAzD,iBAAA;UACAC,OAAA,CAAAC,GAAA,iDAAAL,MAAA;QACA;;QAEA;QACA,IAAAG,iBAAA;UACAC,OAAA,CAAAC,GAAA,cAAAF,iBAAA;UACA;YACA,MAAA0D,QAAA,SAAA9G,cAAA,CAAA+G,sBAAA,CAAA3D,iBAAA;YACAC,OAAA,CAAAC,GAAA,gBAAAwD,QAAA;;YAEA;YACA,IAAAA,QAAA,CAAA3G,IAAA,IAAA2G,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;cACA3D,OAAA,CAAAC,GAAA;;cAEA;cACA,KAAA/C,WAAA,GAAAuG,QAAA,CAAA3G,IAAA,CAAAA,IAAA;cACAkD,OAAA,CAAAC,GAAA,oCAAA/C,WAAA;;cAEA;cACA,MAAA0G,gBAAA,GAAAX,YAAA,CAAAC,OAAA;cACA,IAAAU,gBAAA;gBACA;gBACA,KAAAC,OAAA;kBACAC,KAAA;kBACAxF,OAAA,sBAAApB,WAAA,CAAAsG,kBAAA;kBACAhF,IAAA;kBACAuF,QAAA;gBACA;gBACA;gBACAd,YAAA,CAAAG,UAAA;cACA;;cAEA;cACA,IAAA7C,SAAA,IAAAa,OAAA;gBACApB,OAAA,CAAAC,GAAA,+BAAA/C,WAAA,CAAAsD,cAAA,OAAAtD,WAAA,CAAAmE,YAAA;;gBAEA;gBACA,KAAA2C,iBAAA,QAAA9G,WAAA,CAAAsD,cAAA;gBACA,KAAAyD,eAAA,QAAA/G,WAAA,CAAAmE,YAAA;;gBAEA;gBACA,KAAAnE,WAAA,CAAAsD,cAAA,GAAAD,SAAA;gBACA,KAAArD,WAAA,CAAAmE,YAAA,GAAAD,OAAA;gBAEApB,OAAA,CAAAC,GAAA,mBAAA/C,WAAA,CAAAsD,cAAA,OAAAtD,WAAA,CAAAmE,YAAA;cACA;;cAEA;cACArB,OAAA,CAAAC,GAAA,0BAAA/C,WAAA,CAAAqE,MAAA;cACAvB,OAAA,CAAAC,GAAA,sBAAApB,IAAA;;cAEA;cACA,UAAA3B,WAAA,CAAAqE,MAAA;gBACA;gBACAvB,OAAA,CAAAkE,IAAA;gBACA,KAAAhH,WAAA,CAAAqE,MAAA;cACA;;cAEA;cACAvB,OAAA,CAAAC,GAAA,kBAAA/C,WAAA,CAAAqE,MAAA,gBAAAV,aAAA,MAAA3D,WAAA;cAEA,KAAAH,OAAA;cACA;YACA;cACAiD,OAAA,CAAAkE,IAAA;YACA;UACA,SAAAC,KAAA;YACAnE,OAAA,CAAAmE,KAAA,gBAAAA,KAAA;YACAnE,OAAA,CAAAkE,IAAA;UACA;QACA;;QAEA;QACAlE,OAAA,CAAAC,GAAA,iDAAAJ,IAAA,EAAAE,iBAAA;QACA,MAAA0D,QAAA,SAAA9G,cAAA,CAAAyH,oBAAA,CAAAvE,IAAA,EAAAE,iBAAA;QAEAC,OAAA,CAAAC,GAAA,kBAAAwD,QAAA;QAEA,IAAAA,QAAA,CAAA3G,IAAA,IAAA2G,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;UACA;UACA,KAAAzG,WAAA,GAAAuG,QAAA,CAAA3G,IAAA,CAAAA,IAAA;UACAkD,OAAA,CAAAC,GAAA,oCAAA/C,WAAA;;UAEA;UACA,SAAAA,WAAA,CAAAsG,kBAAA;YACA,KAAAK,OAAA;cACAC,KAAA;cACAxF,OAAA,sBAAApB,WAAA,CAAAsG,kBAAA;cACAhF,IAAA;cACAuF,QAAA;YACA;UACA;;UAEA;UACA,IAAAxD,SAAA,IAAAa,OAAA;YACApB,OAAA,CAAAC,GAAA,+BAAA/C,WAAA,CAAAsD,cAAA,OAAAtD,WAAA,CAAAmE,YAAA;;YAEA;YACA,KAAA2C,iBAAA,QAAA9G,WAAA,CAAAsD,cAAA;YACA,KAAAyD,eAAA,QAAA/G,WAAA,CAAAmE,YAAA;;YAEA;YACA,KAAAnE,WAAA,CAAAsD,cAAA,GAAAD,SAAA;YACA,KAAArD,WAAA,CAAAmE,YAAA,GAAAD,OAAA;YAEApB,OAAA,CAAAC,GAAA,mBAAA/C,WAAA,CAAAsD,cAAA,OAAAtD,WAAA,CAAAmE,YAAA;UACA;;UAEA;UACArB,OAAA,CAAAC,GAAA,0BAAA/C,WAAA,CAAAqE,MAAA;UACAvB,OAAA,CAAAC,GAAA,sBAAApB,IAAA;;UAEA;UACA,UAAA3B,WAAA,CAAAqE,MAAA;YACA;YACAvB,OAAA,CAAAkE,IAAA;YACA,KAAAhH,WAAA,CAAAqE,MAAA;UACA;;UAEA;UACA,SAAArE,WAAA,CAAAsG,kBAAA;YACA;YACA,MAAAa,QAAA,8BAAAnH,WAAA,CAAAsG,kBAAA;YACA,MAAAc,YAAA,GAAArB,YAAA,CAAAC,OAAA,CAAAmB,QAAA;YAEA,IAAAC,YAAA;cACA;gBACA,MAAAC,UAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,YAAA;gBACAtE,OAAA,CAAAC,GAAA,gBAAAsE,UAAA;;gBAEA;gBACA,IAAAA,UAAA,CAAAxE,iBAAA,UAAA7C,WAAA,CAAAsG,kBAAA;kBACA;kBACA,IAAAe,UAAA,CAAAG,QAAA,oBAAAH,UAAA,CAAA1G,YAAA;oBACAmC,OAAA,CAAAC,GAAA,wCAAA/C,WAAA,CAAAsG,kBAAA;oBACA,KAAAtG,WAAA,CAAAqE,MAAA;oBACA,KAAA1D,YAAA;oBACA,KAAAJ,gBAAA,QAAA6D,EAAA;oBACA,KAAA5D,gBAAA;kBACA;gBACA;kBACAsC,OAAA,CAAAC,GAAA;gBACA;cACA,SAAA0E,CAAA;gBACA3E,OAAA,CAAAmE,KAAA,cAAAQ,CAAA;cACA;YACA;UACA;;UAEA;UACA3E,OAAA,CAAAC,GAAA,kBAAA/C,WAAA,CAAAqE,MAAA,gBAAAV,aAAA,MAAA3D,WAAA;QACA;UACA,MAAA0H,QAAA,GAAAnB,QAAA,CAAA3G,IAAA,GAAA2G,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,QAAAgD,EAAA;UACA,KAAAuD,QAAA,CAAAV,KAAA,CAAAS,QAAA;UACA,KAAA1H,WAAA;QACA;MACA,SAAAiH,KAAA;QACAnE,OAAA,CAAAmE,KAAA,iCAAAA,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA,MAAA7C,EAAA;QACA,KAAApE,WAAA;MACA;QACA,KAAAH,OAAA;MACA;IACA;IAEA,MAAAwC,gBAAA;MACA;QACA,KAAAxC,OAAA;QACA;QACA,MAAA8C,IAAA,QAAAqB,MAAA,CAAAtB,MAAA,CAAAC,IAAA;QACA,KAAAA,IAAA;UACA,KAAAsE,KAAA;UACA,KAAApH,OAAA;UACA;QACA;;QAEA;QACA,MAAA6C,MAAA;;QAEA;QACA,MAAAwC,SAAA,OAAAvD,IAAA,GAAAD,OAAA;QACAgB,MAAA,CAAAyD,EAAA,GAAAjB,SAAA;QACApC,OAAA,CAAAC,GAAA,aAAAmC,SAAA;;QAEA;QACA,MAAA7B,SAAA,QAAAW,MAAA,CAAApB,KAAA,CAAAS,SAAA;QACA,MAAAa,OAAA,QAAAF,MAAA,CAAApB,KAAA,CAAAsB,OAAA;QAEA,IAAAb,SAAA;UACAX,MAAA,CAAA0D,UAAA,GAAA/C,SAAA;UACAP,OAAA,CAAAC,GAAA,YAAAM,SAAA;QACA;QACA,IAAAa,OAAA;UACAxB,MAAA,CAAA2D,QAAA,GAAAnC,OAAA;UACApB,OAAA,CAAAC,GAAA,YAAAmB,OAAA;QACA;;QAEA;QACA,IAAAqC,QAAA;;QAEA;QACA,MAAA1D,iBAAA,QAAAmB,MAAA,CAAApB,KAAA,CAAAC,iBAAA;QACA,IAAAA,iBAAA;UACAC,OAAA,CAAAC,GAAA,cAAAF,iBAAA;UACA;UACA0D,QAAA,cAAAqB,IAAA,CAAA5H,WAAA,CAAAkH,oBAAA,CAAAvE,IAAA,EAAAE,iBAAA;QACA,WAAAQ,SAAA,IAAAa,OAAA;UACApB,OAAA,CAAAC,GAAA;YAAAqD,UAAA,EAAA/C,SAAA;YAAAgD,QAAA,EAAAnC;UAAA;UACA;UACAqC,QAAA,cAAAqB,IAAA,CAAA5H,WAAA,CAAA6H,8BAAA,CAAAlF,IAAA,EAAAD,MAAA;QACA;UACAI,OAAA,CAAAC,GAAA;UACA;UACAwD,QAAA,cAAAqB,IAAA,CAAA5H,WAAA,CAAAkH,oBAAA,CAAAvE,IAAA;QACA;QAEAG,OAAA,CAAAC,GAAA,aAAAwD,QAAA;QAEA,IAAAA,QAAA,CAAAE,OAAA;UACA,KAAAzG,WAAA,GAAAuG,QAAA,CAAA3G,IAAA;;UAEA;UACA,KAAAa,WAAA,KAAA8F,QAAA,CAAA3G,IAAA,CAAAkI,YAAA;UACAhF,OAAA,CAAAC,GAAA,iBAAAtC,WAAA;;UAEA;UACA,SAAAT,WAAA,CAAAqE,MAAA;YACA,KAAAX,UAAA,QAAAU,EAAA;YACA,KAAAP,UAAA;UACA,gBAAA7D,WAAA,CAAAqE,MAAA;YACA,KAAAX,UAAA,QAAAU,EAAA;YACA,KAAAP,UAAA;;YAEA;YACA,SAAApD,WAAA;cACA,KAAAE,YAAA;cACAmC,OAAA,CAAAC,GAAA;YACA;UACA;;UAEA;UACA,SAAA/C,WAAA,CAAA+H,YAAA;YACA,KAAAC,aAAA,MAAAhI,WAAA,CAAA+H,YAAA;UACA;QACA;UACA,KAAAd,KAAA,GAAAV,QAAA,CAAAnF,OAAA;QACA;MACA,SAAA6F,KAAA;QACAnE,OAAA,CAAAmE,KAAA,cAAAA,KAAA;QACA,KAAAA,KAAA,mBAAAA,KAAA,CAAA7F,OAAA,IAAA6F,KAAA;MACA;QACA,KAAApH,OAAA;MACA;IACA;IAEA2E,eAAAyD,UAAA;MACA,KAAAA,UAAA;MAEA,MAAAC,IAAA,OAAAvG,IAAA,CAAAsG,UAAA;MACA,UAAAC,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAM,QAAA,IAAAF,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAO,UAAA,IAAAH,QAAA;IACA;IAEA;IACA3E,cAAA3D,WAAA;MACA;MACA8C,OAAA,CAAAC,GAAA,8BAAA/C,WAAA,CAAAqE,MAAA;;MAEA;MACA,IAAArE,WAAA,CAAAqE,MAAA;QACAvB,OAAA,CAAAC,GAAA;QACA,YAAAqB,EAAA;MACA;;MAEA;MACA,IAAA1E,oBAAA,CAAAM,WAAA,CAAAmE,YAAA;QACArB,OAAA,CAAAC,GAAA;QACA,YAAAqB,EAAA;MACA;;MAEA;MACA,MAAAxC,GAAA,OAAAD,IAAA;MACA,MAAA+G,KAAA,OAAA/G,IAAA,CAAA3B,WAAA,CAAAsD,cAAA;MACA,MAAAqF,GAAA,OAAAhH,IAAA,CAAA3B,WAAA,CAAAmE,YAAA;MACA,IAAAvC,GAAA,IAAA8G,KAAA,IAAA9G,GAAA,IAAA+G,GAAA;QACA7F,OAAA,CAAAC,GAAA;QACA,YAAAqB,EAAA;MACA;;MAEA;MACAtB,OAAA,CAAAC,GAAA;MACA,YAAAqB,EAAA;IACA;IAEA;IACAN,cAAA9D,WAAA;MACA;MACA,IAAAA,WAAA,CAAAqE,MAAA;QACA;MACA;;MAEA;MACA,IAAA3E,oBAAA,CAAAM,WAAA,CAAAmE,YAAA;QACA;MACA;;MAEA;MACA,MAAAvC,GAAA,OAAAD,IAAA;MACA,MAAA+G,KAAA,OAAA/G,IAAA,CAAA3B,WAAA,CAAAsD,cAAA;MACA,MAAAqF,GAAA,OAAAhH,IAAA,CAAA3B,WAAA,CAAAmE,YAAA;MACA,IAAAvC,GAAA,IAAA8G,KAAA,IAAA9G,GAAA,IAAA+G,GAAA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC,wBAAA5I,WAAA;MACA,KAAAA,WAAA;MAEA,MAAA4B,GAAA,OAAAD,IAAA;MACA,MAAA+G,KAAA,OAAA/G,IAAA,CAAA3B,WAAA,CAAAsD,cAAA;MACA,MAAAqF,GAAA,OAAAhH,IAAA,CAAA3B,WAAA,CAAAmE,YAAA;;MAEA;MACA,OAAAvC,GAAA,IAAA8G,KAAA,IAAA9G,GAAA,IAAA+G,GAAA;IACA;IAEA;IACAE,aAAA;MACA;MACA,KAAAhI,UAAA;QACAC,aAAA,OAAAd,WAAA,CAAAsD,cAAA;QACAvC,WAAA,OAAAf,WAAA,CAAAmE,YAAA;QACAnD,OAAA,OAAAhB,WAAA,CAAAgB,OAAA;QACAC,SAAA,OAAAjB,WAAA,CAAA8I,UAAA;MACA;;MAEA;MACA,KAAA3I,mBAAA;IACA;IAEA;IACA4I,kBAAA;MACA,MAAA1F,SAAA,OAAA1B,IAAA,MAAAd,UAAA,CAAAC,aAAA;MACA,MAAAoD,OAAA,OAAAvC,IAAA,MAAAd,UAAA,CAAAE,WAAA;MAEA,IAAAsC,SAAA,IAAAa,OAAA;QACA,KAAAyD,QAAA,CAAAV,KAAA,MAAA7C,EAAA;QACA;MACA;MAEA;IACA;IAEA;IACA,MAAA4E,iBAAA;MACA;QACA;QACA,WAAAC,KAAA,CAAApI,UAAA,CAAAqI,QAAA;;QAEA;QACA,UAAAH,iBAAA;QAEA,KAAAhJ,SAAA;;QAEA;QACA,MAAAoJ,UAAA;UACA7F,cAAA,OAAAzC,UAAA,CAAAC,aAAA;UACAqD,YAAA,OAAAtD,UAAA,CAAAE,WAAA;UACAC,OAAA,OAAAH,UAAA,CAAAG,OAAA,IAAAoI,SAAA;UACAN,UAAA,OAAAjI,UAAA,CAAAI,SAAA,IAAAmI,SAAA;UACAC,IAAA,OAAAC,KAAA,CAAAC;QACA;;QAEA;QACA,MAAAhD,QAAA,SAAA9G,cAAA,CAAA+J,iBAAA,CACA,KAAAxJ,WAAA,CAAAyJ,gBAAA,EACAN,UAAA,EACA,KAAAnJ,WAAA,CAAAsG,kBAAA;QACA;QAEA,IAAAC,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;UACA,KAAAkB,QAAA,CAAAlB,OAAA,MAAArC,EAAA;UACA,KAAAjE,mBAAA;UACA;UACA,WAAA2B,gBAAA;;UAEA;UACAiE,YAAA,CAAA2D,OAAA;QACA;UACA,KAAA/B,QAAA,CAAAV,KAAA,CAAAV,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,SAAAgD,EAAA;QACA;MACA,SAAA6C,KAAA;QACAnE,OAAA,CAAAmE,KAAA,YAAAA,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA,MAAA7C,EAAA;MACA;QACA,KAAArE,SAAA;MACA;IACA;IAEA4J,aAAA;MACA,KAAA1J,mBAAA;IACA;IAEA2J,aAAA;MACA,KAAA1J,mBAAA;IACA;IAEA,MAAA2J,cAAA;MACA,KAAA/J,UAAA;MAEA;QACA;QACA,SAAAE,WAAA,CAAA8J,wBAAA;UACAhH,OAAA,CAAAC,GAAA,kEAAA/C,WAAA,CAAA8J,wBAAA;;UAEA;UACA,MAAAC,eAAA,QAAA/J,WAAA,CAAAyJ,gBAAA;UACA,MAAA5G,iBAAA,QAAA7C,WAAA,CAAAsG,kBAAA;;UAEA;UACA,MAAA1G,IAAA;;UAEA;UACA,IAAAiD,iBAAA;YACAjD,IAAA,CAAA0G,kBAAA,GAAAzD,iBAAA;YACAC,OAAA,CAAAC,GAAA,wBAAAF,iBAAA;UACA;YACAC,OAAA,CAAAkE,IAAA;UACA;;UAEA;UACA,MAAAT,QAAA,SAAA9G,cAAA,CAAAuK,iBAAA,CAAAD,eAAA,EAAAnK,IAAA;UAEAkD,OAAA,CAAAC,GAAA,uCAAAwD,QAAA;;UAEA;UACA,IAAAA,QAAA,CAAA3G,IAAA;YACA;YACA,IAAA2G,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,gBAAAmF,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,CAAA6I,QAAA;cACAnH,OAAA,CAAAC,GAAA;;cAEA;cACA,KAAA9C,mBAAA;;cAEA;cACA,KAAAD,WAAA,CAAAqE,MAAA;;cAEA;cACA,KAAA6F,iBAAA;;cAEA;cACA,IAAArH,iBAAA;gBACA,KAAAsH,6BAAA,CAAAtH,iBAAA;cACA;;cAEA;cACA,KAAA8E,QAAA,CAAAlB,OAAA,MAAArC,EAAA;;cAEA;cACA,KAAAgG,QAAA,CACA,yBACA,QACA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAhJ,IAAA;cACA,CACA,EAAAiJ,IAAA;gBACA;gBACA,KAAAC,OAAA,CAAAjF,IAAA,gCAAAvF,WAAA,CAAA8J,wBAAA;cACA,GAAAW,KAAA;gBACA;gBACA,KAAA3I,gBAAA;cACA;cACA;YACA,WAAAyE,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;cACA;cACA;cACA,KAAAxG,mBAAA;;cAEA;cACA,KAAAD,WAAA,CAAAqE,MAAA;;cAEA;cACA,KAAA6F,iBAAA;;cAEA;cACA,IAAArH,iBAAA;gBACA,KAAAsH,6BAAA,CAAAtH,iBAAA;cACA;;cAEA;cACA,KAAA8E,QAAA,CAAAlB,OAAA,MAAArC,EAAA;;cAEA;cACA,KAAAgG,QAAA,CACA,yBACA,QACA;gBACAC,iBAAA;gBACAC,gBAAA;gBACAhJ,IAAA;cACA,CACA,EAAAiJ,IAAA;gBACA;gBACA,KAAAC,OAAA,CAAAjF,IAAA,gCAAAvF,WAAA,CAAA8J,wBAAA;cACA,GAAAW,KAAA;gBACA;gBACA,KAAA3I,gBAAA;cACA;YACA;cACA;cACA,MAAA4F,QAAA,GAAAnB,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,SAAAgD,EAAA;cACA,KAAAuD,QAAA,CAAAV,KAAA,CAAAS,QAAA;cACA;cACA,KAAAzH,mBAAA;YACA;UACA;QACA;UACA;UACA;UACA,MAAAL,IAAA;;UAEA;UACA,SAAAI,WAAA,CAAAsG,kBAAA;YACA1G,IAAA,CAAA0G,kBAAA,QAAAtG,WAAA,CAAAsG,kBAAA;YACAxD,OAAA,CAAAC,GAAA,mBAAA/C,WAAA,CAAAsG,kBAAA;UACA;YACAxD,OAAA,CAAAkE,IAAA;UACA;UAEAlE,OAAA,CAAAC,GAAA,cAAAnD,IAAA;UAEA,MAAA2G,QAAA,SAAA9G,cAAA,CAAAuK,iBAAA,MAAAhK,WAAA,CAAAyJ,gBAAA,EAAA7J,IAAA;UAEAkD,OAAA,CAAAC,GAAA,qBAAAwD,QAAA;;UAEA;UACA,IAAAA,QAAA,CAAA3G,IAAA;YACA;YACA,IAAA2G,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,gBAAAmF,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,CAAA6I,QAAA;cACAnH,OAAA,CAAAC,GAAA;;cAEA;cACA,KAAA9C,mBAAA;;cAEA;cACA,KAAA0H,QAAA,CAAAlB,OAAA,MAAArC,EAAA;;cAEA;cACAtB,OAAA,CAAAC,GAAA;;cAEA;cACA2H,UAAA;gBACA;gBACA3I,MAAA,CAAA4I,QAAA,CAAAC,IAAA;gBACA7I,MAAA,CAAA4I,QAAA,CAAAE,MAAA;cACA;cACA;YACA,WAAAtE,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;cACA;cACA;cACA,KAAAxG,mBAAA;;cAEA;cACA,KAAA0H,QAAA,CAAAlB,OAAA,MAAArC,EAAA;;cAEA;cACAtB,OAAA,CAAAC,GAAA;;cAEA;cACA2H,UAAA;gBACA;gBACA3I,MAAA,CAAA4I,QAAA,CAAAC,IAAA;gBACA7I,MAAA,CAAA4I,QAAA,CAAAE,MAAA;cACA;YACA;cACA;cACA,MAAAnD,QAAA,GAAAnB,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,SAAAgD,EAAA;cACA,KAAAuD,QAAA,CAAAV,KAAA,CAAAS,QAAA;cACA;cACA,KAAAzH,mBAAA;YACA;UACA;QACA;MACA,SAAAgH,KAAA;QACAnE,OAAA,CAAAmE,KAAA,kCAAAA,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA,MAAA7C,EAAA;QACA;QACA,KAAAnE,mBAAA;MACA;QACA,KAAAH,UAAA;MACA;IACA;IAEA,MAAAgL,cAAA;MACA,KAAAhL,UAAA;MAEA;QACA;QACA,MAAAF,IAAA;UACAmL,YAAA;QACA;;QAEA;QACA,SAAA/K,WAAA,CAAAsG,kBAAA;UACA1G,IAAA,CAAA0G,kBAAA,QAAAtG,WAAA,CAAAsG,kBAAA;UACAxD,OAAA,CAAAC,GAAA,0BAAA/C,WAAA,CAAAsG,kBAAA;QACA;UACAxD,OAAA,CAAAkE,IAAA;QACA;QAEAlE,OAAA,CAAAC,GAAA,iBAAAnD,IAAA;QAEA,MAAA2G,QAAA,SAAA9G,cAAA,CAAAuK,iBAAA,MAAAhK,WAAA,CAAAyJ,gBAAA,EAAA7J,IAAA;QAEAkD,OAAA,CAAAC,GAAA,qBAAAwD,QAAA;QAEA,IAAAA,QAAA,CAAA3G,IAAA,IAAA2G,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;UACA;UACA,KAAAvG,mBAAA;;UAEA;UACA,KAAAyH,QAAA,CAAAlB,OAAA,MAAArC,EAAA;;UAEA;UACA,KAAAtC,gBAAA;QACA;UACA,MAAA4F,QAAA,GAAAnB,QAAA,CAAA3G,IAAA,GAAA2G,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,QAAAgD,EAAA;UACA,KAAAuD,QAAA,CAAAV,KAAA,CAAAS,QAAA;UACA,KAAAxH,mBAAA;QACA;MACA,SAAA+G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,gCAAAA,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA,MAAA7C,EAAA;QACA,KAAAlE,mBAAA;MACA;QACA,KAAAJ,UAAA;MACA;IACA;IAEAkL,OAAA;MACA;MACA,MAAAC,aAAA,QAAAjH,MAAA,CAAApB,KAAA,CAAAqI,aAAA;MAEA,IAAAA,aAAA;QACA;QACA,KAAAT,OAAA,CAAAjF,IAAA;MACA,WAAAxD,MAAA,CAAAmJ,OAAA,CAAAvG,MAAA;QACA;QACA;QACAoB,YAAA,CAAA2D,OAAA;;QAEA;QACA,KAAAc,OAAA,CAAAW,EAAA;;QAEA;QACAT,UAAA;UACA3E,YAAA,CAAAG,UAAA;QACA;MACA;QACA;QACA,KAAAsE,OAAA,CAAAjF,IAAA;MACA;IACA;IAEA;IACAtD,UAAA;MACA,UAAAjC,WAAA;;MAEA;MACA,MAAA0D,UAAA,QAAAC,aAAA,MAAA3D,WAAA;MACA,MAAA6D,UAAA,QAAAC,aAAA,MAAA9D,WAAA;;MAEA;MACA,MAAAoL,QAAA,8BAAApL,WAAA,CAAAyJ,gBAAA;MACA,MAAA4B,KAAA;QACA3H,UAAA;QACAG,UAAA;QACAqB,SAAA,MAAAvD,IAAA,GAAAD,OAAA;MACA;MAEAoB,OAAA,CAAAC,GAAA,kCAAAsI,KAAA;MACAtF,YAAA,CAAA2D,OAAA,CAAA0B,QAAA,EAAA9D,IAAA,CAAAgE,SAAA,CAAAD,KAAA;IACA;IAEA5H,cAAA;MACA,UAAAzD,WAAA;;MAEA;MACA,MAAAoL,QAAA,8BAAApL,WAAA,CAAAyJ,gBAAA;MACA,MAAA8B,aAAA,GAAAxF,YAAA,CAAAC,OAAA,CAAAoF,QAAA;MAEA,KAAAG,aAAA;MAEA;QACA,MAAA/H,UAAA,GAAA8D,IAAA,CAAAC,KAAA,CAAAgE,aAAA;QACAzI,OAAA,CAAAC,GAAA,2BAAAS,UAAA;;QAEA;QACA,MAAA5B,GAAA,OAAAD,IAAA,GAAAD,OAAA;QACA,MAAA8J,WAAA;QACA,IAAA5J,GAAA,GAAA4B,UAAA,CAAA0B,SAAA,GAAAsG,WAAA;UACA1I,OAAA,CAAAC,GAAA;UACAgD,YAAA,CAAAG,UAAA,CAAAkF,QAAA;UACA;QACA;QAEA,OAAA5H,UAAA;MACA,SAAAiE,CAAA;QACA3E,OAAA,CAAAmE,KAAA,+BAAAQ,CAAA;QACA;MACA;IACA;IAEA;IACAgE,uBAAAC,SAAA;MACA5I,OAAA,CAAAC,GAAA,cAAA2I,SAAA;;MAEA;MACA,IAAAhI,UAAA;MACA,IAAAG,UAAA;MAEA,IAAA6H,SAAA;QACAhI,UAAA,QAAAU,EAAA;QACAP,UAAA;MACA,WAAA6H,SAAA;QACA,MAAA9J,GAAA,OAAAD,IAAA;QACA,MAAA+G,KAAA,OAAA/G,IAAA,MAAA3B,WAAA,CAAAsD,cAAA;QACA,MAAAqF,GAAA,OAAAhH,IAAA,MAAA3B,WAAA,CAAAmE,YAAA;QAEA,IAAAvC,GAAA,IAAA8G,KAAA,IAAA9G,GAAA,IAAA+G,GAAA;UACA;UACAjF,UAAA,QAAAU,EAAA;UACAP,UAAA;QACA,WAAAnE,oBAAA,MAAAM,WAAA,CAAAmE,YAAA;UACA;UACAT,UAAA,QAAAU,EAAA;UACAP,UAAA;QACA;UACA;UACAH,UAAA,QAAAU,EAAA;UACAP,UAAA;QACA;MACA;MAEAf,OAAA,CAAAC,GAAA,cAAAW,UAAA,EAAAG,UAAA;;MAEA;MACA,MAAAjB,KAAA;QAAA,QAAAoB,MAAA,CAAApB,KAAA;QAAAqB,aAAA,EAAAP,UAAA;QAAAY,iBAAA,EAAAT;MAAA;;MAEA;MACA,KAAA2G,OAAA,CAAAmB,OAAA;QACAC,IAAA,OAAA5H,MAAA,CAAA4H,IAAA;QACAhJ;MACA,GAAA6H,KAAA,CAAAoB,GAAA;QACA;QACA,IAAAA,GAAA,CAAAlM,IAAA;UACA,MAAAkM,GAAA;QACA;MACA;IACA;IAEA;IACA3B,kBAAAwB,SAAA;MACA5I,OAAA,CAAAC,GAAA,aAAA2I,SAAA;;MAEA;MACA,IAAAhI,UAAA;MACA,IAAAG,UAAA;MAEA,IAAA6H,SAAA;QACAhI,UAAA,QAAAU,EAAA;QACAP,UAAA;;QAEA;QACA,SAAA7D,WAAA,SAAAA,WAAA,CAAAsG,kBAAA;UACA;UACA,MAAA8E,QAAA,8BAAApL,WAAA,CAAAsG,kBAAA;UACA,MAAA+E,KAAA;YACA3H,UAAA,OAAAU,EAAA;YACAP,UAAA;YACA2D,QAAA;YACA7G,YAAA;YACAuE,SAAA,MAAAvD,IAAA,GAAAD,OAAA;YACAoK,SAAA;YACAjJ,iBAAA,OAAA7C,WAAA,CAAAsG;UACA;UACAxD,OAAA,CAAAC,GAAA,sBAAAsI,KAAA;UACAtF,YAAA,CAAA2D,OAAA,CAAA0B,QAAA,EAAA9D,IAAA,CAAAgE,SAAA,CAAAD,KAAA;QACA;;QAEA;QACA,SAAArL,WAAA;UACA,KAAAA,WAAA,CAAAqE,MAAA;QACA;;QAEA;QACA,KAAA9D,gBAAA,GAAAmD,UAAA;QACA,KAAAlD,gBAAA,GAAAqD,UAAA;QACA,KAAAlD,YAAA;MACA,WAAA+K,SAAA;QACA,MAAA9J,GAAA,OAAAD,IAAA;QACA,MAAA+G,KAAA,OAAA/G,IAAA,MAAA3B,WAAA,CAAAsD,cAAA;QACA,MAAAqF,GAAA,OAAAhH,IAAA,MAAA3B,WAAA,CAAAmE,YAAA;QAEA,IAAAvC,GAAA,IAAA8G,KAAA,IAAA9G,GAAA,IAAA+G,GAAA;UACA;UACAjF,UAAA,QAAAU,EAAA;UACAP,UAAA;QACA,WAAAnE,oBAAA,MAAAM,WAAA,CAAAmE,YAAA;UACA;UACAT,UAAA,QAAAU,EAAA;UACAP,UAAA;QACA;UACA;UACAH,UAAA,QAAAU,EAAA;UACAP,UAAA;QACA;;QAEA;QACA,SAAA7D,WAAA;UACA,KAAAA,WAAA,CAAAqE,MAAA;;UAEA;UACA,SAAArE,WAAA,CAAAsG,kBAAA;YACA,MAAA8E,QAAA,8BAAApL,WAAA,CAAAsG,kBAAA;YACAP,YAAA,CAAAG,UAAA,CAAAkF,QAAA;UACA;QACA;;QAEA;QACA,KAAA7K,gBAAA,GAAAmD,UAAA;QACA,KAAAlD,gBAAA,GAAAqD,UAAA;QACA,KAAAlD,YAAA;MACA;;MAEA;MACA,KAAA8K,sBAAA,CAAAC,SAAA;IACA;IAEA;IACAvB,8BAAAtH,iBAAA,EAAAwB,MAAA;MACA,KAAAxB,iBAAA;MAEAC,OAAA,CAAAC,GAAA,gBAAAF,iBAAA,EAAAwB,MAAA;;MAEA;MACA,MAAA8C,QAAA,yBAAAtE,iBAAA;MAEA,IAAAa,UAAA;MACA,IAAAG,UAAA;MAEA,IAAAQ,MAAA;QACAX,UAAA,QAAAU,EAAA;QACAP,UAAA;MACA,WAAAQ,MAAA;QACAX,UAAA,QAAAU,EAAA;QACAP,UAAA;MACA;;MAEA;MACA,MAAAwH,KAAA;QACA3H,UAAA;QACAG,UAAA;QACA2D,QAAA,EAAAnD,MAAA;QACA1D,YAAA,EAAA0D,MAAA;QACAa,SAAA,MAAAvD,IAAA,GAAAD,OAAA;QACAoK,SAAA;QACAjJ,iBAAA,EAAAA;MACA;MAEAC,OAAA,CAAAC,GAAA,iBAAAoE,QAAA,EAAAkE,KAAA;MACAtF,YAAA,CAAA2D,OAAA,CAAAvC,QAAA,EAAAG,IAAA,CAAAgE,SAAA,CAAAD,KAAA;IACA;IAEA;IACAU,eAAAC,YAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,UAAA;MACA,KAAAH,YAAA,KAAAC,UAAA,KAAAC,YAAA,KAAAC,UAAA;QACA;MACA;MAEArJ,OAAA,CAAAC,GAAA;QACAiJ,YAAA;QACAC,UAAA;QACAC,YAAA;QACAC;MACA;;MAEA;MACA,MAAAC,UAAA,GAAAC,OAAA;QACA;UACA;UACA,MAAAnE,IAAA,OAAAvG,IAAA,CAAA0K,OAAA;UACA,IAAAC,KAAA,CAAApE,IAAA,CAAAxG,OAAA;YACA;YACA,OAAA2K,OAAA;UACA;;UAEA;UACA,MAAAE,IAAA,GAAArE,IAAA,CAAAC,WAAA;UACA,MAAAqE,KAAA,GAAApE,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA;UACA,MAAAmE,GAAA,GAAArE,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA;;UAEA;UACA,UAAAiE,IAAA,IAAAC,KAAA,IAAAC,GAAA;QACA,SAAAhF,CAAA;UACA3E,OAAA,CAAAmE,KAAA,aAAAQ,CAAA;UACA,OAAA4E,OAAA;QACA;MACA;MAEA,MAAAK,iBAAA,GAAAN,UAAA,CAAAJ,YAAA;MACA,MAAAW,eAAA,GAAAP,UAAA,CAAAH,UAAA;MACA,MAAAW,iBAAA,GAAAR,UAAA,CAAAF,YAAA;MACA,MAAAW,eAAA,GAAAT,UAAA,CAAAD,UAAA;MAEArJ,OAAA,CAAAC,GAAA;QACA2J,iBAAA;QACAC,eAAA;QACAC,iBAAA;QACAC;MACA;;MAEA;MACA,MAAAC,OAAA,GAAAJ,iBAAA,KAAAE,iBAAA,IAAAD,eAAA,KAAAE,eAAA;MAEA/J,OAAA,CAAAC,GAAA,YAAA+J,OAAA;MAEA,OAAAA,OAAA;IACA;IAEA;IACAC,oBAAAf,YAAA,EAAAC,UAAA,EAAAC,YAAA,EAAAC,UAAA;MACA,KAAAH,YAAA,KAAAC,UAAA,KAAAC,YAAA,KAAAC,UAAA;QACA;MACA;MAEArJ,OAAA,CAAAC,GAAA;QACAiJ,YAAA;QACAC,UAAA;QACAC,YAAA;QACAC;MACA;MAEA;QACA;QACA;;QAEA;QACA,IAAAa,QAAA,EAAAC,MAAA;QACA,WAAAjB,YAAA;UACA;UACA,IAAAA,YAAA,CAAA/B,QAAA;YACA+C,QAAA,OAAArL,IAAA,CAAAqK,YAAA;UACA;YACA;YACA,MAAAkB,KAAA,GAAAlB,YAAA,CAAAmB,KAAA;YACA,IAAAD,KAAA,CAAAvI,MAAA;cACA,MAAAyI,SAAA,GAAAF,KAAA,IAAAC,KAAA;cACA,MAAAE,SAAA,GAAAH,KAAA,IAAAC,KAAA;cACAH,QAAA,OAAArL,IAAA,CACA2L,QAAA,CAAAF,SAAA,MACAE,QAAA,CAAAF,SAAA;cAAA;cACAE,QAAA,CAAAF,SAAA,MACAE,QAAA,CAAAD,SAAA,MACAC,QAAA,CAAAD,SAAA,IACA;YACA;cACAL,QAAA,OAAArL,IAAA,CAAAqK,YAAA;YACA;UACA;QACA;UACAgB,QAAA,OAAArL,IAAA,CAAAqK,YAAA;QACA;QAEA,WAAAC,UAAA;UACA,IAAAA,UAAA,CAAAhC,QAAA;YACAgD,MAAA,OAAAtL,IAAA,CAAAsK,UAAA;UACA;YACA,MAAAiB,KAAA,GAAAjB,UAAA,CAAAkB,KAAA;YACA,IAAAD,KAAA,CAAAvI,MAAA;cACA,MAAAyI,SAAA,GAAAF,KAAA,IAAAC,KAAA;cACA,MAAAE,SAAA,GAAAH,KAAA,IAAAC,KAAA;cACAF,MAAA,OAAAtL,IAAA,CACA2L,QAAA,CAAAF,SAAA,MACAE,QAAA,CAAAF,SAAA,UACAE,QAAA,CAAAF,SAAA,MACAE,QAAA,CAAAD,SAAA,MACAC,QAAA,CAAAD,SAAA,IACA;YACA;cACAJ,MAAA,OAAAtL,IAAA,CAAAsK,UAAA;YACA;UACA;QACA;UACAgB,MAAA,OAAAtL,IAAA,CAAAsK,UAAA;QACA;;QAEA;QACA,IAAAsB,QAAA,OAAA5L,IAAA,CAAAuK,YAAA;QACA,IAAAsB,MAAA,OAAA7L,IAAA,CAAAwK,UAAA;;QAEA;QACA,MAAAsB,gBAAA,GAAAvF,IAAA;UACA,IAAAoE,KAAA,CAAApE,IAAA,CAAAxG,OAAA;YACAoB,OAAA,CAAAmE,KAAA,aAAAiB,IAAA;YACA;UACA;UACA,UAAAA,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAK,OAAA,IAAAD,QAAA;QACA;QAEA,MAAAoE,iBAAA,GAAAe,gBAAA,CAAAT,QAAA;QACA,MAAAL,eAAA,GAAAc,gBAAA,CAAAR,MAAA;QACA,MAAAL,iBAAA,GAAAa,gBAAA,CAAAF,QAAA;QACA,MAAAV,eAAA,GAAAY,gBAAA,CAAAD,MAAA;QAEA1K,OAAA,CAAAC,GAAA;UACA2J,iBAAA;UACAC,eAAA;UACAC,iBAAA;UACAC;QACA;;QAEA;QACA,MAAAC,OAAA,GAAAJ,iBAAA,KAAAE,iBAAA,IAAAD,eAAA,KAAAE,eAAA;QAEA/J,OAAA,CAAAC,GAAA,cAAA+J,OAAA;QAEA,OAAAA,OAAA;MACA,SAAArF,CAAA;QACA3E,OAAA,CAAAmE,KAAA,cAAAQ,CAAA;QACA;QACA;MACA;IACA;IACA;IACA,MAAAiG,YAAA;MACA,UAAA1N,WAAA;MAEA,KAAAI,oBAAA;MACA,KAAAE,cAAA;MAEA;QACA;QACA,MAAAiG,QAAA,cAAAqB,IAAA,CAAA5H,WAAA,CAAA2N,qBAAA,CACA,KAAA3N,WAAA,CAAAyJ,gBAAA,EACA,KAAAzJ,WAAA,CAAAsG,kBACA;QAEA,IAAAC,QAAA,CAAA3G,IAAA,IAAA2G,QAAA,CAAA3G,IAAA,CAAA6G,OAAA;UACA,KAAApG,cAAA,GAAAkG,QAAA,CAAA3G,IAAA,CAAAA,IAAA;QACA;UACA,MAAA8H,QAAA,GAAAnB,QAAA,CAAA3G,IAAA,GAAA2G,QAAA,CAAA3G,IAAA,CAAAwB,OAAA,QAAAgD,EAAA;UACA,KAAAuD,QAAA,CAAAV,KAAA,CAAAS,QAAA;UACA,KAAArH,cAAA;QACA;MACA,SAAA4G,KAAA;QACAnE,OAAA,CAAAmE,KAAA,cAAAA,KAAA;QACA,KAAAU,QAAA,CAAAV,KAAA,MAAA7C,EAAA;QACA,KAAA/D,cAAA;MACA;QACA,KAAAC,cAAA;MACA;IACA;IAEA;IACAsN,oBAAAC,SAAA;MACA,MAAAC,QAAA;QACA,uBAAA1J,EAAA;QACA,qBAAAA,EAAA;QACA,gBAAAA,EAAA;QACA,kBAAAA,EAAA;QACA,qBAAAA,EAAA;QACA,wBAAAA,EAAA;QACA,mBAAAA,EAAA;QACA,eAAAA,EAAA;QACA,aAAAA,EAAA;MACA;MAEA,OAAA0J,QAAA,CAAAD,SAAA,KAAAA,SAAA;IACA;IAEA;IACAE,qBAAAC,MAAA;MACA,MAAAC,SAAA;QACA,eAAA7J,EAAA;QACA,eAAAA,EAAA;QACA,eAAAA,EAAA;QACA,sBAAAA,EAAA;MACA;MAEA,OAAA6J,SAAA,CAAAD,MAAA,KAAAA,MAAA;IACA;IAEA;IACAE,mBAAAL,SAAA,EAAAM,KAAA;MACA,KAAAA,KAAA;MAEA,IAAAN,SAAA,yBAAAA,SAAA;QACA,YAAArJ,cAAA,CAAA2J,KAAA;MACA,WAAAN,SAAA;QACA,MAAAO,SAAA;UACA,kBAAAhK,EAAA;UACA,kBAAAA,EAAA;UACA,eAAAA,EAAA;UACA,gBAAAA,EAAA;QACA;QACA,OAAAgK,SAAA,CAAAD,KAAA,KAAAA,KAAA;MACA;MAEA,OAAAA,KAAA;IACA;IAEA;IACAE,yBAAA;MACA,UAAArO,WAAA,UAAAA,WAAA,CAAA8J,wBAAA;;MAEA;MACA,IAAA/D,YAAA,CAAAC,OAAA;QACAlD,OAAA,CAAAC,GAAA;QACA;MACA;;MAEA;MACA,KAAAyH,OAAA,CAAAjF,IAAA;QACAqG,IAAA,iCAAA5L,WAAA,CAAA8J,wBAAA;QACAlH,KAAA;UACA0L,SAAA;UACAvE,eAAA,OAAA/J,WAAA,CAAAyJ,gBAAA;UACAwB,aAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}