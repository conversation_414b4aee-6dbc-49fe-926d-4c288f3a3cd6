{"ast": null, "code": "import { reservationApi, equipmentApi } from '@/api';\nimport { formatDate } from '@/utils/date';\nexport default {\n  name: 'ReservationDetail',\n  props: {\n    isAdmin: {\n      type: Boolean,\n      default: false\n    }\n  },\n  data() {\n    return {\n      loading: true,\n      reservation: null,\n      cancelDialogVisible: false,\n      cancelling: false,\n      returnDialogVisible: false,\n      returning: false,\n      modifyDialogVisible: false,\n      modifying: false,\n      timeConflict: false,\n      conflictMessage: '',\n      conflictingReservations: [],\n      timeAvailabilityChecked: false,\n      modifyForm: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      modifyRules: {\n        startDateTime: [{\n          required: true,\n          message: this.$t('reservation.startTimeRequired'),\n          trigger: 'change'\n        }],\n        endDateTime: [{\n          required: true,\n          message: this.$t('reservation.endTimeRequired'),\n          trigger: 'change'\n        }],\n        userEmail: [{\n          required: true,\n          message: this.$t('reservation.emailRequired'),\n          trigger: 'blur'\n        }, {\n          type: 'email',\n          message: this.$t('reservation.emailFormat'),\n          trigger: 'blur'\n        }]\n      },\n      dateTimePickerOptions: {\n        disabledDate: this.disabledDate\n      },\n      historyDialogVisible: false,\n      loadingHistory: false,\n      historyRecords: [],\n      // 添加循环预定相关属性\n      recurringReservationId: null,\n      isRecurringReservation: false\n    };\n  },\n  computed: {\n    // 是否可以取消预定\n    canCancel() {\n      if (!this.reservation) return false;\n\n      // 只有确认状态的预定可以取消\n      return this.reservation.status === 'confirmed';\n    },\n    // 是否可以提前归还\n    canReturn() {\n      if (!this.reservation) return false;\n\n      // 只有使用中状态的预定可以提前归还\n      return this.reservation.status === 'in_use';\n    },\n    // 是否可以修改预定\n    canModify() {\n      if (!this.reservation) return false;\n\n      // 只有确认状态且未开始的预定可以修改\n      if (this.reservation.status !== 'confirmed') return false;\n\n      // 检查是否已开始\n      const now = new Date();\n      const startTime = new Date(this.reservation.start_datetime);\n      return startTime > now;\n    },\n    // 处理历史记录，按时间分组并过滤掉不需要显示的字段\n    processedHistoryRecords() {\n      if (!this.historyRecords || this.historyRecords.length === 0) {\n        return [];\n      }\n\n      // 过滤掉 lang 字段的修改记录\n      const filteredRecords = this.historyRecords.filter(record => record.field_name !== 'lang');\n\n      // 按照修改时间分组\n      const groupedRecords = {};\n      filteredRecords.forEach(record => {\n        const timestamp = record.created_at;\n        if (!groupedRecords[timestamp]) {\n          groupedRecords[timestamp] = {\n            timestamp: timestamp,\n            user_type: record.user_type,\n            user_id: record.user_id,\n            records: []\n          };\n        }\n        groupedRecords[timestamp].records.push(record);\n      });\n\n      // 转换为数组并按时间倒序排序\n      return Object.values(groupedRecords).sort((a, b) => {\n        return new Date(b.timestamp) - new Date(a.timestamp);\n      });\n    },\n    // 时间冲突标题\n    timeConflictTitle() {\n      if (this.conflictingReservations && this.conflictingReservations.length > 0) {\n        return this.$t('reservation.timeConflictWith', {\n          count: this.conflictingReservations.length\n        });\n      } else if (this.conflictMessage) {\n        return this.conflictMessage;\n      } else {\n        return this.$t('reservation.timeSlotOccupied');\n      }\n    }\n  },\n  created() {\n    this.fetchReservation();\n  },\n  methods: {\n    // 获取预定详情\n    async fetchReservation() {\n      this.loading = true;\n      try {\n        // 检查是否是通过预约序号查看\n        const reservationNumber = this.$route.params.number;\n        const code = this.$route.params.code;\n        let response;\n        if (reservationNumber) {\n          console.log('通过预约序号查看预约详情:', reservationNumber);\n\n          // 从URL中获取预约码（如果有）\n          const codeFromQuery = this.$route.query.code;\n\n          // 如果URL中有预约码，使用预约码和预约序号查询\n          if (codeFromQuery) {\n            console.log('使用预约码和预约序号查询:', codeFromQuery, reservationNumber);\n\n            // 直接使用预约序号作为参数，不要包装在对象中\n            console.log('直接使用预约序号作为参数:', reservationNumber);\n\n            // 使用预约码和预约序号查询\n            response = await reservationApi.getReservationByCode(codeFromQuery, reservationNumber);\n          } else {\n            // 如果URL中没有预约码，直接使用预约序号查询\n            console.log('直接使用预约序号查询:', reservationNumber);\n\n            // 对于预约序号查询，直接使用专门的API，不依赖localStorage\n            console.log('使用专门的预约序号API查询:', reservationNumber);\n            response = await reservationApi.getReservationByNumber(reservationNumber);\n          }\n        } else if (this.isAdmin) {\n          // 管理员查询\n          response = await reservationApi.getReservation(code);\n        } else {\n          // 用户查询\n          response = await reservationApi.getReservationByCode(code);\n        }\n        if (response.data.success) {\n          this.reservation = response.data.data;\n          console.log('获取到预约详情:', this.reservation);\n\n          // 检查是否是循环预约的子预约\n          if (this.reservation.recurring_reservation_id) {\n            this.isRecurringReservation = true;\n            this.recurringReservationId = this.reservation.recurring_reservation_id;\n            console.log('这是循环预约的子预约，循环预约ID:', this.recurringReservationId);\n          }\n\n          // 检查是否是从循环预约详情页面跳转过来的\n          const isFromRecurring = this.$route.query.child === 'true' && this.$route.query.recurringId;\n          if (isFromRecurring) {\n            this.recurringReservationId = this.$route.query.recurringId;\n            console.log('从循环预约详情页面跳转过来，循环预约ID:', this.recurringReservationId);\n          }\n\n          // 检查是否需要自动进入编辑模式\n          this.$nextTick(() => {\n            if (this.$route.query.edit === 'true' && this.canModify) {\n              console.log('自动进入编辑模式');\n              this.showModifyDialog();\n            }\n          });\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.notFound'));\n          this.reservation = null;\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservation:', error);\n        this.$message.error(this.$t('common.error'));\n        this.reservation = null;\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 返回上一页\n    goBack() {\n      console.log('ReservationDetail.goBack() - 当前路由参数:', this.$route.query);\n\n      // 检查是否是从循环预约详情页面跳转过来的\n      const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId;\n      if (isFromRecurring) {\n        // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n        console.log('ReservationDetail.goBack() - 返回到循环预约详情页面:', this.recurringReservationId);\n\n        // 构建查询参数，保留用户联系方式等信息\n        const query = {\n          fromChild: 'true',\n          reservation_number: this.reservation.reservation_number\n        };\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          query.userContact = this.$route.query.userContact;\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          query.from = this.$route.query.from;\n        }\n        this.$router.push({\n          path: `/recurring-reservation/${this.recurringReservationId}`,\n          query: query\n        });\n      } else {\n        // 否则返回到个人预约管理页面\n        console.log('ReservationDetail.goBack() - 返回到个人预约管理页面');\n\n        // 检查是否有查询参数，如果有则恢复查询状态\n        if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n          // 构建查询参数来恢复查询状态\n          const queryParams = {};\n          if (this.$route.query.userContact) {\n            queryParams.userContact = this.$route.query.userContact;\n          }\n          if (this.$route.query.reservationCode) {\n            queryParams.reservationCode = this.$route.query.reservationCode;\n          }\n\n          // 添加一个标记表示需要自动执行查询\n          queryParams.autoQuery = 'true';\n          console.log('ReservationDetail.goBack() - 恢复查询状态:', queryParams);\n          this.$router.push({\n            path: '/reservation/query',\n            query: queryParams\n          });\n        } else {\n          // 没有查询参数，直接返回空白查询页面\n          this.$router.push('/reservation/query');\n        }\n      }\n    },\n    // 格式化日期时间\n    formatDateTime(datetime) {\n      return formatDate(datetime, 'YYYY-MM-DD HH:mm:ss', false); // 设置toBeijingTime为false，不进行时区转换\n    },\n    // 获取状态类名\n    getStatusClass(status) {\n      const statusMap = {\n        confirmed: 'status-confirmed',\n        cancelled: 'status-cancelled',\n        completed: 'status-completed',\n        in_use: 'status-in-use',\n        expired: 'status-expired'\n      };\n      return statusMap[status] || 'status-unknown';\n    },\n    // 获取状态图标\n    getStatusIcon(status) {\n      const iconMap = {\n        confirmed: 'el-icon-check',\n        cancelled: 'el-icon-close',\n        completed: 'el-icon-success',\n        in_use: 'el-icon-time',\n        expired: 'el-icon-warning'\n      };\n      return iconMap[status] || 'el-icon-question';\n    },\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        confirmed: this.$t('reservation.statusConfirmed'),\n        cancelled: this.$t('reservation.statusCancelled'),\n        completed: this.$t('reservation.statusCompleted'),\n        in_use: this.$t('reservation.statusInUse'),\n        expired: this.$t('reservation.statusExpired')\n      };\n      return statusMap[status] || this.$t('reservation.statusUnknown');\n    },\n    // 显示取消对话框\n    showCancelDialog() {\n      this.cancelDialogVisible = true;\n    },\n    // 显示提前归还对话框\n    showReturnDialog() {\n      this.returnDialogVisible = true;\n    },\n    // 取消预定\n    async cancelReservation() {\n      this.cancelling = true;\n      try {\n        const response = await reservationApi.cancelReservation(this.reservation.reservation_code, {\n          reservation_number: this.reservation.reservation_number\n        });\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'));\n          this.cancelDialogVisible = false;\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId;\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              };\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact;\n              }\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              });\n            } else {\n              // 否则返回到预约管理页面，恢复查询状态\n              if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n                const queryParams = {};\n                if (this.$route.query.userContact) {\n                  queryParams.userContact = this.$route.query.userContact;\n                }\n                if (this.$route.query.reservationCode) {\n                  queryParams.reservationCode = this.$route.query.reservationCode;\n                }\n                queryParams.autoQuery = 'true';\n                this.$router.push({\n                  path: '/reservation/query',\n                  query: queryParams\n                });\n              } else {\n                this.$router.push('/reservation/query');\n              }\n            }\n          }, 1500); // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error);\n        this.$message.error(this.$t('reservation.cancelFailed'));\n      } finally {\n        this.cancelling = false;\n      }\n    },\n    // 提前归还设备\n    async returnEquipment() {\n      this.returning = true;\n      try {\n        // 这里应该调用提前归还API，但目前后端可能没有实现\n        // 暂时使用取消预约API代替\n        const response = await reservationApi.cancelReservation(this.reservation.reservation_code, {\n          reservation_number: this.reservation.reservation_number,\n          is_early_return: true\n        });\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.returnSuccess'));\n          this.returnDialogVisible = false;\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId;\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              };\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact;\n              }\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              });\n            } else {\n              // 否则返回到预约管理页面，恢复查询状态\n              if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n                const queryParams = {};\n                if (this.$route.query.userContact) {\n                  queryParams.userContact = this.$route.query.userContact;\n                }\n                if (this.$route.query.reservationCode) {\n                  queryParams.reservationCode = this.$route.query.reservationCode;\n                }\n                queryParams.autoQuery = 'true';\n                this.$router.push({\n                  path: '/reservation/query',\n                  query: queryParams\n                });\n              } else {\n                this.$router.push('/reservation/query');\n              }\n            }\n          }, 1500); // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.returnFailed'));\n        }\n      } catch (error) {\n        console.error('Failed to return equipment:', error);\n        this.$message.error(this.$t('reservation.returnFailed'));\n      } finally {\n        this.returning = false;\n      }\n    },\n    // 禁用日期（今天之前的日期不可选）\n    disabledDate(time) {\n      return time.getTime() < Date.now() - 8.64e7; // 8.64e7是一天的毫秒数\n    },\n    // 显示修改对话框\n    showModifyDialog() {\n      // 初始化表单数据\n      this.modifyForm = {\n        startDateTime: this.reservation.start_datetime,\n        endDateTime: this.reservation.end_datetime,\n        purpose: this.reservation.purpose || '',\n        userEmail: this.reservation.user_email || ''\n      };\n\n      // 显示对话框\n      this.modifyDialogVisible = true;\n    },\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyForm.startDateTime);\n      const endTime = new Date(this.modifyForm.endDateTime);\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'));\n        return false;\n      }\n      return true;\n    },\n    // 检查时间可用性\n    async checkTimeAvailability() {\n      if (!this.modifyForm.startDateTime || !this.modifyForm.endDateTime) {\n        this.timeAvailabilityChecked = false;\n        return;\n      }\n\n      // 添加更严格的验证\n      if (this.modifyForm.startDateTime >= this.modifyForm.endDateTime) {\n        this.$message.warning(this.$t('reservation.invalidTime'));\n        this.timeConflict = true;\n        this.timeAvailabilityChecked = false;\n        return;\n      }\n      try {\n        const equipmentId = this.reservation.equipment_id;\n        const startDate = this.modifyForm.startDateTime;\n        const endDate = this.modifyForm.endDateTime;\n\n        // 调用API检查时间可用性，排除当前预定\n        const excludeId = this.reservation.id;\n        const params = {\n          start_date: startDate,\n          end_date: endDate\n        };\n\n        // 只有当excludeId存在且不为null/undefined时才添加参数\n        if (excludeId != null && excludeId !== undefined) {\n          params.exclude_reservation_id = excludeId;\n        }\n        const response = await this.$http.get(`/api/equipment/${equipmentId}/availability`, {\n          params\n        });\n\n        // 检查是否有冲突 - 处理API响应格式\n        if (response.data.specific_time_check) {\n          // 如果是具体时间段检查\n          console.log('具体时间段检查结果:', response.data.available);\n          this.timeConflict = response.data.available.includes(false);\n        } else {\n          // 如果是按日期检查\n          console.log('按日期检查结果:', response.data.available);\n          this.timeConflict = response.data.available.includes(false);\n        }\n\n        // 设置冲突信息\n        if (this.timeConflict) {\n          console.log('检测到时间冲突:', response.data.available);\n\n          // 获取冲突的预定信息\n          this.conflictingReservations = response.data.conflicting_reservations || [];\n\n          // 检查是否是因为达到最大同时预定数量\n          if (response.data.allow_simultaneous && response.data.max_simultaneous > 1) {\n            this.conflictMessage = this.$t('reservation.maxSimultaneousReached', {\n              count: response.data.max_simultaneous\n            });\n          } else {\n            this.conflictMessage = '';\n          }\n        } else {\n          console.log('时间段可用');\n          this.conflictMessage = '';\n          this.conflictingReservations = [];\n        }\n        this.timeAvailabilityChecked = true;\n      } catch (error) {\n        console.error('Failed to check availability:', error);\n        this.timeConflict = true;\n        this.timeAvailabilityChecked = false;\n        this.conflictingReservations = [];\n        this.$message.error(this.$t('common.error'));\n      }\n    },\n    // 查看修改历史\n    async showHistory() {\n      this.historyDialogVisible = true;\n      this.loadingHistory = true;\n      try {\n        // 传递预约码和预约序号\n        const response = await reservationApi.getReservationHistory(this.reservation.reservation_code, this.reservation.reservation_number);\n        if (response.data.success) {\n          this.historyRecords = response.data.data;\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.historyFetchFailed'));\n          this.historyRecords = [];\n        }\n      } catch (error) {\n        console.error('Failed to fetch history:', error);\n        this.$message.error(this.$t('reservation.historyFetchFailed'));\n        this.historyRecords = [];\n      } finally {\n        this.loadingHistory = false;\n      }\n    },\n    // 获取历史记录项类型\n    getHistoryItemType(action) {\n      const typeMap = {\n        'update': 'primary',\n        'status_change': 'success'\n      };\n      return typeMap[action] || 'info';\n    },\n    // 获取历史记录操作文本\n    getHistoryActionText(action) {\n      const actionMap = {\n        'update': this.$t('reservation.modified'),\n        'create': this.$t('reservation.created'),\n        'delete': this.$t('reservation.deleted'),\n        'status_change': this.$t('reservation.statusChanged')\n      };\n      return actionMap[action] || action;\n    },\n    // 获取字段显示名称\n    getFieldDisplayName(fieldName) {\n      const fieldMap = {\n        'start_datetime': this.$t('reservation.startTime'),\n        'end_datetime': this.$t('reservation.endTime'),\n        'purpose': this.$t('reservation.purpose'),\n        'user_email': this.$t('reservation.userEmail'),\n        'status': this.$t('reservation.status')\n      };\n      return fieldMap[fieldName] || fieldName;\n    },\n    // 格式化历史记录值\n    formatHistoryValue(fieldName, value) {\n      if (!value) return '-';\n      if (fieldName === 'start_datetime' || fieldName === 'end_datetime') {\n        return formatDate(value, 'YYYY-MM-DD HH:mm:ss', false); // 设置toBeijingTime为false，不进行时区转换\n      } else if (fieldName === 'status') {\n        return this.getStatusText(value);\n      }\n      return value;\n    },\n    // 查看循环预约详情\n    viewRecurringReservation() {\n      if (!this.reservation || !this.reservation.recurring_reservation_id) return;\n\n      // 跳转到循环预约详情页面，并传递来源信息和预约码\n      this.$router.push({\n        path: `/recurring-reservation/${this.reservation.recurring_reservation_id}`,\n        query: {\n          fromAdmin: this.isAdmin ? 'true' : 'false',\n          reservationCode: this.reservation.reservation_code\n        }\n      });\n    },\n    // 提交修改表单\n    submitModifyForm() {\n      this.$refs.modifyForm.validate(async valid => {\n        if (!valid) return;\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return;\n\n        // 检查时间冲突\n        if (this.timeConflict) {\n          this.$message.error(this.$t('reservation.timeConflict'));\n          return;\n        }\n        this.modifying = true;\n        try {\n          // 构建更新数据\n          const updateData = {\n            start_datetime: this.modifyForm.startDateTime,\n            end_datetime: this.modifyForm.endDateTime,\n            purpose: this.modifyForm.purpose || undefined,\n            user_email: this.modifyForm.userEmail || undefined,\n            lang: this.$i18n.locale\n          };\n\n          // 调用更新API - 传递预约序号以确保修改正确的子预约\n          const response = await reservationApi.updateReservation(this.reservation.reservation_code, updateData, this.reservation.reservation_number // 传递预约序号\n          );\n          if (response.data.success) {\n            this.$message.success(this.$t('reservation.updateSuccess'));\n            this.modifyDialogVisible = false;\n            // 重新获取预定信息\n            await this.fetchReservation();\n          } else {\n            this.$message.error(response.data.message || this.$t('reservation.updateFailed'));\n          }\n        } catch (error) {\n          console.error('Failed to update reservation:', error);\n          this.$message.error(this.$t('reservation.updateFailed'));\n        } finally {\n          this.modifying = false;\n        }\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["reservationApi", "equipmentApi", "formatDate", "name", "props", "isAdmin", "type", "Boolean", "default", "data", "loading", "reservation", "cancelDialogVisible", "cancelling", "returnDialogVisible", "returning", "modifyDialogVisible", "modifying", "timeConflict", "conflictMessage", "conflictingReservations", "timeAvailabilityChecked", "modifyForm", "startDateTime", "endDateTime", "purpose", "userEmail", "modifyRules", "required", "message", "$t", "trigger", "dateTimePickerOptions", "disabledDate", "historyDialogVisible", "loadingHistory", "historyRecords", "recurringReservationId", "isRecurringReservation", "computed", "canCancel", "status", "canReturn", "canModify", "now", "Date", "startTime", "start_datetime", "processedHistoryRecords", "length", "filteredRecords", "filter", "record", "field_name", "groupedRecords", "for<PERSON>ach", "timestamp", "created_at", "user_type", "user_id", "records", "push", "Object", "values", "sort", "a", "b", "timeConflictTitle", "count", "created", "fetchReservation", "methods", "reservationNumber", "$route", "params", "number", "code", "response", "console", "log", "codeFromQuery", "query", "getReservationByCode", "getReservationByNumber", "getReservation", "success", "recurring_reservation_id", "isFromRecurring", "child", "recurringId", "$nextTick", "edit", "showModifyDialog", "$message", "error", "goBack", "fromChild", "reservation_number", "userContact", "from", "$router", "path", "reservationCode", "queryParams", "autoQuery", "formatDateTime", "datetime", "getStatusClass", "statusMap", "confirmed", "cancelled", "completed", "in_use", "expired", "getStatusIcon", "iconMap", "getStatusText", "showCancelDialog", "showReturnDialog", "cancelReservation", "reservation_code", "setTimeout", "returnEquipment", "is_early_return", "time", "getTime", "end_datetime", "user_email", "validateTimeRange", "endTime", "checkTimeAvailability", "warning", "equipmentId", "equipment_id", "startDate", "endDate", "excludeId", "id", "start_date", "end_date", "undefined", "exclude_reservation_id", "$http", "get", "specific_time_check", "available", "includes", "conflicting_reservations", "allow_simultaneous", "max_simultaneous", "showHistory", "getReservationHistory", "getHistoryItemType", "action", "typeMap", "getHistoryActionText", "actionMap", "getFieldDisplayName", "fieldName", "fieldMap", "formatHistoryValue", "value", "viewRecurringReservation", "fromAdmin", "submitModifyForm", "$refs", "validate", "valid", "updateData", "lang", "$i18n", "locale", "updateReservation"], "sources": ["src/views/reservation/ReservationDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"reservation-detail\">\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"!reservation\" class=\"error-container\">\n      <el-result\n        icon=\"error\"\n        :title=\"$t('error.errorMessage')\"\n        :sub-title=\"$t('reservation.reservationNotFound')\"\n      >\n        <template #extra>\n          <el-button type=\"primary\" @click=\"$router.push('/reservation/query')\">\n            {{ $t('reservation.query') }}\n          </el-button>\n        </template>\n      </el-result>\n    </div>\n\n    <div v-else>\n      <!-- 返回按钮 -->\n      <div class=\"back-link\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"goBack\">\n          {{ $t('common.back') }}\n        </el-button>\n      </div>\n\n      <h1 class=\"page-title\">{{ $t('reservation.detail') }}</h1>\n\n      <!-- 预定状态 -->\n      <div class=\"reservation-status-card\" :class=\"getStatusClass(reservation.status)\">\n        <div class=\"status-icon\">\n          <i :class=\"getStatusIcon(reservation.status)\"></i>\n        </div>\n        <div class=\"status-text\">\n          <h2>{{ getStatusText(reservation.status) }}</h2>\n          <p>{{ $t('reservation.code') }}: {{ reservation.reservation_code }}</p>\n        </div>\n      </div>\n\n      <!-- 预定详情 -->\n      <el-card shadow=\"never\" class=\"detail-card\">\n        <div slot=\"header\">\n          <span>{{ $t('reservation.detail') }}</span>\n        </div>\n\n        <el-descriptions :column=\"1\" border>\n          <el-descriptions-item :label=\"$t('reservation.number')\">\n            {{ reservation.reservation_number || '-' }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.reservationType')\">\n            <el-tag\n              size=\"medium\"\n              :type=\"reservation.recurring_reservation_id ? 'primary' : 'success'\"\n              effect=\"plain\"\n            >\n              {{ reservation.recurring_reservation_id ? $t('reservation.recurringReservation') : $t('reservation.singleReservation') }}\n            </el-tag>\n            <el-button\n              v-if=\"reservation.recurring_reservation_id\"\n              type=\"primary\"\n              size=\"mini\"\n              style=\"margin-left: 10px;\"\n              @click=\"viewRecurringReservation\"\n            >\n              {{ $t('reservation.viewRecurringReservation') }}\n            </el-button>\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.equipmentName')\">\n            {{ reservation.equipment_name }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('equipment.category')\" v-if=\"reservation.equipment_category\">\n            {{ reservation.equipment_category }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('equipment.location')\" v-if=\"reservation.equipment_location\">\n            {{ reservation.equipment_location }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.startTime')\">\n            {{ formatDateTime(reservation.start_datetime) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.endTime')\">\n            {{ formatDateTime(reservation.end_datetime) }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.purpose')\" v-if=\"reservation.purpose\">\n            {{ reservation.purpose }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <!-- 用户信息 -->\n      <el-card shadow=\"never\" class=\"user-card\">\n        <div slot=\"header\">\n          <span>{{ $t('common.userInfo') }}</span>\n        </div>\n\n        <el-descriptions :column=\"1\" border>\n          <el-descriptions-item :label=\"$t('reservation.userName')\">\n            {{ reservation.user_name }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userDepartment')\">\n            {{ reservation.user_department }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userContact')\">\n            {{ reservation.user_contact }}\n          </el-descriptions-item>\n\n          <el-descriptions-item :label=\"$t('reservation.userEmail')\" v-if=\"reservation.user_email\">\n            {{ reservation.user_email }}\n          </el-descriptions-item>\n        </el-descriptions>\n      </el-card>\n\n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <!-- 已确认状态的预约显示修改按钮 -->\n        <el-button v-if=\"canModify\" type=\"primary\" @click=\"showModifyDialog\" icon=\"el-icon-edit\">\n          {{ $t('reservation.modifyReservation') }}\n        </el-button>\n\n        <!-- 已确认状态的预约显示取消按钮 -->\n        <el-button v-if=\"canCancel\" type=\"danger\" @click=\"showCancelDialog\" icon=\"el-icon-close\">\n          {{ $t('reservation.cancelReservation') }}\n        </el-button>\n\n        <!-- 使用中状态的预约显示提前归还按钮 -->\n        <el-button v-if=\"canReturn\" type=\"primary\" @click=\"showReturnDialog\" icon=\"el-icon-time\">\n          {{ $t('reservation.earlyReturn') }}\n        </el-button>\n\n        <!-- 查看历史记录按钮 -->\n        <el-button type=\"info\" @click=\"showHistory\" icon=\"el-icon-document\">\n          {{ $t('reservation.viewHistory') }}\n        </el-button>\n      </div>\n\n      <!-- 取消预定对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.cancelConfirmation')\"\n        :visible.sync=\"cancelDialogVisible\"\n        :modal=\"window.innerWidth > 600\"\n        :width=\"window.innerWidth <= 600 ? '90%' : '400px'\"\n        :class=\"{ 'mobile-dialog': window.innerWidth <= 600 }\"\n>\n  <p>{{ $t('reservation.cancelConfirmationMessage') }}</p>\n  <div slot=\"footer\" class=\"dialog-footer\">\n    <el-button @click=\"cancelDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n    <el-button type=\"danger\" @click=\"cancelReservation\" :loading=\"cancelling\">\n      {{ $t('common.confirm') }}\n    </el-button>\n  </div>\n</el-dialog>\n\n      <!-- 提前归还对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.earlyReturn')\"\n        :visible.sync=\"returnDialogVisible\"\n        width=\"400px\"\n      >\n        <p>{{ $t('reservation.confirmEarlyReturn') }}</p>\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"returnDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n          <el-button type=\"primary\" @click=\"returnEquipment\" :loading=\"returning\">\n            {{ $t('common.confirm') }}\n          </el-button>\n        </div>\n      </el-dialog>\n\n      <!-- 修改预定对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.modifyReservation')\"\n        :visible.sync=\"modifyDialogVisible\"\n        width=\"600px\"\n      >\n        <el-form\n          ref=\"modifyForm\"\n          :model=\"modifyForm\"\n          :rules=\"modifyRules\"\n          label-width=\"120px\"\n          v-loading=\"modifying\"\n        >\n          <!-- 开始时间 -->\n          <el-form-item :label=\"$t('reservation.startTime')\" prop=\"startDateTime\">\n            <el-date-picker\n              v-model=\"modifyForm.startDateTime\"\n              type=\"datetime\"\n              :placeholder=\"$t('reservation.selectStartTime')\"\n              style=\"width: 100%\"\n              :picker-options=\"dateTimePickerOptions\"\n              value-format=\"yyyy-MM-ddTHH:mm:ss\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              @change=\"checkTimeAvailability\"\n            ></el-date-picker>\n          </el-form-item>\n\n          <!-- 结束时间 -->\n          <el-form-item :label=\"$t('reservation.endTime')\" prop=\"endDateTime\">\n            <el-date-picker\n              v-model=\"modifyForm.endDateTime\"\n              type=\"datetime\"\n              :placeholder=\"$t('reservation.selectEndTime')\"\n              style=\"width: 100%\"\n              :picker-options=\"dateTimePickerOptions\"\n              value-format=\"yyyy-MM-ddTHH:mm:ss\"\n              format=\"yyyy-MM-dd HH:mm:ss\"\n              @change=\"checkTimeAvailability\"\n            ></el-date-picker>\n          </el-form-item>\n\n          <!-- 时间冲突提示 -->\n          <el-alert\n            v-if=\"timeConflict\"\n            :title=\"timeConflictTitle\"\n            type=\"error\"\n            :closable=\"false\"\n            show-icon\n            style=\"margin-bottom: 15px;\"\n          >\n            <div v-if=\"conflictingReservations && conflictingReservations.length > 0\">\n              <p style=\"margin-bottom: 10px;\">{{ $t('reservation.conflictWithFollowing') }}</p>\n              <div v-for=\"conflict in conflictingReservations\" :key=\"conflict.id\" style=\"margin-bottom: 8px; padding: 8px; background-color: #fef0f0; border-radius: 4px;\">\n                <div><strong>{{ $t('reservation.conflictTime') }}</strong>{{ conflict.start_datetime }} {{ $t('reservation.conflictTo') }} {{ conflict.end_datetime }}</div>\n                <div><strong>{{ $t('reservation.conflictUser') }}</strong>{{ conflict.user_name }} ({{ conflict.user_department }})</div>\n                <div v-if=\"conflict.user_email\"><strong>{{ $t('reservation.conflictEmail') }}</strong>{{ conflict.user_email }}</div>\n                <div v-if=\"conflict.user_phone\"><strong>{{ $t('reservation.conflictPhone') }}</strong>{{ conflict.user_phone }}</div>\n                <div v-if=\"conflict.purpose\"><strong>{{ $t('reservation.conflictPurpose') }}</strong>{{ conflict.purpose }}</div>\n              </div>\n            </div>\n            <template v-else-if=\"conflictMessage\">\n              {{ conflictMessage }}\n            </template>\n          </el-alert>\n\n          <!-- 时间可用提示 -->\n          <el-alert\n            v-if=\"!timeConflict && timeAvailabilityChecked\"\n            :title=\"$t('reservation.timeSlotAvailable')\"\n            type=\"success\"\n            :closable=\"false\"\n            show-icon\n            style=\"margin-bottom: 15px;\"\n          ></el-alert>\n\n          <!-- 使用目的 -->\n          <el-form-item :label=\"$t('reservation.purpose')\" prop=\"purpose\">\n            <el-input\n              v-model=\"modifyForm.purpose\"\n              type=\"textarea\"\n              :rows=\"3\"\n              :placeholder=\"$t('reservation.purposePlaceholder')\"\n            ></el-input>\n          </el-form-item>\n\n          <!-- 用户邮箱 -->\n          <el-form-item :label=\"$t('reservation.userEmail')\" prop=\"userEmail\" required>\n            <el-input\n              v-model=\"modifyForm.userEmail\"\n              :placeholder=\"$t('reservation.emailPlaceholder')\"\n            ></el-input>\n          </el-form-item>\n        </el-form>\n\n        <div slot=\"footer\" class=\"dialog-footer\">\n          <el-button @click=\"modifyDialogVisible = false\">{{ $t('common.cancel') }}</el-button>\n          <el-button type=\"primary\" @click=\"submitModifyForm\" :loading=\"modifying\" :disabled=\"timeConflict\">\n            {{ $t('common.confirm') }}\n          </el-button>\n        </div>\n      </el-dialog>\n\n      <!-- 修改历史记录对话框 -->\n      <el-dialog\n        :title=\"$t('reservation.modificationHistory')\"\n        :visible.sync=\"historyDialogVisible\"\n        width=\"700px\"\n      >\n        <div v-loading=\"loadingHistory\">\n          <el-empty v-if=\"processedHistoryRecords.length === 0\" :description=\"$t('reservation.noHistory')\"></el-empty>\n          <el-timeline v-else>\n            <el-timeline-item\n              v-for=\"(group, index) in processedHistoryRecords\"\n              :key=\"index\"\n              type=\"primary\"\n            >\n              <el-card class=\"history-card\">\n                <!-- 修改时间显示在最上面 -->\n                <div class=\"history-time\">\n                  <i class=\"el-icon-time\"></i> {{ formatDateTime(group.timestamp) }}\n                </div>\n\n                <div class=\"history-user\">\n                  {{ group.user_type === 'admin' ? $t('reservation.admin') : $t('reservation.user') }}\n                  {{ group.user_id ? ': ' + group.user_id : '' }}\n                </div>\n\n                <div v-for=\"(record, recordIndex) in group.records\" :key=\"recordIndex\" class=\"history-item\">\n                  <div class=\"history-action\">\n                    {{ getHistoryActionText(record.action) }}\n                    <span class=\"history-field\">{{ getFieldDisplayName(record.field_name) }}</span>\n                  </div>\n                  <div class=\"history-values\">\n                    <div class=\"history-old-value\">\n                      <span class=\"history-label\">{{ $t('reservation.oldValue') }}:</span>\n                      <span>{{ formatHistoryValue(record.field_name, record.old_value) }}</span>\n                    </div>\n                    <div class=\"history-new-value\">\n                      <span class=\"history-label\">{{ $t('reservation.newValue') }}:</span>\n                      <span>{{ formatHistoryValue(record.field_name, record.new_value) }}</span>\n                    </div>\n                  </div>\n                </div>\n              </el-card>\n            </el-timeline-item>\n          </el-timeline>\n        </div>\n      </el-dialog>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { reservationApi, equipmentApi } from '@/api'\nimport { formatDate } from '@/utils/date'\n\nexport default {\n  name: 'ReservationDetail',\n\n  props: {\n    isAdmin: {\n      type: Boolean,\n      default: false\n    }\n  },\n\n  data() {\n    return {\n      loading: true,\n      reservation: null,\n      cancelDialogVisible: false,\n      cancelling: false,\n      returnDialogVisible: false,\n      returning: false,\n      modifyDialogVisible: false,\n      modifying: false,\n      timeConflict: false,\n      conflictMessage: '',\n      conflictingReservations: [],\n      timeAvailabilityChecked: false,\n      modifyForm: {\n        startDateTime: '',\n        endDateTime: '',\n        purpose: '',\n        userEmail: ''\n      },\n      modifyRules: {\n        startDateTime: [\n          { required: true, message: this.$t('reservation.startTimeRequired'), trigger: 'change' }\n        ],\n        endDateTime: [\n          { required: true, message: this.$t('reservation.endTimeRequired'), trigger: 'change' }\n        ],\n        userEmail: [\n          { required: true, message: this.$t('reservation.emailRequired'), trigger: 'blur' },\n          { type: 'email', message: this.$t('reservation.emailFormat'), trigger: 'blur' }\n        ]\n      },\n      dateTimePickerOptions: {\n        disabledDate: this.disabledDate\n      },\n      historyDialogVisible: false,\n      loadingHistory: false,\n      historyRecords: [],\n      // 添加循环预定相关属性\n      recurringReservationId: null,\n      isRecurringReservation: false\n    }\n  },\n\n  computed: {\n    // 是否可以取消预定\n    canCancel() {\n      if (!this.reservation) return false\n\n      // 只有确认状态的预定可以取消\n      return this.reservation.status === 'confirmed'\n    },\n\n    // 是否可以提前归还\n    canReturn() {\n      if (!this.reservation) return false\n\n      // 只有使用中状态的预定可以提前归还\n      return this.reservation.status === 'in_use'\n    },\n\n    // 是否可以修改预定\n    canModify() {\n      if (!this.reservation) return false\n\n      // 只有确认状态且未开始的预定可以修改\n      if (this.reservation.status !== 'confirmed') return false\n\n      // 检查是否已开始\n      const now = new Date()\n      const startTime = new Date(this.reservation.start_datetime)\n\n      return startTime > now\n    },\n\n    // 处理历史记录，按时间分组并过滤掉不需要显示的字段\n    processedHistoryRecords() {\n      if (!this.historyRecords || this.historyRecords.length === 0) {\n        return []\n      }\n\n      // 过滤掉 lang 字段的修改记录\n      const filteredRecords = this.historyRecords.filter(record =>\n        record.field_name !== 'lang'\n      )\n\n      // 按照修改时间分组\n      const groupedRecords = {}\n      filteredRecords.forEach(record => {\n        const timestamp = record.created_at\n        if (!groupedRecords[timestamp]) {\n          groupedRecords[timestamp] = {\n            timestamp: timestamp,\n            user_type: record.user_type,\n            user_id: record.user_id,\n            records: []\n          }\n        }\n        groupedRecords[timestamp].records.push(record)\n      })\n\n      // 转换为数组并按时间倒序排序\n      return Object.values(groupedRecords).sort((a, b) => {\n        return new Date(b.timestamp) - new Date(a.timestamp)\n      })\n    },\n\n    // 时间冲突标题\n    timeConflictTitle() {\n      if (this.conflictingReservations && this.conflictingReservations.length > 0) {\n        return this.$t('reservation.timeConflictWith', { count: this.conflictingReservations.length })\n      } else if (this.conflictMessage) {\n        return this.conflictMessage\n      } else {\n        return this.$t('reservation.timeSlotOccupied')\n      }\n    }\n  },\n\n  created() {\n    this.fetchReservation()\n  },\n\n  methods: {\n    // 获取预定详情\n    async fetchReservation() {\n      this.loading = true\n      try {\n        // 检查是否是通过预约序号查看\n        const reservationNumber = this.$route.params.number\n        const code = this.$route.params.code\n        let response\n\n        if (reservationNumber) {\n          console.log('通过预约序号查看预约详情:', reservationNumber)\n\n          // 从URL中获取预约码（如果有）\n          const codeFromQuery = this.$route.query.code\n\n          // 如果URL中有预约码，使用预约码和预约序号查询\n          if (codeFromQuery) {\n            console.log('使用预约码和预约序号查询:', codeFromQuery, reservationNumber)\n\n            // 直接使用预约序号作为参数，不要包装在对象中\n            console.log('直接使用预约序号作为参数:', reservationNumber);\n\n            // 使用预约码和预约序号查询\n            response = await reservationApi.getReservationByCode(codeFromQuery, reservationNumber)\n          } else {\n            // 如果URL中没有预约码，直接使用预约序号查询\n            console.log('直接使用预约序号查询:', reservationNumber)\n\n            // 对于预约序号查询，直接使用专门的API，不依赖localStorage\n            console.log('使用专门的预约序号API查询:', reservationNumber);\n            response = await reservationApi.getReservationByNumber(reservationNumber)\n          }\n        } else if (this.isAdmin) {\n          // 管理员查询\n          response = await reservationApi.getReservation(code)\n        } else {\n          // 用户查询\n          response = await reservationApi.getReservationByCode(code)\n        }\n\n        if (response.data.success) {\n          this.reservation = response.data.data\n          console.log('获取到预约详情:', this.reservation)\n\n          // 检查是否是循环预约的子预约\n          if (this.reservation.recurring_reservation_id) {\n            this.isRecurringReservation = true\n            this.recurringReservationId = this.reservation.recurring_reservation_id\n            console.log('这是循环预约的子预约，循环预约ID:', this.recurringReservationId)\n          }\n\n          // 检查是否是从循环预约详情页面跳转过来的\n          const isFromRecurring = this.$route.query.child === 'true' && this.$route.query.recurringId\n          if (isFromRecurring) {\n            this.recurringReservationId = this.$route.query.recurringId\n            console.log('从循环预约详情页面跳转过来，循环预约ID:', this.recurringReservationId)\n          }\n\n          // 检查是否需要自动进入编辑模式\n          this.$nextTick(() => {\n            if (this.$route.query.edit === 'true' && this.canModify) {\n              console.log('自动进入编辑模式')\n              this.showModifyDialog()\n            }\n          })\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.notFound'))\n          this.reservation = null\n        }\n      } catch (error) {\n        console.error('Failed to fetch reservation:', error)\n        this.$message.error(this.$t('common.error'))\n        this.reservation = null\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 返回上一页\n    goBack() {\n      console.log('ReservationDetail.goBack() - 当前路由参数:', this.$route.query)\n\n      // 检查是否是从循环预约详情页面跳转过来的\n      const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId\n\n      if (isFromRecurring) {\n        // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n        console.log('ReservationDetail.goBack() - 返回到循环预约详情页面:', this.recurringReservationId)\n\n        // 构建查询参数，保留用户联系方式等信息\n        const query = {\n          fromChild: 'true',\n          reservation_number: this.reservation.reservation_number\n        }\n\n        // 保留用户联系方式参数（如果有）\n        if (this.$route.query.userContact) {\n          query.userContact = this.$route.query.userContact\n        }\n\n        // 保留来源参数（如果有）\n        if (this.$route.query.from) {\n          query.from = this.$route.query.from\n        }\n\n        this.$router.push({\n          path: `/recurring-reservation/${this.recurringReservationId}`,\n          query: query\n        })\n      } else {\n        // 否则返回到个人预约管理页面\n        console.log('ReservationDetail.goBack() - 返回到个人预约管理页面')\n\n        // 检查是否有查询参数，如果有则恢复查询状态\n        if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n          // 构建查询参数来恢复查询状态\n          const queryParams = {}\n\n          if (this.$route.query.userContact) {\n            queryParams.userContact = this.$route.query.userContact\n          }\n\n          if (this.$route.query.reservationCode) {\n            queryParams.reservationCode = this.$route.query.reservationCode\n          }\n\n          // 添加一个标记表示需要自动执行查询\n          queryParams.autoQuery = 'true'\n\n          console.log('ReservationDetail.goBack() - 恢复查询状态:', queryParams)\n\n          this.$router.push({\n            path: '/reservation/query',\n            query: queryParams\n          })\n        } else {\n          // 没有查询参数，直接返回空白查询页面\n          this.$router.push('/reservation/query')\n        }\n      }\n    },\n\n    // 格式化日期时间\n    formatDateTime(datetime) {\n      return formatDate(datetime, 'YYYY-MM-DD HH:mm:ss', false) // 设置toBeijingTime为false，不进行时区转换\n    },\n\n    // 获取状态类名\n    getStatusClass(status) {\n      const statusMap = {\n        confirmed: 'status-confirmed',\n        cancelled: 'status-cancelled',\n        completed: 'status-completed',\n        in_use: 'status-in-use',\n        expired: 'status-expired'\n      }\n      return statusMap[status] || 'status-unknown'\n    },\n\n    // 获取状态图标\n    getStatusIcon(status) {\n      const iconMap = {\n        confirmed: 'el-icon-check',\n        cancelled: 'el-icon-close',\n        completed: 'el-icon-success',\n        in_use: 'el-icon-time',\n        expired: 'el-icon-warning'\n      }\n      return iconMap[status] || 'el-icon-question'\n    },\n\n    // 获取状态文本\n    getStatusText(status) {\n      const statusMap = {\n        confirmed: this.$t('reservation.statusConfirmed'),\n        cancelled: this.$t('reservation.statusCancelled'),\n        completed: this.$t('reservation.statusCompleted'),\n        in_use: this.$t('reservation.statusInUse'),\n        expired: this.$t('reservation.statusExpired')\n      }\n      return statusMap[status] || this.$t('reservation.statusUnknown')\n    },\n\n\n\n    // 显示取消对话框\n    showCancelDialog() {\n      this.cancelDialogVisible = true\n    },\n\n    // 显示提前归还对话框\n    showReturnDialog() {\n      this.returnDialogVisible = true\n    },\n\n    // 取消预定\n    async cancelReservation() {\n      this.cancelling = true\n      try {\n        const response = await reservationApi.cancelReservation(\n          this.reservation.reservation_code,\n          { reservation_number: this.reservation.reservation_number }\n        )\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.cancelSuccess'))\n          this.cancelDialogVisible = false\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId\n\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              }\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact\n              }\n\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              })\n            } else {\n              // 否则返回到预约管理页面，恢复查询状态\n              if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n                const queryParams = {}\n\n                if (this.$route.query.userContact) {\n                  queryParams.userContact = this.$route.query.userContact\n                }\n\n                if (this.$route.query.reservationCode) {\n                  queryParams.reservationCode = this.$route.query.reservationCode\n                }\n\n                queryParams.autoQuery = 'true'\n\n                this.$router.push({\n                  path: '/reservation/query',\n                  query: queryParams\n                })\n              } else {\n                this.$router.push('/reservation/query')\n              }\n            }\n          }, 1500) // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.cancelFailed'))\n        }\n      } catch (error) {\n        console.error('Failed to cancel reservation:', error)\n        this.$message.error(this.$t('reservation.cancelFailed'))\n      } finally {\n        this.cancelling = false\n      }\n    },\n\n    // 提前归还设备\n    async returnEquipment() {\n      this.returning = true\n      try {\n        // 这里应该调用提前归还API，但目前后端可能没有实现\n        // 暂时使用取消预约API代替\n        const response = await reservationApi.cancelReservation(\n          this.reservation.reservation_code,\n          {\n            reservation_number: this.reservation.reservation_number,\n            is_early_return: true\n          }\n        )\n\n        if (response.data.success) {\n          this.$message.success(this.$t('reservation.returnSuccess'))\n          this.returnDialogVisible = false\n\n          // 设置一个延迟，让用户看到状态变化后再跳转\n          setTimeout(() => {\n            // 检查是否是从循环预约详情页面跳转过来的\n            const isFromRecurring = this.$route.query.child === 'true' && this.recurringReservationId\n\n            if (isFromRecurring) {\n              // 如果是从循环预约详情页面跳转过来的，返回到循环预约详情页面\n              const query = {\n                fromChild: 'true',\n                reservation_number: this.reservation.reservation_number\n              }\n\n              // 保留用户联系方式参数（如果有）\n              if (this.$route.query.userContact) {\n                query.userContact = this.$route.query.userContact\n              }\n\n              this.$router.push({\n                path: `/recurring-reservation/${this.recurringReservationId}`,\n                query: query\n              })\n            } else {\n              // 否则返回到预约管理页面，恢复查询状态\n              if (this.$route.query.from === 'query' && (this.$route.query.userContact || this.$route.query.reservationCode)) {\n                const queryParams = {}\n\n                if (this.$route.query.userContact) {\n                  queryParams.userContact = this.$route.query.userContact\n                }\n\n                if (this.$route.query.reservationCode) {\n                  queryParams.reservationCode = this.$route.query.reservationCode\n                }\n\n                queryParams.autoQuery = 'true'\n\n                this.$router.push({\n                  path: '/reservation/query',\n                  query: queryParams\n                })\n              } else {\n                this.$router.push('/reservation/query')\n              }\n            }\n          }, 1500) // 增加延迟时间，让用户有更多时间看到状态变化\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.returnFailed'))\n        }\n      } catch (error) {\n        console.error('Failed to return equipment:', error)\n        this.$message.error(this.$t('reservation.returnFailed'))\n      } finally {\n        this.returning = false\n      }\n    },\n\n    // 禁用日期（今天之前的日期不可选）\n    disabledDate(time) {\n      return time.getTime() < Date.now() - 8.64e7 // 8.64e7是一天的毫秒数\n    },\n\n    // 显示修改对话框\n    showModifyDialog() {\n      // 初始化表单数据\n      this.modifyForm = {\n        startDateTime: this.reservation.start_datetime,\n        endDateTime: this.reservation.end_datetime,\n        purpose: this.reservation.purpose || '',\n        userEmail: this.reservation.user_email || ''\n      }\n\n      // 显示对话框\n      this.modifyDialogVisible = true\n    },\n\n    // 验证时间范围\n    validateTimeRange() {\n      const startTime = new Date(this.modifyForm.startDateTime)\n      const endTime = new Date(this.modifyForm.endDateTime)\n\n      if (startTime >= endTime) {\n        this.$message.error(this.$t('reservation.invalidTime'))\n        return false\n      }\n\n      return true\n    },\n\n    // 检查时间可用性\n    async checkTimeAvailability() {\n      if (!this.modifyForm.startDateTime || !this.modifyForm.endDateTime) {\n        this.timeAvailabilityChecked = false\n        return\n      }\n\n      // 添加更严格的验证\n      if (this.modifyForm.startDateTime >= this.modifyForm.endDateTime) {\n        this.$message.warning(this.$t('reservation.invalidTime'))\n        this.timeConflict = true\n        this.timeAvailabilityChecked = false\n        return\n      }\n\n      try {\n        const equipmentId = this.reservation.equipment_id\n        const startDate = this.modifyForm.startDateTime\n        const endDate = this.modifyForm.endDateTime\n\n        // 调用API检查时间可用性，排除当前预定\n        const excludeId = this.reservation.id\n        const params = {\n          start_date: startDate,\n          end_date: endDate\n        }\n\n        // 只有当excludeId存在且不为null/undefined时才添加参数\n        if (excludeId != null && excludeId !== undefined) {\n          params.exclude_reservation_id = excludeId\n        }\n\n\n\n        const response = await this.$http.get(`/api/equipment/${equipmentId}/availability`, { params })\n\n        // 检查是否有冲突 - 处理API响应格式\n        if (response.data.specific_time_check) {\n          // 如果是具体时间段检查\n          console.log('具体时间段检查结果:', response.data.available)\n          this.timeConflict = response.data.available.includes(false)\n        } else {\n          // 如果是按日期检查\n          console.log('按日期检查结果:', response.data.available)\n          this.timeConflict = response.data.available.includes(false)\n        }\n\n        // 设置冲突信息\n        if (this.timeConflict) {\n          console.log('检测到时间冲突:', response.data.available)\n\n          // 获取冲突的预定信息\n          this.conflictingReservations = response.data.conflicting_reservations || []\n\n          // 检查是否是因为达到最大同时预定数量\n          if (response.data.allow_simultaneous && response.data.max_simultaneous > 1) {\n            this.conflictMessage = this.$t('reservation.maxSimultaneousReached', { count: response.data.max_simultaneous });\n          } else {\n            this.conflictMessage = '';\n          }\n        } else {\n          console.log('时间段可用')\n          this.conflictMessage = '';\n          this.conflictingReservations = [];\n        }\n\n        this.timeAvailabilityChecked = true\n      } catch (error) {\n        console.error('Failed to check availability:', error)\n        this.timeConflict = true\n        this.timeAvailabilityChecked = false\n        this.conflictingReservations = []\n        this.$message.error(this.$t('common.error'))\n      }\n    },\n\n    // 查看修改历史\n    async showHistory() {\n      this.historyDialogVisible = true\n      this.loadingHistory = true\n\n      try {\n        // 传递预约码和预约序号\n        const response = await reservationApi.getReservationHistory(\n          this.reservation.reservation_code,\n          this.reservation.reservation_number\n        )\n\n        if (response.data.success) {\n          this.historyRecords = response.data.data\n        } else {\n          this.$message.error(response.data.message || this.$t('reservation.historyFetchFailed'))\n          this.historyRecords = []\n        }\n      } catch (error) {\n        console.error('Failed to fetch history:', error)\n        this.$message.error(this.$t('reservation.historyFetchFailed'))\n        this.historyRecords = []\n      } finally {\n        this.loadingHistory = false\n      }\n    },\n\n    // 获取历史记录项类型\n    getHistoryItemType(action) {\n      const typeMap = {\n        'update': 'primary',\n        'status_change': 'success'\n      }\n      return typeMap[action] || 'info'\n    },\n\n    // 获取历史记录操作文本\n    getHistoryActionText(action) {\n      const actionMap = {\n        'update': this.$t('reservation.modified'),\n        'create': this.$t('reservation.created'),\n        'delete': this.$t('reservation.deleted'),\n        'status_change': this.$t('reservation.statusChanged')\n      }\n      return actionMap[action] || action\n    },\n\n    // 获取字段显示名称\n    getFieldDisplayName(fieldName) {\n      const fieldMap = {\n        'start_datetime': this.$t('reservation.startTime'),\n        'end_datetime': this.$t('reservation.endTime'),\n        'purpose': this.$t('reservation.purpose'),\n        'user_email': this.$t('reservation.userEmail'),\n        'status': this.$t('reservation.status')\n      }\n      return fieldMap[fieldName] || fieldName\n    },\n\n    // 格式化历史记录值\n    formatHistoryValue(fieldName, value) {\n      if (!value) return '-'\n\n      if (fieldName === 'start_datetime' || fieldName === 'end_datetime') {\n        return formatDate(value, 'YYYY-MM-DD HH:mm:ss', false) // 设置toBeijingTime为false，不进行时区转换\n      } else if (fieldName === 'status') {\n        return this.getStatusText(value)\n      }\n\n      return value\n    },\n\n    // 查看循环预约详情\n    viewRecurringReservation() {\n      if (!this.reservation || !this.reservation.recurring_reservation_id) return;\n\n      // 跳转到循环预约详情页面，并传递来源信息和预约码\n      this.$router.push({\n        path: `/recurring-reservation/${this.reservation.recurring_reservation_id}`,\n        query: {\n          fromAdmin: this.isAdmin ? 'true' : 'false',\n          reservationCode: this.reservation.reservation_code\n        }\n      });\n    },\n\n    // 提交修改表单\n    submitModifyForm() {\n      this.$refs.modifyForm.validate(async valid => {\n        if (!valid) return\n\n        // 验证时间范围\n        if (!this.validateTimeRange()) return\n\n        // 检查时间冲突\n        if (this.timeConflict) {\n          this.$message.error(this.$t('reservation.timeConflict'))\n          return\n        }\n\n        this.modifying = true\n        try {\n          // 构建更新数据\n          const updateData = {\n            start_datetime: this.modifyForm.startDateTime,\n            end_datetime: this.modifyForm.endDateTime,\n            purpose: this.modifyForm.purpose || undefined,\n            user_email: this.modifyForm.userEmail || undefined,\n            lang: this.$i18n.locale\n          }\n\n          // 调用更新API - 传递预约序号以确保修改正确的子预约\n          const response = await reservationApi.updateReservation(\n            this.reservation.reservation_code,\n            updateData,\n            this.reservation.reservation_number  // 传递预约序号\n          )\n\n          if (response.data.success) {\n            this.$message.success(this.$t('reservation.updateSuccess'))\n            this.modifyDialogVisible = false\n            // 重新获取预定信息\n            await this.fetchReservation()\n          } else {\n            this.$message.error(response.data.message || this.$t('reservation.updateFailed'))\n          }\n        } catch (error) {\n          console.error('Failed to update reservation:', error)\n          this.$message.error(this.$t('reservation.updateFailed'))\n        } finally {\n          this.modifying = false\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.reservation-detail {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.page-title {\n  font-size: 24px;\n  margin-bottom: 20px;\n  color: #303133;\n}\n\n.back-link {\n  margin-bottom: 20px;\n}\n\n.loading-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.error-container {\n  padding: 40px 0;\n}\n\n.reservation-status-card {\n  display: flex;\n  align-items: center;\n  padding: 20px;\n  border-radius: 4px;\n  margin-bottom: 20px;\n  color: white;\n}\n\n.status-icon {\n  font-size: 40px;\n  margin-right: 20px;\n}\n\n.status-text h2 {\n  margin: 0 0 10px 0;\n  font-size: 20px;\n}\n\n.status-text p {\n  margin: 0;\n  font-size: 16px;\n}\n\n.status-confirmed {\n  background-color: #67c23a;\n}\n\n.status-cancelled {\n  background-color: #f56c6c;\n}\n\n.status-completed {\n  background-color: #909399;\n}\n\n.status-in-use {\n  background-color: #409eff;\n}\n\n.status-expired {\n  background-color: #e6a23c;\n}\n\n.status-unknown {\n  background-color: #909399;\n}\n\n.history-card {\n  margin-bottom: 10px;\n}\n\n.history-time {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #ff7c40;\n  font-size: 16px;\n  border-bottom: 1px solid #EBEEF5;\n  padding-bottom: 10px;\n}\n\n.history-user {\n  font-weight: bold;\n  margin-bottom: 10px;\n  color: #606266;\n}\n\n.history-item {\n  margin-bottom: 15px;\n  padding-bottom: 15px;\n  border-bottom: 1px dashed #ebeef5;\n}\n\n.history-item:last-child {\n  margin-bottom: 0;\n  padding-bottom: 0;\n  border-bottom: none;\n}\n\n.history-action {\n  font-weight: bold;\n  margin-bottom: 8px;\n  font-size: 14px;\n}\n\n.history-field {\n  color: #409eff;\n}\n\n.history-values {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  padding-left: 10px;\n  font-size: 13px;\n}\n\n.history-old-value, .history-new-value {\n  display: flex;\n  align-items: flex-start;\n}\n\n.history-old-value {\n  color: #F56C6C;\n}\n\n.history-new-value {\n  color: #67C23A;\n}\n\n.history-label {\n  font-weight: bold;\n  margin-right: 10px;\n  min-width: 80px;\n  color: #606266;\n}\n\n.detail-card, .user-card {\n  margin-bottom: 20px;\n}\n\n.action-buttons {\n  margin-top: 30px;\n  text-align: center;\n}\n</style>\n"], "mappings": "AA0UA,SAAAA,cAAA,EAAAC,YAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;EACA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MACAC,mBAAA;MACAC,UAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,YAAA;MACAC,eAAA;MACAC,uBAAA;MACAC,uBAAA;MACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACAC,WAAA;QACAJ,aAAA,GACA;UAAAK,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAP,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,EACA;QACAL,SAAA,GACA;UAAAE,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,GACA;UAAAzB,IAAA;UAAAuB,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,qBAAA;QACAC,YAAA,OAAAA;MACA;MACAC,oBAAA;MACAC,cAAA;MACAC,cAAA;MACA;MACAC,sBAAA;MACAC,sBAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,UAAA;MACA,UAAA7B,WAAA;;MAEA;MACA,YAAAA,WAAA,CAAA8B,MAAA;IACA;IAEA;IACAC,UAAA;MACA,UAAA/B,WAAA;;MAEA;MACA,YAAAA,WAAA,CAAA8B,MAAA;IACA;IAEA;IACAE,UAAA;MACA,UAAAhC,WAAA;;MAEA;MACA,SAAAA,WAAA,CAAA8B,MAAA;;MAEA;MACA,MAAAG,GAAA,OAAAC,IAAA;MACA,MAAAC,SAAA,OAAAD,IAAA,MAAAlC,WAAA,CAAAoC,cAAA;MAEA,OAAAD,SAAA,GAAAF,GAAA;IACA;IAEA;IACAI,wBAAA;MACA,UAAAZ,cAAA,SAAAA,cAAA,CAAAa,MAAA;QACA;MACA;;MAEA;MACA,MAAAC,eAAA,QAAAd,cAAA,CAAAe,MAAA,CAAAC,MAAA,IACAA,MAAA,CAAAC,UAAA,WACA;;MAEA;MACA,MAAAC,cAAA;MACAJ,eAAA,CAAAK,OAAA,CAAAH,MAAA;QACA,MAAAI,SAAA,GAAAJ,MAAA,CAAAK,UAAA;QACA,KAAAH,cAAA,CAAAE,SAAA;UACAF,cAAA,CAAAE,SAAA;YACAA,SAAA,EAAAA,SAAA;YACAE,SAAA,EAAAN,MAAA,CAAAM,SAAA;YACAC,OAAA,EAAAP,MAAA,CAAAO,OAAA;YACAC,OAAA;UACA;QACA;QACAN,cAAA,CAAAE,SAAA,EAAAI,OAAA,CAAAC,IAAA,CAAAT,MAAA;MACA;;MAEA;MACA,OAAAU,MAAA,CAAAC,MAAA,CAAAT,cAAA,EAAAU,IAAA,EAAAC,CAAA,EAAAC,CAAA;QACA,WAAArB,IAAA,CAAAqB,CAAA,CAAAV,SAAA,QAAAX,IAAA,CAAAoB,CAAA,CAAAT,SAAA;MACA;IACA;IAEA;IACAW,kBAAA;MACA,SAAA/C,uBAAA,SAAAA,uBAAA,CAAA6B,MAAA;QACA,YAAAnB,EAAA;UAAAsC,KAAA,OAAAhD,uBAAA,CAAA6B;QAAA;MACA,gBAAA9B,eAAA;QACA,YAAAA,eAAA;MACA;QACA,YAAAW,EAAA;MACA;IACA;EACA;EAEAuC,QAAA;IACA,KAAAC,gBAAA;EACA;EAEAC,OAAA;IACA;IACA,MAAAD,iBAAA;MACA,KAAA5D,OAAA;MACA;QACA;QACA,MAAA8D,iBAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,MAAA;QACA,MAAAC,IAAA,QAAAH,MAAA,CAAAC,MAAA,CAAAE,IAAA;QACA,IAAAC,QAAA;QAEA,IAAAL,iBAAA;UACAM,OAAA,CAAAC,GAAA,kBAAAP,iBAAA;;UAEA;UACA,MAAAQ,aAAA,QAAAP,MAAA,CAAAQ,KAAA,CAAAL,IAAA;;UAEA;UACA,IAAAI,aAAA;YACAF,OAAA,CAAAC,GAAA,kBAAAC,aAAA,EAAAR,iBAAA;;YAEA;YACAM,OAAA,CAAAC,GAAA,kBAAAP,iBAAA;;YAEA;YACAK,QAAA,SAAA7E,cAAA,CAAAkF,oBAAA,CAAAF,aAAA,EAAAR,iBAAA;UACA;YACA;YACAM,OAAA,CAAAC,GAAA,gBAAAP,iBAAA;;YAEA;YACAM,OAAA,CAAAC,GAAA,oBAAAP,iBAAA;YACAK,QAAA,SAAA7E,cAAA,CAAAmF,sBAAA,CAAAX,iBAAA;UACA;QACA,gBAAAnE,OAAA;UACA;UACAwE,QAAA,SAAA7E,cAAA,CAAAoF,cAAA,CAAAR,IAAA;QACA;UACA;UACAC,QAAA,SAAA7E,cAAA,CAAAkF,oBAAA,CAAAN,IAAA;QACA;QAEA,IAAAC,QAAA,CAAApE,IAAA,CAAA4E,OAAA;UACA,KAAA1E,WAAA,GAAAkE,QAAA,CAAApE,IAAA,CAAAA,IAAA;UACAqE,OAAA,CAAAC,GAAA,kBAAApE,WAAA;;UAEA;UACA,SAAAA,WAAA,CAAA2E,wBAAA;YACA,KAAAhD,sBAAA;YACA,KAAAD,sBAAA,QAAA1B,WAAA,CAAA2E,wBAAA;YACAR,OAAA,CAAAC,GAAA,4BAAA1C,sBAAA;UACA;;UAEA;UACA,MAAAkD,eAAA,QAAAd,MAAA,CAAAQ,KAAA,CAAAO,KAAA,oBAAAf,MAAA,CAAAQ,KAAA,CAAAQ,WAAA;UACA,IAAAF,eAAA;YACA,KAAAlD,sBAAA,QAAAoC,MAAA,CAAAQ,KAAA,CAAAQ,WAAA;YACAX,OAAA,CAAAC,GAAA,+BAAA1C,sBAAA;UACA;;UAEA;UACA,KAAAqD,SAAA;YACA,SAAAjB,MAAA,CAAAQ,KAAA,CAAAU,IAAA,oBAAAhD,SAAA;cACAmC,OAAA,CAAAC,GAAA;cACA,KAAAa,gBAAA;YACA;UACA;QACA;UACA,KAAAC,QAAA,CAAAC,KAAA,CAAAjB,QAAA,CAAApE,IAAA,CAAAoB,OAAA,SAAAC,EAAA;UACA,KAAAnB,WAAA;QACA;MACA,SAAAmF,KAAA;QACAhB,OAAA,CAAAgB,KAAA,iCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA,KAAAnB,WAAA;MACA;QACA,KAAAD,OAAA;MACA;IACA;IAEA;IACAqF,OAAA;MACAjB,OAAA,CAAAC,GAAA,8CAAAN,MAAA,CAAAQ,KAAA;;MAEA;MACA,MAAAM,eAAA,QAAAd,MAAA,CAAAQ,KAAA,CAAAO,KAAA,oBAAAnD,sBAAA;MAEA,IAAAkD,eAAA;QACA;QACAT,OAAA,CAAAC,GAAA,mDAAA1C,sBAAA;;QAEA;QACA,MAAA4C,KAAA;UACAe,SAAA;UACAC,kBAAA,OAAAtF,WAAA,CAAAsF;QACA;;QAEA;QACA,SAAAxB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;UACAjB,KAAA,CAAAiB,WAAA,QAAAzB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;QACA;;QAEA;QACA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAkB,IAAA;UACAlB,KAAA,CAAAkB,IAAA,QAAA1B,MAAA,CAAAQ,KAAA,CAAAkB,IAAA;QACA;QAEA,KAAAC,OAAA,CAAAvC,IAAA;UACAwC,IAAA,iCAAAhE,sBAAA;UACA4C,KAAA,EAAAA;QACA;MACA;QACA;QACAH,OAAA,CAAAC,GAAA;;QAEA;QACA,SAAAN,MAAA,CAAAQ,KAAA,CAAAkB,IAAA,sBAAA1B,MAAA,CAAAQ,KAAA,CAAAiB,WAAA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;UACA;UACA,MAAAC,WAAA;UAEA,SAAA9B,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;YACAK,WAAA,CAAAL,WAAA,QAAAzB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;UACA;UAEA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;YACAC,WAAA,CAAAD,eAAA,QAAA7B,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;UACA;;UAEA;UACAC,WAAA,CAAAC,SAAA;UAEA1B,OAAA,CAAAC,GAAA,yCAAAwB,WAAA;UAEA,KAAAH,OAAA,CAAAvC,IAAA;YACAwC,IAAA;YACApB,KAAA,EAAAsB;UACA;QACA;UACA;UACA,KAAAH,OAAA,CAAAvC,IAAA;QACA;MACA;IACA;IAEA;IACA4C,eAAAC,QAAA;MACA,OAAAxG,UAAA,CAAAwG,QAAA;IACA;IAEA;IACAC,eAAAlE,MAAA;MACA,MAAAmE,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAL,SAAA,CAAAnE,MAAA;IACA;IAEA;IACAyE,cAAAzE,MAAA;MACA,MAAA0E,OAAA;QACAN,SAAA;QACAC,SAAA;QACAC,SAAA;QACAC,MAAA;QACAC,OAAA;MACA;MACA,OAAAE,OAAA,CAAA1E,MAAA;IACA;IAEA;IACA2E,cAAA3E,MAAA;MACA,MAAAmE,SAAA;QACAC,SAAA,OAAA/E,EAAA;QACAgF,SAAA,OAAAhF,EAAA;QACAiF,SAAA,OAAAjF,EAAA;QACAkF,MAAA,OAAAlF,EAAA;QACAmF,OAAA,OAAAnF,EAAA;MACA;MACA,OAAA8E,SAAA,CAAAnE,MAAA,UAAAX,EAAA;IACA;IAIA;IACAuF,iBAAA;MACA,KAAAzG,mBAAA;IACA;IAEA;IACA0G,iBAAA;MACA,KAAAxG,mBAAA;IACA;IAEA;IACA,MAAAyG,kBAAA;MACA,KAAA1G,UAAA;MACA;QACA,MAAAgE,QAAA,SAAA7E,cAAA,CAAAuH,iBAAA,CACA,KAAA5G,WAAA,CAAA6G,gBAAA,EACA;UAAAvB,kBAAA,OAAAtF,WAAA,CAAAsF;QAAA,CACA;QAEA,IAAApB,QAAA,CAAApE,IAAA,CAAA4E,OAAA;UACA,KAAAQ,QAAA,CAAAR,OAAA,MAAAvD,EAAA;UACA,KAAAlB,mBAAA;;UAEA;UACA6G,UAAA;YACA;YACA,MAAAlC,eAAA,QAAAd,MAAA,CAAAQ,KAAA,CAAAO,KAAA,oBAAAnD,sBAAA;YAEA,IAAAkD,eAAA;cACA;cACA,MAAAN,KAAA;gBACAe,SAAA;gBACAC,kBAAA,OAAAtF,WAAA,CAAAsF;cACA;;cAEA;cACA,SAAAxB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;gBACAjB,KAAA,CAAAiB,WAAA,QAAAzB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;cACA;cAEA,KAAAE,OAAA,CAAAvC,IAAA;gBACAwC,IAAA,iCAAAhE,sBAAA;gBACA4C,KAAA,EAAAA;cACA;YACA;cACA;cACA,SAAAR,MAAA,CAAAQ,KAAA,CAAAkB,IAAA,sBAAA1B,MAAA,CAAAQ,KAAA,CAAAiB,WAAA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;gBACA,MAAAC,WAAA;gBAEA,SAAA9B,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;kBACAK,WAAA,CAAAL,WAAA,QAAAzB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;gBACA;gBAEA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;kBACAC,WAAA,CAAAD,eAAA,QAAA7B,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;gBACA;gBAEAC,WAAA,CAAAC,SAAA;gBAEA,KAAAJ,OAAA,CAAAvC,IAAA;kBACAwC,IAAA;kBACApB,KAAA,EAAAsB;gBACA;cACA;gBACA,KAAAH,OAAA,CAAAvC,IAAA;cACA;YACA;UACA;QACA;UACA,KAAAgC,QAAA,CAAAC,KAAA,CAAAjB,QAAA,CAAApE,IAAA,CAAAoB,OAAA,SAAAC,EAAA;QACA;MACA,SAAAgE,KAAA;QACAhB,OAAA,CAAAgB,KAAA,kCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;MACA;QACA,KAAAjB,UAAA;MACA;IACA;IAEA;IACA,MAAA6G,gBAAA;MACA,KAAA3G,SAAA;MACA;QACA;QACA;QACA,MAAA8D,QAAA,SAAA7E,cAAA,CAAAuH,iBAAA,CACA,KAAA5G,WAAA,CAAA6G,gBAAA,EACA;UACAvB,kBAAA,OAAAtF,WAAA,CAAAsF,kBAAA;UACA0B,eAAA;QACA,CACA;QAEA,IAAA9C,QAAA,CAAApE,IAAA,CAAA4E,OAAA;UACA,KAAAQ,QAAA,CAAAR,OAAA,MAAAvD,EAAA;UACA,KAAAhB,mBAAA;;UAEA;UACA2G,UAAA;YACA;YACA,MAAAlC,eAAA,QAAAd,MAAA,CAAAQ,KAAA,CAAAO,KAAA,oBAAAnD,sBAAA;YAEA,IAAAkD,eAAA;cACA;cACA,MAAAN,KAAA;gBACAe,SAAA;gBACAC,kBAAA,OAAAtF,WAAA,CAAAsF;cACA;;cAEA;cACA,SAAAxB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;gBACAjB,KAAA,CAAAiB,WAAA,QAAAzB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;cACA;cAEA,KAAAE,OAAA,CAAAvC,IAAA;gBACAwC,IAAA,iCAAAhE,sBAAA;gBACA4C,KAAA,EAAAA;cACA;YACA;cACA;cACA,SAAAR,MAAA,CAAAQ,KAAA,CAAAkB,IAAA,sBAAA1B,MAAA,CAAAQ,KAAA,CAAAiB,WAAA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;gBACA,MAAAC,WAAA;gBAEA,SAAA9B,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;kBACAK,WAAA,CAAAL,WAAA,QAAAzB,MAAA,CAAAQ,KAAA,CAAAiB,WAAA;gBACA;gBAEA,SAAAzB,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;kBACAC,WAAA,CAAAD,eAAA,QAAA7B,MAAA,CAAAQ,KAAA,CAAAqB,eAAA;gBACA;gBAEAC,WAAA,CAAAC,SAAA;gBAEA,KAAAJ,OAAA,CAAAvC,IAAA;kBACAwC,IAAA;kBACApB,KAAA,EAAAsB;gBACA;cACA;gBACA,KAAAH,OAAA,CAAAvC,IAAA;cACA;YACA;UACA;QACA;UACA,KAAAgC,QAAA,CAAAC,KAAA,CAAAjB,QAAA,CAAApE,IAAA,CAAAoB,OAAA,SAAAC,EAAA;QACA;MACA,SAAAgE,KAAA;QACAhB,OAAA,CAAAgB,KAAA,gCAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;MACA;QACA,KAAAf,SAAA;MACA;IACA;IAEA;IACAkB,aAAA2F,IAAA;MACA,OAAAA,IAAA,CAAAC,OAAA,KAAAhF,IAAA,CAAAD,GAAA;IACA;IAEA;IACAgD,iBAAA;MACA;MACA,KAAAtE,UAAA;QACAC,aAAA,OAAAZ,WAAA,CAAAoC,cAAA;QACAvB,WAAA,OAAAb,WAAA,CAAAmH,YAAA;QACArG,OAAA,OAAAd,WAAA,CAAAc,OAAA;QACAC,SAAA,OAAAf,WAAA,CAAAoH,UAAA;MACA;;MAEA;MACA,KAAA/G,mBAAA;IACA;IAEA;IACAgH,kBAAA;MACA,MAAAlF,SAAA,OAAAD,IAAA,MAAAvB,UAAA,CAAAC,aAAA;MACA,MAAA0G,OAAA,OAAApF,IAAA,MAAAvB,UAAA,CAAAE,WAAA;MAEA,IAAAsB,SAAA,IAAAmF,OAAA;QACA,KAAApC,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA;MACA;MAEA;IACA;IAEA;IACA,MAAAoG,sBAAA;MACA,UAAA5G,UAAA,CAAAC,aAAA,UAAAD,UAAA,CAAAE,WAAA;QACA,KAAAH,uBAAA;QACA;MACA;;MAEA;MACA,SAAAC,UAAA,CAAAC,aAAA,SAAAD,UAAA,CAAAE,WAAA;QACA,KAAAqE,QAAA,CAAAsC,OAAA,MAAArG,EAAA;QACA,KAAAZ,YAAA;QACA,KAAAG,uBAAA;QACA;MACA;MAEA;QACA,MAAA+G,WAAA,QAAAzH,WAAA,CAAA0H,YAAA;QACA,MAAAC,SAAA,QAAAhH,UAAA,CAAAC,aAAA;QACA,MAAAgH,OAAA,QAAAjH,UAAA,CAAAE,WAAA;;QAEA;QACA,MAAAgH,SAAA,QAAA7H,WAAA,CAAA8H,EAAA;QACA,MAAA/D,MAAA;UACAgE,UAAA,EAAAJ,SAAA;UACAK,QAAA,EAAAJ;QACA;;QAEA;QACA,IAAAC,SAAA,YAAAA,SAAA,KAAAI,SAAA;UACAlE,MAAA,CAAAmE,sBAAA,GAAAL,SAAA;QACA;QAIA,MAAA3D,QAAA,cAAAiE,KAAA,CAAAC,GAAA,mBAAAX,WAAA;UAAA1D;QAAA;;QAEA;QACA,IAAAG,QAAA,CAAApE,IAAA,CAAAuI,mBAAA;UACA;UACAlE,OAAA,CAAAC,GAAA,eAAAF,QAAA,CAAApE,IAAA,CAAAwI,SAAA;UACA,KAAA/H,YAAA,GAAA2D,QAAA,CAAApE,IAAA,CAAAwI,SAAA,CAAAC,QAAA;QACA;UACA;UACApE,OAAA,CAAAC,GAAA,aAAAF,QAAA,CAAApE,IAAA,CAAAwI,SAAA;UACA,KAAA/H,YAAA,GAAA2D,QAAA,CAAApE,IAAA,CAAAwI,SAAA,CAAAC,QAAA;QACA;;QAEA;QACA,SAAAhI,YAAA;UACA4D,OAAA,CAAAC,GAAA,aAAAF,QAAA,CAAApE,IAAA,CAAAwI,SAAA;;UAEA;UACA,KAAA7H,uBAAA,GAAAyD,QAAA,CAAApE,IAAA,CAAA0I,wBAAA;;UAEA;UACA,IAAAtE,QAAA,CAAApE,IAAA,CAAA2I,kBAAA,IAAAvE,QAAA,CAAApE,IAAA,CAAA4I,gBAAA;YACA,KAAAlI,eAAA,QAAAW,EAAA;cAAAsC,KAAA,EAAAS,QAAA,CAAApE,IAAA,CAAA4I;YAAA;UACA;YACA,KAAAlI,eAAA;UACA;QACA;UACA2D,OAAA,CAAAC,GAAA;UACA,KAAA5D,eAAA;UACA,KAAAC,uBAAA;QACA;QAEA,KAAAC,uBAAA;MACA,SAAAyE,KAAA;QACAhB,OAAA,CAAAgB,KAAA,kCAAAA,KAAA;QACA,KAAA5E,YAAA;QACA,KAAAG,uBAAA;QACA,KAAAD,uBAAA;QACA,KAAAyE,QAAA,CAAAC,KAAA,MAAAhE,EAAA;MACA;IACA;IAEA;IACA,MAAAwH,YAAA;MACA,KAAApH,oBAAA;MACA,KAAAC,cAAA;MAEA;QACA;QACA,MAAA0C,QAAA,SAAA7E,cAAA,CAAAuJ,qBAAA,CACA,KAAA5I,WAAA,CAAA6G,gBAAA,EACA,KAAA7G,WAAA,CAAAsF,kBACA;QAEA,IAAApB,QAAA,CAAApE,IAAA,CAAA4E,OAAA;UACA,KAAAjD,cAAA,GAAAyC,QAAA,CAAApE,IAAA,CAAAA,IAAA;QACA;UACA,KAAAoF,QAAA,CAAAC,KAAA,CAAAjB,QAAA,CAAApE,IAAA,CAAAoB,OAAA,SAAAC,EAAA;UACA,KAAAM,cAAA;QACA;MACA,SAAA0D,KAAA;QACAhB,OAAA,CAAAgB,KAAA,6BAAAA,KAAA;QACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA,KAAAM,cAAA;MACA;QACA,KAAAD,cAAA;MACA;IACA;IAEA;IACAqH,mBAAAC,MAAA;MACA,MAAAC,OAAA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,MAAA;IACA;IAEA;IACAE,qBAAAF,MAAA;MACA,MAAAG,SAAA;QACA,eAAA9H,EAAA;QACA,eAAAA,EAAA;QACA,eAAAA,EAAA;QACA,sBAAAA,EAAA;MACA;MACA,OAAA8H,SAAA,CAAAH,MAAA,KAAAA,MAAA;IACA;IAEA;IACAI,oBAAAC,SAAA;MACA,MAAAC,QAAA;QACA,uBAAAjI,EAAA;QACA,qBAAAA,EAAA;QACA,gBAAAA,EAAA;QACA,mBAAAA,EAAA;QACA,eAAAA,EAAA;MACA;MACA,OAAAiI,QAAA,CAAAD,SAAA,KAAAA,SAAA;IACA;IAEA;IACAE,mBAAAF,SAAA,EAAAG,KAAA;MACA,KAAAA,KAAA;MAEA,IAAAH,SAAA,yBAAAA,SAAA;QACA,OAAA5J,UAAA,CAAA+J,KAAA;MACA,WAAAH,SAAA;QACA,YAAA1C,aAAA,CAAA6C,KAAA;MACA;MAEA,OAAAA,KAAA;IACA;IAEA;IACAC,yBAAA;MACA,UAAAvJ,WAAA,UAAAA,WAAA,CAAA2E,wBAAA;;MAEA;MACA,KAAAc,OAAA,CAAAvC,IAAA;QACAwC,IAAA,iCAAA1F,WAAA,CAAA2E,wBAAA;QACAL,KAAA;UACAkF,SAAA,OAAA9J,OAAA;UACAiG,eAAA,OAAA3F,WAAA,CAAA6G;QACA;MACA;IACA;IAEA;IACA4C,iBAAA;MACA,KAAAC,KAAA,CAAA/I,UAAA,CAAAgJ,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;;QAEA;QACA,UAAAvC,iBAAA;;QAEA;QACA,SAAA9G,YAAA;UACA,KAAA2E,QAAA,CAAAC,KAAA,MAAAhE,EAAA;UACA;QACA;QAEA,KAAAb,SAAA;QACA;UACA;UACA,MAAAuJ,UAAA;YACAzH,cAAA,OAAAzB,UAAA,CAAAC,aAAA;YACAuG,YAAA,OAAAxG,UAAA,CAAAE,WAAA;YACAC,OAAA,OAAAH,UAAA,CAAAG,OAAA,IAAAmH,SAAA;YACAb,UAAA,OAAAzG,UAAA,CAAAI,SAAA,IAAAkH,SAAA;YACA6B,IAAA,OAAAC,KAAA,CAAAC;UACA;;UAEA;UACA,MAAA9F,QAAA,SAAA7E,cAAA,CAAA4K,iBAAA,CACA,KAAAjK,WAAA,CAAA6G,gBAAA,EACAgD,UAAA,EACA,KAAA7J,WAAA,CAAAsF,kBAAA;UACA;UAEA,IAAApB,QAAA,CAAApE,IAAA,CAAA4E,OAAA;YACA,KAAAQ,QAAA,CAAAR,OAAA,MAAAvD,EAAA;YACA,KAAAd,mBAAA;YACA;YACA,WAAAsD,gBAAA;UACA;YACA,KAAAuB,QAAA,CAAAC,KAAA,CAAAjB,QAAA,CAAApE,IAAA,CAAAoB,OAAA,SAAAC,EAAA;UACA;QACA,SAAAgE,KAAA;UACAhB,OAAA,CAAAgB,KAAA,kCAAAA,KAAA;UACA,KAAAD,QAAA,CAAAC,KAAA,MAAAhE,EAAA;QACA;UACA,KAAAb,SAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}