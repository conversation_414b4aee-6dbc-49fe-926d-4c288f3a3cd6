{"ast": null, "code": "import { mapGetters, mapActions } from 'vuex';\nimport { equipmentApi } from '@/api';\nimport axios from 'axios';\nexport default {\n  name: 'EquipmentList',\n  data() {\n    return {\n      loading: false,\n      equipments: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 20,\n      categories: [],\n      filter: {\n        category: '',\n        status: '',\n        search: ''\n      }\n    };\n  },\n  computed: {\n    ...mapGetters(['getEquipmentCategories']),\n    // 获取完整的图片URL\n    baseUrl() {\n      return axios.defaults.baseURL || '';\n    }\n  },\n  created() {\n    this.fetchData();\n    this.fetchCategories();\n  },\n  methods: {\n    ...mapActions(['fetchEquipmentCategories']),\n    async fetchData() {\n      this.loading = true;\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize,\n          category: this.filter.category || undefined,\n          status: this.filter.status || undefined,\n          search: this.filter.search || undefined\n        };\n        const response = await equipmentApi.getEquipments(params);\n        this.equipments = response.data.items;\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('Failed to fetch equipments:', error);\n        this.$message.error(this.$t('common.error'));\n      } finally {\n        this.loading = false;\n      }\n    },\n    async fetchCategories() {\n      try {\n        const response = await equipmentApi.getCategories();\n        // 直接使用返回的类别列表，不需要再映射\n        this.categories = response.data.categories || [];\n      } catch (error) {\n        console.error('Failed to fetch categories:', error);\n        // 确保即使API调用失败，categories也是一个数组\n        this.categories = [];\n      }\n    },\n    handleFilterChange() {\n      this.currentPage = 1;\n      this.fetchData();\n    },\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchData();\n    },\n    viewEquipmentDetail(id) {\n      this.$router.push(`/equipment/${id}`);\n    },\n    reserveEquipment(id) {\n      this.$router.push(`/equipment/${id}/reserve`);\n    },\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '';\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://')) {\n        return url;\n      }\n\n      // 如果是相对路径，添加基础URL\n      if (url.startsWith('/')) {\n        return this.baseUrl + url;\n      }\n\n      // 其他情况，添加基础URL和斜杠\n      return this.baseUrl + '/' + url;\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "mapActions", "equipmentApi", "axios", "name", "data", "loading", "equipments", "total", "currentPage", "pageSize", "categories", "filter", "category", "status", "search", "computed", "baseUrl", "defaults", "baseURL", "created", "fetchData", "fetchCategories", "methods", "params", "page", "limit", "undefined", "response", "getEquipments", "items", "error", "console", "$message", "$t", "getCategories", "handleFilterChange", "handlePageChange", "viewEquipmentDetail", "id", "$router", "push", "reserveEquipment", "getFullImageUrl", "url", "startsWith"], "sources": ["src/views/equipment/EquipmentList.vue"], "sourcesContent": ["<template>\n  <div class=\"equipment-list\">\n    <h1 class=\"page-title\">{{ $t('equipment.list') }}</h1>\n\n    <!-- 筛选和搜索 -->\n    <el-card class=\"filter-card\">\n      <el-row :gutter=\"20\">\n        <el-col :xs=\"24\" :sm=\"8\" :md=\"6\">\n          <el-select\n            v-model=\"filter.category\"\n            :placeholder=\"$t('equipment.allCategories')\"\n            clearable\n            style=\"width: 100%\"\n            @change=\"handleFilterChange\"\n          >\n            <el-option\n              v-for=\"category in categories\"\n              :key=\"category\"\n              :label=\"category\"\n              :value=\"category\"\n            ></el-option>\n          </el-select>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"8\" :md=\"6\">\n          <el-select\n            v-model=\"filter.status\"\n            :placeholder=\"$t('equipment.allStatus')\"\n            clearable\n            style=\"width: 100%\"\n            @change=\"handleFilterChange\"\n          >\n            <el-option\n              :label=\"$t('equipment.available')\"\n              value=\"available\"\n            ></el-option>\n            <el-option\n              :label=\"$t('equipment.maintenance')\"\n              value=\"maintenance\"\n            ></el-option>\n          </el-select>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"8\" :md=\"12\">\n          <el-input\n            v-model=\"filter.search\"\n            :placeholder=\"$t('equipment.searchPlaceholder')\"\n            clearable\n            @keyup.enter.native=\"handleFilterChange\"\n            @clear=\"handleFilterChange\"\n          >\n            <el-button\n              slot=\"append\"\n              icon=\"el-icon-search\"\n              @click=\"handleFilterChange\"\n            ></el-button>\n          </el-input>\n        </el-col>\n      </el-row>\n    </el-card>\n\n    <!-- 设备列表 -->\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"3\" animated />\n    </div>\n\n    <div v-else>\n      <div v-if=\"equipments.length === 0\" class=\"empty-data\">\n        <el-empty :description=\"$t('common.noData')\"></el-empty>\n      </div>\n\n      <el-row :gutter=\"20\" v-else>\n        <el-col\n          v-for=\"equipment in equipments\"\n          :key=\"equipment.id\"\n          :xs=\"24\"\n          :sm=\"12\"\n          :md=\"8\"\n          :lg=\"6\"\n          class=\"equipment-item\"\n        >\n          <el-card\n            :body-style=\"{ padding: '0px' }\"\n            shadow=\"hover\"\n            @click.native=\"viewEquipmentDetail(equipment.id)\"\n          >\n            <div class=\"equipment-image-container\">\n              <img\n                :src=\"equipment.image_path ? getFullImageUrl(equipment.image_path) : require('@/assets/upload.png')\"\n                :alt=\"equipment.name\"\n                class=\"equipment-image\"\n              />\n            </div>\n\n            <div class=\"equipment-info\">\n              <h3 class=\"equipment-name\">{{ equipment.name }}</h3>\n              <p class=\"equipment-category\">{{ equipment.category }}</p>\n\n              <div class=\"equipment-meta\">\n                <span class=\"equipment-location\" v-if=\"equipment.location\">\n                  <i class=\"el-icon-location\"></i> {{ equipment.location }}\n                </span>\n\n                <el-tag\n                  v-if=\"equipment.status !== 'available'\"\n                  type=\"warning\"\n                  size=\"medium\"\n                  style=\"font-weight: bold; padding: 0px 10px; font-size: 14px;\"\n                >\n                  {{ $t('equipment.maintenance') }}\n                </el-tag>\n                <el-tag\n                  v-else\n                  type=\"success\"\n                  size=\"medium\"\n                  style=\"font-weight: bold; padding: 0px 10px; font-size: 14px;\"\n                >\n                  {{ $t('equipment.available') }}\n                </el-tag>\n\n                <!-- 可同时预定标记 -->\n                <el-tag\n                  v-if=\"equipment.allow_simultaneous\"\n                  type=\"primary\"\n                  size=\"mini\"\n                  style=\"margin-left: 5px; font-size: 12px;\"\n                >\n                  {{ $t('equipment.simultaneousReservation') }} ({{ equipment.max_simultaneous }})\n                </el-tag>\n              </div>\n\n              <div class=\"equipment-actions\">\n                <el-button\n                  type=\"danger\"\n                  size=\"small\"\n                  @click.stop=\"viewEquipmentDetail(equipment.id)\"\n                  style=\"font-weight: bold;\"\n                >\n                  {{ $t('equipment.viewDetail') }}\n                </el-button>\n\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click.stop=\"reserveEquipment(equipment.id)\"\n                  :disabled=\"equipment.status !== 'available'\"\n                >\n                  {{ $t('equipment.reserve') }}\n                </el-button>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 分页 -->\n      <div class=\"pagination-container\">\n        <el-pagination\n          background\n          layout=\"prev, pager, next\"\n          :total=\"total\"\n          :page-size=\"pageSize\"\n          :current-page.sync=\"currentPage\"\n          @current-change=\"handlePageChange\"\n        ></el-pagination>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { mapGetters, mapActions } from 'vuex'\nimport { equipmentApi } from '@/api'\nimport axios from 'axios'\n\nexport default {\n  name: 'EquipmentList',\n\n  data() {\n    return {\n      loading: false,\n      equipments: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 20,\n      categories: [],\n      filter: {\n        category: '',\n        status: '',\n        search: ''\n      }\n    }\n  },\n\n  computed: {\n    ...mapGetters(['getEquipmentCategories']),\n\n    // 获取完整的图片URL\n    baseUrl() {\n      return axios.defaults.baseURL || '';\n    }\n  },\n\n  created() {\n    this.fetchData()\n    this.fetchCategories()\n  },\n\n  methods: {\n    ...mapActions(['fetchEquipmentCategories']),\n\n    async fetchData() {\n      this.loading = true\n      try {\n        const params = {\n          page: this.currentPage,\n          limit: this.pageSize,\n          category: this.filter.category || undefined,\n          status: this.filter.status || undefined,\n          search: this.filter.search || undefined\n        }\n\n        const response = await equipmentApi.getEquipments(params)\n        this.equipments = response.data.items\n        this.total = response.data.total\n      } catch (error) {\n        console.error('Failed to fetch equipments:', error)\n        this.$message.error(this.$t('common.error'))\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async fetchCategories() {\n      try {\n        const response = await equipmentApi.getCategories()\n        // 直接使用返回的类别列表，不需要再映射\n        this.categories = response.data.categories || []\n      } catch (error) {\n        console.error('Failed to fetch categories:', error)\n        // 确保即使API调用失败，categories也是一个数组\n        this.categories = []\n      }\n    },\n\n    handleFilterChange() {\n      this.currentPage = 1\n      this.fetchData()\n    },\n\n    handlePageChange(page) {\n      this.currentPage = page\n      this.fetchData()\n    },\n\n    viewEquipmentDetail(id) {\n      this.$router.push(`/equipment/${id}`)\n    },\n\n    reserveEquipment(id) {\n      this.$router.push(`/equipment/${id}/reserve`)\n    },\n\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '';\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://')) {\n        return url;\n      }\n\n      // 如果是相对路径，添加基础URL\n      if (url.startsWith('/')) {\n        return this.baseUrl + url;\n      }\n\n      // 其他情况，添加基础URL和斜杠\n      return this.baseUrl + '/' + url;\n    }\n  }\n}\n</script>\n\n<style scoped>\n.equipment-list {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.page-title {\n  margin-bottom: 20px;\n  font-size: 24px;\n  /* 不设置颜色，使用全局CSS中的颜色 */\n}\n\n.filter-card {\n  margin-bottom: 20px;\n}\n\n.equipment-item {\n  margin-bottom: 20px;\n}\n\n.equipment-image-container {\n  height: 200px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background-color: #f5f7fa;\n}\n\n.equipment-image {\n  max-height: 100%;\n  max-width: 100%;\n  object-fit: contain;\n}\n\n.equipment-info {\n  padding: 14px;\n}\n\n.equipment-name {\n  margin: 0 0 10px;\n  font-size: 18px;\n  font-weight: bold;\n  color: #303133;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.equipment-category {\n  margin: 0 0 10px;\n  font-size: 14px;\n  color: #909399;\n}\n\n.equipment-meta {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.equipment-location {\n  font-size: 13px;\n  color: #606266;\n}\n\n.equipment-actions {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-top: 10px;\n  border-top: 1px solid #ebeef5;\n  padding-top: 10px;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.empty-data {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.pagination-container {\n  text-align: center;\n  margin-top: 30px;\n  margin-bottom: 20px;\n}\n\n@media (max-width: 768px) {\n  .equipment-list {\n    padding-bottom: 40px !important; /* 增加底部空间，确保内容完全可见 */\n    min-height: calc(100vh - 60px) !important;\n  }\n\n  .filter-card .el-col {\n    margin-bottom: 10px;\n  }\n\n  .pagination-container {\n    margin-bottom: 40px !important; /* 确保分页组件底部有足够空间 */\n  }\n}\n</style>\n"], "mappings": "AA2KA,SAAAA,UAAA,EAAAC,UAAA;AACA,SAAAC,YAAA;AACA,OAAAC,KAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,UAAA;MACAC,MAAA;QACAC,QAAA;QACAC,MAAA;QACAC,MAAA;MACA;IACA;EACA;EAEAC,QAAA;IACA,GAAAhB,UAAA;IAEA;IACAiB,QAAA;MACA,OAAAd,KAAA,CAAAe,QAAA,CAAAC,OAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,eAAA;EACA;EAEAC,OAAA;IACA,GAAAtB,UAAA;IAEA,MAAAoB,UAAA;MACA,KAAAf,OAAA;MACA;QACA,MAAAkB,MAAA;UACAC,IAAA,OAAAhB,WAAA;UACAiB,KAAA,OAAAhB,QAAA;UACAG,QAAA,OAAAD,MAAA,CAAAC,QAAA,IAAAc,SAAA;UACAb,MAAA,OAAAF,MAAA,CAAAE,MAAA,IAAAa,SAAA;UACAZ,MAAA,OAAAH,MAAA,CAAAG,MAAA,IAAAY;QACA;QAEA,MAAAC,QAAA,SAAA1B,YAAA,CAAA2B,aAAA,CAAAL,MAAA;QACA,KAAAjB,UAAA,GAAAqB,QAAA,CAAAvB,IAAA,CAAAyB,KAAA;QACA,KAAAtB,KAAA,GAAAoB,QAAA,CAAAvB,IAAA,CAAAG,KAAA;MACA,SAAAuB,KAAA;QACAC,OAAA,CAAAD,KAAA,gCAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA,MAAAG,EAAA;MACA;QACA,KAAA5B,OAAA;MACA;IACA;IAEA,MAAAgB,gBAAA;MACA;QACA,MAAAM,QAAA,SAAA1B,YAAA,CAAAiC,aAAA;QACA;QACA,KAAAxB,UAAA,GAAAiB,QAAA,CAAAvB,IAAA,CAAAM,UAAA;MACA,SAAAoB,KAAA;QACAC,OAAA,CAAAD,KAAA,gCAAAA,KAAA;QACA;QACA,KAAApB,UAAA;MACA;IACA;IAEAyB,mBAAA;MACA,KAAA3B,WAAA;MACA,KAAAY,SAAA;IACA;IAEAgB,iBAAAZ,IAAA;MACA,KAAAhB,WAAA,GAAAgB,IAAA;MACA,KAAAJ,SAAA;IACA;IAEAiB,oBAAAC,EAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,eAAAF,EAAA;IACA;IAEAG,iBAAAH,EAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,eAAAF,EAAA;IACA;IAEA;IACAI,gBAAAC,GAAA;MACA,KAAAA,GAAA;;MAEA;MACA,IAAAA,GAAA,CAAAC,UAAA,eAAAD,GAAA,CAAAC,UAAA;QACA,OAAAD,GAAA;MACA;;MAEA;MACA,IAAAA,GAAA,CAAAC,UAAA;QACA,YAAA5B,OAAA,GAAA2B,GAAA;MACA;;MAEA;MACA,YAAA3B,OAAA,SAAA2B,GAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}