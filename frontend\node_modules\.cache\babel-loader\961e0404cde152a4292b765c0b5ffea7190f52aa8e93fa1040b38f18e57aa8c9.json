{"ast": null, "code": "import LanguageSwitcher from '@/components/common/LanguageSwitcher.vue';\nimport ThemeSwitch from '@/components/common/ThemeSwitch.vue';\nexport default {\n  name: 'MobileNav',\n  components: {\n    LanguageSwitcher,\n    ThemeSwitch\n  },\n  data() {\n    return {\n      isOpen: false\n    };\n  },\n  computed: {\n    activeRoute() {\n      return this.$route.path;\n    }\n  },\n  methods: {\n    toggleMenu() {\n      this.isOpen = !this.isOpen;\n\n      // 禁用/启用body滚动\n      if (this.isOpen) {\n        document.body.style.overflow = 'hidden';\n      } else {\n        document.body.style.overflow = '';\n      }\n    },\n    closeMenu() {\n      this.isOpen = false;\n      document.body.style.overflow = '';\n    },\n    handleSelect(key) {\n      this.$router.push(key);\n      this.closeMenu();\n    }\n  },\n  // 路由变化时关闭菜单\n  watch: {\n    '$route'() {\n      this.closeMenu();\n    }\n  },\n  // 组件销毁时恢复body滚动\n  beforeDestroy() {\n    document.body.style.overflow = '';\n  }\n};", "map": {"version": 3, "names": ["LanguageSwitcher", "ThemeSwitch", "name", "components", "data", "isOpen", "computed", "activeRoute", "$route", "path", "methods", "toggleMenu", "document", "body", "style", "overflow", "closeMenu", "handleSelect", "key", "$router", "push", "watch", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/components/layout/MobileNav.vue"], "sourcesContent": ["<template>\n  <div class=\"mobile-nav\">\n    <div class=\"mobile-nav-overlay\" v-if=\"isOpen\" @click=\"closeMenu\"></div>\n\n    <div class=\"mobile-nav-toggle\" @click=\"toggleMenu\">\n      <i :class=\"isOpen ? 'el-icon-close' : 'el-icon-menu'\"></i>\n    </div>\n\n    <div class=\"mobile-nav-menu\" :class=\"{ 'mobile-nav-menu-open': isOpen }\">\n      <div class=\"mobile-nav-header\">\n        <img src=\"@/assets/images/logo.svg\" alt=\"Logo\" class=\"mobile-nav-logo\">\n        <h3 class=\"mobile-nav-title\">{{ $t('common.appName') }}</h3>\n      </div>\n\n      <el-menu\n        :default-active=\"activeRoute\"\n        class=\"mobile-nav-list\"\n        background-color=\"#304156\"\n        text-color=\"#bfcbd9\"\n        active-text-color=\"#409EFF\"\n        @select=\"handleSelect\"\n      >\n        <el-menu-item index=\"/\">\n          <i class=\"el-icon-s-home\"></i>\n          <span>{{ $t('nav.home') }}</span>\n        </el-menu-item>\n\n        <el-menu-item index=\"/calendar\">\n          <i class=\"el-icon-date\"></i>\n          <span>{{ $t('nav.calendar') }}</span>\n        </el-menu-item>\n\n        <el-menu-item index=\"/equipment\">\n          <i class=\"el-icon-s-grid\"></i>\n          <span>{{ $t('nav.equipment') }}</span>\n        </el-menu-item>\n\n        <el-menu-item index=\"/reservation/query\">\n          <i class=\"el-icon-s-order\"></i>\n          <span>{{ $t('nav.reservation') }}</span>\n        </el-menu-item>\n\n        <el-menu-item index=\"/admin/login\">\n          <i class=\"el-icon-s-custom\"></i>\n          <span>{{ $t('nav.admin') }}</span>\n        </el-menu-item>\n      </el-menu>\n\n      <div class=\"mobile-nav-footer\">\n        <div class=\"mobile-footer-controls\">\n          <theme-switch class=\"mobile-theme-switch\" />\n        <language-switcher />\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport LanguageSwitcher from '@/components/common/LanguageSwitcher.vue'\nimport ThemeSwitch from '@/components/common/ThemeSwitch.vue'\n\nexport default {\n  name: 'MobileNav',\n\n  components: {\n    LanguageSwitcher,\n    ThemeSwitch\n  },\n\n  data() {\n    return {\n      isOpen: false\n    }\n  },\n\n  computed: {\n    activeRoute() {\n      return this.$route.path\n    }\n  },\n\n  methods: {\n    toggleMenu() {\n      this.isOpen = !this.isOpen\n\n      // 禁用/启用body滚动\n      if (this.isOpen) {\n        document.body.style.overflow = 'hidden'\n      } else {\n        document.body.style.overflow = ''\n      }\n    },\n\n    closeMenu() {\n      this.isOpen = false\n      document.body.style.overflow = ''\n    },\n\n    handleSelect(key) {\n      this.$router.push(key)\n      this.closeMenu()\n    }\n  },\n\n  // 路由变化时关闭菜单\n  watch: {\n    '$route'() {\n      this.closeMenu()\n    }\n  },\n\n  // 组件销毁时恢复body滚动\n  beforeDestroy() {\n    document.body.style.overflow = ''\n  }\n}\n</script>\n\n<style scoped>\n.mobile-nav {\n  display: none;\n}\n\n.mobile-nav-toggle {\n  position: fixed;\n  top: 15px;\n  right: 15px;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background-color: #409EFF;\n  color: #fff;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  cursor: pointer;\n  z-index: 1001;\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.mobile-nav-toggle i {\n  font-size: 24px;\n}\n\n.mobile-nav-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  background-color: rgba(0, 0, 0, 0.5);\n  z-index: 999;\n}\n\n.mobile-nav-menu {\n  position: fixed;\n  top: 0;\n  right: -280px;\n  width: 280px;\n  height: 100%;\n  background-color: #304156;\n  z-index: 1000;\n  transition: right 0.3s ease;\n  display: flex;\n  flex-direction: column;\n  overflow-y: auto;\n}\n\n.mobile-nav-menu-open {\n  right: 0;\n}\n\n.mobile-nav-header {\n  height: 60px;\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n  background-color: #263445;\n}\n\n.mobile-nav-logo {\n  width: 32px;\n  height: 32px;\n  margin-right: 10px;\n}\n\n.mobile-nav-title {\n  margin: 0;\n  font-size: 16px;\n  font-weight: 600;\n  color: #fff;\n}\n\n.mobile-nav-list {\n  flex: 1;\n  border-right: none;\n}\n\n.mobile-nav-footer {\n  padding: 15px 20px;\n  background-color: #263445;\n  display: flex;\n  justify-content: center;\n}\n\n.mobile-footer-controls {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n}\n\n.mobile-theme-switch {\n  margin-right: 20px;\n}\n\n@media (max-width: 768px) {\n  .mobile-nav {\n    display: block !important;\n    position: relative !important;\n    z-index: 9999 !important;\n  }\n  .mobile-nav-toggle {\n    top: 15px !important;\n    right: 15px !important;\n    left: auto;\n    transform: none !important;\n    margin-right: 0 !important;\n    position: fixed !important;\n    z-index: 9999 !important;\n  }\n}\n</style>\n"], "mappings": "AA2DA,OAAAA,gBAAA;AACA,OAAAC,WAAA;AAEA;EACAC,IAAA;EAEAC,UAAA;IACAH,gBAAA;IACAC;EACA;EAEAG,KAAA;IACA;MACAC,MAAA;IACA;EACA;EAEAC,QAAA;IACAC,YAAA;MACA,YAAAC,MAAA,CAAAC,IAAA;IACA;EACA;EAEAC,OAAA;IACAC,WAAA;MACA,KAAAN,MAAA,SAAAA,MAAA;;MAEA;MACA,SAAAA,MAAA;QACAO,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;MACA;QACAH,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;MACA;IACA;IAEAC,UAAA;MACA,KAAAX,MAAA;MACAO,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;IACA;IAEAE,aAAAC,GAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,GAAA;MACA,KAAAF,SAAA;IACA;EACA;EAEA;EACAK,KAAA;IACA,QAAAb,CAAA;MACA,KAAAQ,SAAA;IACA;EACA;EAEA;EACAM,cAAA;IACAV,QAAA,CAAAC,IAAA,CAAAC,KAAA,CAAAC,QAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}