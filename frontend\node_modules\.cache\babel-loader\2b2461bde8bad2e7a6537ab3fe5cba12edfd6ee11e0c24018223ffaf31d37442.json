{"ast": null, "code": "import { equipmentApi, reservationApi } from '@/api';\nimport axios from 'axios';\nimport { isReservationExpired } from '@/utils/date';\nexport default {\n  name: 'EquipmentDetail',\n  data() {\n    return {\n      loading: false,\n      loadingReservations: false,\n      equipment: null,\n      reservations: [],\n      dateRange: [new Date(), new Date(new Date().setDate(new Date().getDate() + 7))],\n      pickerOptions: {\n        shortcuts: [{\n          text: this.$t('common.today'),\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: this.$t('common.week'),\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);\n            picker.$emit('pick', [start, end]);\n          }\n        }, {\n          text: this.$t('common.month'),\n          onClick(picker) {\n            const end = new Date();\n            const start = new Date();\n            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);\n            picker.$emit('pick', [start, end]);\n          }\n        }]\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    };\n  },\n  computed: {\n    // 获取完整的图片URL\n    baseUrl() {\n      return axios.defaults.baseURL || 'http://localhost:8000';\n    }\n  },\n  created() {\n    this.fetchEquipment();\n  },\n  methods: {\n    async fetchEquipment() {\n      this.loading = true;\n      try {\n        const equipmentId = this.$route.params.id;\n        const response = await equipmentApi.getEquipment(equipmentId);\n        this.equipment = response.data;\n\n        // 获取预定情况\n        this.fetchReservations();\n      } catch (error) {\n        console.error('Failed to fetch equipment:', error);\n        this.$message.error(this.$t('common.error'));\n        this.equipment = null;\n      } finally {\n        this.loading = false;\n      }\n    },\n    async fetchReservations() {\n      if (!this.equipment) return;\n      this.loadingReservations = true;\n      try {\n        const equipmentId = this.equipment.id;\n        const startDate = this.formatDate(this.dateRange[0]);\n\n        // 将结束日期调整为当天的最后一秒\n        const endDateObj = new Date(this.dateRange[1]);\n        endDateObj.setHours(23, 59, 59, 999);\n        const endDate = endDateObj.toISOString();\n        const params = {\n          equipment_id: equipmentId,\n          from_date: startDate,\n          to_date: endDate\n          // 不指定status，获取所有状态的预定\n        };\n        const response = await reservationApi.getReservations(params);\n        // 按开始时间升序排序，使最近的预约显示在前面\n        this.reservations = response.data.items.sort((a, b) => {\n          const dateA = new Date(a.start_datetime);\n          const dateB = new Date(b.start_datetime);\n          return dateA - dateB;\n        });\n      } catch (error) {\n        console.error('Failed to fetch reservations:', error);\n        this.$message.error(this.$t('common.error'));\n        this.reservations = [];\n      } finally {\n        this.loadingReservations = false;\n      }\n    },\n    reserveEquipment() {\n      this.$router.push(`/equipment/${this.equipment.id}/reserve`);\n    },\n    recurringReserveEquipment() {\n      this.$router.push(`/equipment/${this.equipment.id}/recurring-reserve`);\n    },\n    formatDateTime(dateTime) {\n      if (!dateTime) return '';\n      const date = new Date(dateTime);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n    formatDate(date) {\n      if (!date) return '';\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;\n    },\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '';\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://')) {\n        return url;\n      }\n\n      // 如果是相对路径，添加基础URL\n      if (url.startsWith('/')) {\n        return this.baseUrl + url;\n      }\n\n      // 其他情况，添加基础URL和斜杠\n      return this.baseUrl + '/' + url;\n    },\n    // 获取时间线项的类型\n    getTimelineItemType(reservation) {\n      // 根据实际状态返回不同的类型\n      switch (reservation.status) {\n        case 'cancelled':\n          return 'danger';\n        // 已取消：从info改为danger(红色)\n        case 'expired':\n          return 'warning';\n        case 'in_use':\n          return 'primary';\n        // 使用中：从success改为primary(蓝色)\n        case 'confirmed':\n          return 'success';\n        // 已确认：从primary改为success(绿色)\n        default:\n          return 'success';\n      }\n    },\n    // 获取状态标签的类型\n    getStatusTagType(reservation) {\n      // 根据实际状态返回不同的类型\n      switch (reservation.status) {\n        case 'cancelled':\n          return 'danger';\n        // 已取消：从info改为danger(红色)\n        case 'expired':\n          return 'warning';\n        case 'in_use':\n          return 'primary';\n        // 使用中：从success改为primary(蓝色)\n        case 'confirmed':\n          return 'success';\n        // 已确认：从primary改为success(绿色)\n        default:\n          return 'success';\n      }\n    },\n    // 获取状态文本\n    getStatusText(reservation) {\n      // 根据实际状态返回对应的文本\n      switch (reservation.status) {\n        case 'cancelled':\n          return this.$t('reservation.cancelled');\n        case 'expired':\n          return this.$t('reservation.expired');\n        case 'in_use':\n          return this.$t('reservation.inUse');\n        case 'confirmed':\n          return this.$t('reservation.confirmed');\n        default:\n          return this.$t('reservation.confirmed');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["equipmentApi", "reservationApi", "axios", "isReservationExpired", "name", "data", "loading", "loadingReservations", "equipment", "reservations", "date<PERSON><PERSON><PERSON>", "Date", "setDate", "getDate", "pickerOptions", "shortcuts", "text", "$t", "onClick", "picker", "end", "start", "$emit", "setTime", "getTime", "isMobile", "window", "innerWidth", "computed", "baseUrl", "defaults", "baseURL", "created", "fetchEquipment", "methods", "equipmentId", "$route", "params", "id", "response", "getEquipment", "fetchReservations", "error", "console", "$message", "startDate", "formatDate", "endDateObj", "setHours", "endDate", "toISOString", "equipment_id", "from_date", "to_date", "getReservations", "items", "sort", "a", "b", "dateA", "start_datetime", "dateB", "reserveEquipment", "$router", "push", "recurringReserveEquipment", "formatDateTime", "dateTime", "date", "getFullYear", "String", "getMonth", "padStart", "getHours", "getMinutes", "getFullImageUrl", "url", "startsWith", "getTimelineItemType", "reservation", "status", "getStatusTagType", "getStatusText"], "sources": ["src/views/equipment/EquipmentDetail.vue"], "sourcesContent": ["<template>\n  <div class=\"equipment-detail\">\n    <div v-if=\"loading\" class=\"loading-container\">\n      <el-skeleton :rows=\"10\" animated />\n    </div>\n\n    <div v-else-if=\"!equipment\" class=\"error-container\">\n      <el-result\n        icon=\"error\"\n        :title=\"$t('error.errorMessage')\"\n        :sub-title=\"$t('equipment.notFound')\"\n      >\n        <template #extra>\n          <el-button type=\"primary\" @click=\"$router.push('/equipment')\">\n            {{ $t('equipment.list') }}\n          </el-button>\n        </template>\n      </el-result>\n    </div>\n\n    <div v-else>\n      <!-- 返回按钮 -->\n      <div class=\"back-link\">\n        <el-button icon=\"el-icon-arrow-left\" @click=\"$router.push('/equipment')\">\n          {{ $t('common.back') }}\n        </el-button>\n      </div>\n\n      <!-- 设备信息 -->\n      <el-row :gutter=\"20\">\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"10\" :lg=\"8\">\n          <el-card shadow=\"never\" class=\"image-card\">\n            <div class=\"equipment-image-container\">\n              <img\n                :src=\"equipment.image_path ? getFullImageUrl(equipment.image_path) : require('@/assets/upload.png')\"\n                :alt=\"equipment.name\"\n                class=\"equipment-image\"\n              />\n            </div>\n          </el-card>\n        </el-col>\n\n        <el-col :xs=\"24\" :sm=\"24\" :md=\"14\" :lg=\"16\">\n          <el-card shadow=\"never\" class=\"info-card\">\n            <div class=\"equipment-header\">\n              <h1 class=\"equipment-name\">{{ equipment.name }}</h1>\n              <div class=\"equipment-tags\">\n                <el-tag\n                  :type=\"equipment.status === 'available' ? 'success' : 'warning'\"\n                  size=\"medium\"\n                >\n                  {{ equipment.status === 'available' ? $t('equipment.available') : $t('equipment.maintenance') }}\n                </el-tag>\n\n                <!-- 可同时预定标记 -->\n                <el-tag\n                  v-if=\"equipment.allow_simultaneous\"\n                  type=\"primary\"\n                  size=\"medium\"\n                  style=\"margin-left: 8px;\"\n                >\n                  {{ $t('equipment.simultaneousReservation') }} ({{ equipment.max_simultaneous }})\n                </el-tag>\n              </div>\n            </div>\n\n            <el-divider></el-divider>\n\n            <el-descriptions :column=\"1\" border>\n              <el-descriptions-item :label=\"$t('equipment.category')\">\n                {{ equipment.category }}\n              </el-descriptions-item>\n\n              <el-descriptions-item :label=\"$t('equipment.model')\" v-if=\"equipment.model\">\n                {{ equipment.model }}\n              </el-descriptions-item>\n\n              <el-descriptions-item :label=\"$t('equipment.location')\" v-if=\"equipment.location\">\n                {{ equipment.location }}\n              </el-descriptions-item>\n\n              <el-descriptions-item :label=\"$t('equipment.description')\" v-if=\"equipment.description\">\n                {{ equipment.description }}\n              </el-descriptions-item>\n\n              <!-- 可同时预定说明 -->\n              <el-descriptions-item v-if=\"equipment.allow_simultaneous\" :label=\"$t('equipment.simultaneousReservationInfo')\">\n                <div class=\"simultaneous-info\">\n                  <i class=\"el-icon-info\"></i>\n                  {{ $t('equipment.simultaneousReservationDesc', { max: equipment.max_simultaneous }) }}\n                </div>\n              </el-descriptions-item>\n\n              <el-descriptions-item :label=\"$t('equipment.userGuide')\" v-if=\"equipment.user_guide\">\n                <div class=\"user-guide-content rich-text-content\" v-html=\"equipment.user_guide\"></div>\n              </el-descriptions-item>\n\n              <el-descriptions-item :label=\"$t('equipment.videoTutorial')\" v-if=\"equipment.video_tutorial\">\n                <div class=\"video-container\">\n                  <iframe\n                    :src=\"equipment.video_tutorial\"\n                    frameborder=\"0\"\n                    allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n                    allowfullscreen\n                  ></iframe>\n                </div>\n              </el-descriptions-item>\n            </el-descriptions>\n\n            <div class=\"action-buttons\">\n              <el-button\n                type=\"primary\"\n                size=\"large\"\n                @click=\"reserveEquipment\"\n                :disabled=\"equipment.status !== 'available'\"\n              >\n                {{ $t('equipment.reserve') }}\n              </el-button>\n\n              <el-button\n                type=\"success\"\n                size=\"large\"\n                @click=\"recurringReserveEquipment\"\n                :disabled=\"equipment.status !== 'available'\"\n              >\n                {{ $t('reservation.createRecurringReservation') }}\n              </el-button>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n\n      <!-- 预定情况 -->\n      <el-card shadow=\"never\" class=\"reservations-card\">\n        <div slot=\"header\" class=\"reservations-header\">\n          <span>{{ $t('equipment.currentReservations') }}</span>\n          <el-date-picker\n            v-model=\"dateRange\"\n            type=\"daterange\"\n            range-separator=\"→\"\n            :start-placeholder=\"$t('reservation.startTime')\"\n            :end-placeholder=\"$t('reservation.endTime')\"\n            :picker-options=\"pickerOptions\"\n            @change=\"fetchReservations\"\n            size=\"small\"\n          ></el-date-picker>\n        </div>\n\n        <div v-if=\"loadingReservations\" class=\"loading-container\">\n          <el-skeleton :rows=\"3\" animated />\n        </div>\n\n        <div v-else-if=\"reservations.length === 0\" class=\"empty-reservations\">\n          <el-empty :description=\"$t('equipment.noReservations')\"></el-empty>\n        </div>\n\n        <div v-else class=\"reservations-list\">\n          <!-- 移动端卡片视图 -->\n          <div v-if=\"isMobile\" class=\"mobile-reservations-container\">\n            <div\n              v-for=\"reservation in reservations\"\n              :key=\"reservation.id\"\n              class=\"reservation-mobile-card\"\n            >\n              <div class=\"card-header\">\n                <div class=\"reservation-info\">\n                  <div class=\"reservation-id\">ID: {{ reservation.id }}</div>\n                  <div class=\"reservation-user\">{{ reservation.user_name }}</div>\n                </div>\n                <el-tag\n                  :type=\"getStatusType(reservation)\"\n                  size=\"medium\"\n                >\n                  {{ getStatusText(reservation) }}\n                </el-tag>\n              </div>\n\n              <div class=\"card-content\">\n                <div class=\"info-row\">\n                  <span class=\"label\">部门:</span>\n                  <span class=\"value\">{{ reservation.user_department }}</span>\n                </div>\n                <div class=\"info-row\">\n                  <span class=\"label\">开始时间:</span>\n                  <span class=\"value\">{{ formatDateTime(reservation.start_datetime) }}</span>\n                </div>\n                <div class=\"info-row\">\n                  <span class=\"label\">结束时间:</span>\n                  <span class=\"value\">{{ formatDateTime(reservation.end_datetime) }}</span>\n                </div>\n                <div class=\"info-row\" v-if=\"reservation.purpose\">\n                  <span class=\"label\">用途:</span>\n                  <span class=\"value\">{{ reservation.purpose }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          <!-- 桌面端时间线视图 -->\n          <el-timeline v-else>\n            <el-timeline-item\n              v-for=\"reservation in reservations\"\n              :key=\"reservation.id\"\n              :timestamp=\"formatDateTime(reservation.start_datetime) + ' → ' + formatDateTime(reservation.end_datetime)\"\n              :type=\"getTimelineItemType(reservation)\"\n            >\n              <el-card class=\"reservation-card\">\n                <div class=\"reservation-info\">\n                  <div class=\"reservation-user\">\n                    <span class=\"user-name\">{{ reservation.user_name }}</span>\n                    <span class=\"user-department\">{{ reservation.user_department }}</span>\n                  </div>\n\n                  <div class=\"reservation-purpose\" v-if=\"reservation.purpose\">\n                    <strong>{{ $t('reservation.purpose') }}:</strong> {{ reservation.purpose }}\n                  </div>\n\n                  <div class=\"reservation-status\">\n                    <el-tag\n                      size=\"small\"\n                      :type=\"getStatusTagType(reservation)\"\n                    >\n                      {{ getStatusText(reservation) }}\n                    </el-tag>\n                  </div>\n                </div>\n              </el-card>\n            </el-timeline-item>\n          </el-timeline>\n        </div>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { equipmentApi, reservationApi } from '@/api'\nimport axios from 'axios'\nimport { isReservationExpired } from '@/utils/date'\n\nexport default {\n  name: 'EquipmentDetail',\n\n  data() {\n    return {\n      loading: false,\n      loadingReservations: false,\n      equipment: null,\n      reservations: [],\n      dateRange: [\n        new Date(),\n        new Date(new Date().setDate(new Date().getDate() + 7))\n      ],\n      pickerOptions: {\n        shortcuts: [\n          {\n            text: this.$t('common.today'),\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              picker.$emit('pick', [start, end])\n            }\n          },\n          {\n            text: this.$t('common.week'),\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)\n              picker.$emit('pick', [start, end])\n            }\n          },\n          {\n            text: this.$t('common.month'),\n            onClick(picker) {\n              const end = new Date()\n              const start = new Date()\n              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)\n              picker.$emit('pick', [start, end])\n            }\n          }\n        ]\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    }\n  },\n\n  computed: {\n    // 获取完整的图片URL\n    baseUrl() {\n      return axios.defaults.baseURL || 'http://localhost:8000';\n    }\n  },\n\n  created() {\n    this.fetchEquipment()\n  },\n\n  methods: {\n    async fetchEquipment() {\n      this.loading = true\n      try {\n        const equipmentId = this.$route.params.id\n        const response = await equipmentApi.getEquipment(equipmentId)\n        this.equipment = response.data\n\n        // 获取预定情况\n        this.fetchReservations()\n      } catch (error) {\n        console.error('Failed to fetch equipment:', error)\n        this.$message.error(this.$t('common.error'))\n        this.equipment = null\n      } finally {\n        this.loading = false\n      }\n    },\n\n    async fetchReservations() {\n      if (!this.equipment) return\n\n      this.loadingReservations = true\n      try {\n        const equipmentId = this.equipment.id\n        const startDate = this.formatDate(this.dateRange[0])\n\n        // 将结束日期调整为当天的最后一秒\n        const endDateObj = new Date(this.dateRange[1])\n        endDateObj.setHours(23, 59, 59, 999)\n        const endDate = endDateObj.toISOString()\n\n        const params = {\n          equipment_id: equipmentId,\n          from_date: startDate,\n          to_date: endDate\n          // 不指定status，获取所有状态的预定\n        }\n\n        const response = await reservationApi.getReservations(params)\n        // 按开始时间升序排序，使最近的预约显示在前面\n        this.reservations = response.data.items.sort((a, b) => {\n          const dateA = new Date(a.start_datetime)\n          const dateB = new Date(b.start_datetime)\n          return dateA - dateB\n        })\n      } catch (error) {\n        console.error('Failed to fetch reservations:', error)\n        this.$message.error(this.$t('common.error'))\n        this.reservations = []\n      } finally {\n        this.loadingReservations = false\n      }\n    },\n\n    reserveEquipment() {\n      this.$router.push(`/equipment/${this.equipment.id}/reserve`)\n    },\n\n    recurringReserveEquipment() {\n      this.$router.push(`/equipment/${this.equipment.id}/recurring-reserve`)\n    },\n\n    formatDateTime(dateTime) {\n      if (!dateTime) return ''\n\n      const date = new Date(dateTime)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n    },\n\n    formatDate(date) {\n      if (!date) return ''\n\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`\n    },\n\n    // 获取完整的图片URL\n    getFullImageUrl(url) {\n      if (!url) return '';\n\n      // 如果已经是完整URL，直接返回\n      if (url.startsWith('http://') || url.startsWith('https://')) {\n        return url;\n      }\n\n      // 如果是相对路径，添加基础URL\n      if (url.startsWith('/')) {\n        return this.baseUrl + url;\n      }\n\n      // 其他情况，添加基础URL和斜杠\n      return this.baseUrl + '/' + url;\n    },\n\n    // 获取时间线项的类型\n    getTimelineItemType(reservation) {\n      // 根据实际状态返回不同的类型\n      switch(reservation.status) {\n        case 'cancelled':\n          return 'danger'  // 已取消：从info改为danger(红色)\n        case 'expired':\n          return 'warning'\n        case 'in_use':\n          return 'primary' // 使用中：从success改为primary(蓝色)\n        case 'confirmed':\n          return 'success' // 已确认：从primary改为success(绿色)\n        default:\n          return 'success'\n      }\n    },\n\n    // 获取状态标签的类型\n    getStatusTagType(reservation) {\n      // 根据实际状态返回不同的类型\n      switch(reservation.status) {\n        case 'cancelled':\n          return 'danger'  // 已取消：从info改为danger(红色)\n        case 'expired':\n          return 'warning'\n        case 'in_use':\n          return 'primary' // 使用中：从success改为primary(蓝色)\n        case 'confirmed':\n          return 'success' // 已确认：从primary改为success(绿色)\n        default:\n          return 'success'\n      }\n    },\n\n    // 获取状态文本\n    getStatusText(reservation) {\n      // 根据实际状态返回对应的文本\n      switch(reservation.status) {\n        case 'cancelled':\n          return this.$t('reservation.cancelled')\n        case 'expired':\n          return this.$t('reservation.expired')\n        case 'in_use':\n          return this.$t('reservation.inUse')\n        case 'confirmed':\n          return this.$t('reservation.confirmed')\n        default:\n          return this.$t('reservation.confirmed')\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.equipment-detail {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.back-link {\n  margin-bottom: 20px;\n}\n\n.image-card {\n  margin-bottom: 20px;\n}\n\n.equipment-image-container {\n  height: 300px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  overflow: hidden;\n  background-color: #f5f7fa;\n}\n\n.equipment-image {\n  max-height: 100%;\n  max-width: 100%;\n  object-fit: contain;\n}\n\n.info-card {\n  margin-bottom: 20px;\n}\n\n.equipment-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.equipment-name {\n  margin: 0;\n  font-size: 24px;\n  color: #303133;\n}\n\n.action-buttons {\n  margin-top: 20px;\n  text-align: center;\n}\n\n.reservations-card {\n  margin-bottom: 20px;\n}\n\n.reservations-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.empty-reservations {\n  padding: 20px 0;\n  text-align: center;\n}\n\n.reservation-card {\n  margin-bottom: 10px;\n}\n\n.reservation-info {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.reservation-user {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.user-name {\n  font-weight: bold;\n  color: #303133;\n}\n\n.user-department {\n  color: #909399;\n  font-size: 13px;\n}\n\n.reservation-purpose {\n  color: #606266;\n  font-size: 14px;\n}\n\n.user-guide-content {\n  line-height: 1.6;\n}\n\n.rich-text-content {\n  max-width: 100%;\n  overflow-x: auto;\n}\n\n.rich-text-content img {\n  max-width: 100%;\n  height: auto;\n}\n\n.rich-text-content table {\n  border-collapse: collapse;\n  width: 100%;\n  margin-bottom: 1rem;\n}\n\n.rich-text-content table td,\n.rich-text-content table th {\n  border: 1px solid #ddd;\n  padding: 8px;\n}\n\n.rich-text-content ul,\n.rich-text-content ol {\n  padding-left: 2em;\n}\n\n.video-container {\n  position: relative;\n  padding-bottom: 56.25%; /* 16:9 */\n  height: 0;\n  overflow: hidden;\n  max-width: 100%;\n}\n\n.video-container iframe {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.loading-container {\n  padding: 40px 0;\n}\n\n.error-container {\n  padding: 40px 0;\n  text-align: center;\n}\n\n.reservations-status {\n  margin-top: 20px;\n}\n\n.equipment-tags {\n  display: flex;\n  align-items: center;\n}\n\n.simultaneous-info {\n  color: #409EFF;\n  font-size: 14px;\n  line-height: 1.5;\n  margin: 5px 0;\n}\n\n.simultaneous-info i {\n  margin-right: 5px;\n}\n\n@media (max-width: 768px) {\n  .reservations-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n\n  .equipment-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 10px;\n  }\n}\n</style>\n"], "mappings": "AA4OA,SAAAA,YAAA,EAAAC,cAAA;AACA,OAAAC,KAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,mBAAA;MACAC,SAAA;MACAC,YAAA;MACAC,SAAA,GACA,IAAAC,IAAA,IACA,IAAAA,IAAA,KAAAA,IAAA,GAAAC,OAAA,KAAAD,IAAA,GAAAE,OAAA,SACA;MACAC,aAAA;QACAC,SAAA,GACA;UACAC,IAAA,OAAAC,EAAA;UACAC,QAAAC,MAAA;YACA,MAAAC,GAAA,OAAAT,IAAA;YACA,MAAAU,KAAA,OAAAV,IAAA;YACAQ,MAAA,CAAAG,KAAA,UAAAD,KAAA,EAAAD,GAAA;UACA;QACA,GACA;UACAJ,IAAA,OAAAC,EAAA;UACAC,QAAAC,MAAA;YACA,MAAAC,GAAA,OAAAT,IAAA;YACA,MAAAU,KAAA,OAAAV,IAAA;YACAU,KAAA,CAAAE,OAAA,CAAAF,KAAA,CAAAG,OAAA;YACAL,MAAA,CAAAG,KAAA,UAAAD,KAAA,EAAAD,GAAA;UACA;QACA,GACA;UACAJ,IAAA,OAAAC,EAAA;UACAC,QAAAC,MAAA;YACA,MAAAC,GAAA,OAAAT,IAAA;YACA,MAAAU,KAAA,OAAAV,IAAA;YACAU,KAAA,CAAAE,OAAA,CAAAF,KAAA,CAAAG,OAAA;YACAL,MAAA,CAAAG,KAAA,UAAAD,KAAA,EAAAD,GAAA;UACA;QACA;MAEA;MACA;MACAK,QAAA,EAAAC,MAAA,CAAAC,UAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,QAAA;MACA,OAAA3B,KAAA,CAAA4B,QAAA,CAAAC,OAAA;IACA;EACA;EAEAC,QAAA;IACA,KAAAC,cAAA;EACA;EAEAC,OAAA;IACA,MAAAD,eAAA;MACA,KAAA3B,OAAA;MACA;QACA,MAAA6B,WAAA,QAAAC,MAAA,CAAAC,MAAA,CAAAC,EAAA;QACA,MAAAC,QAAA,SAAAvC,YAAA,CAAAwC,YAAA,CAAAL,WAAA;QACA,KAAA3B,SAAA,GAAA+B,QAAA,CAAAlC,IAAA;;QAEA;QACA,KAAAoC,iBAAA;MACA,SAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,+BAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA,MAAAzB,EAAA;QACA,KAAAT,SAAA;MACA;QACA,KAAAF,OAAA;MACA;IACA;IAEA,MAAAmC,kBAAA;MACA,UAAAjC,SAAA;MAEA,KAAAD,mBAAA;MACA;QACA,MAAA4B,WAAA,QAAA3B,SAAA,CAAA8B,EAAA;QACA,MAAAO,SAAA,QAAAC,UAAA,MAAApC,SAAA;;QAEA;QACA,MAAAqC,UAAA,OAAApC,IAAA,MAAAD,SAAA;QACAqC,UAAA,CAAAC,QAAA;QACA,MAAAC,OAAA,GAAAF,UAAA,CAAAG,WAAA;QAEA,MAAAb,MAAA;UACAc,YAAA,EAAAhB,WAAA;UACAiB,SAAA,EAAAP,SAAA;UACAQ,OAAA,EAAAJ;UACA;QACA;QAEA,MAAAV,QAAA,SAAAtC,cAAA,CAAAqD,eAAA,CAAAjB,MAAA;QACA;QACA,KAAA5B,YAAA,GAAA8B,QAAA,CAAAlC,IAAA,CAAAkD,KAAA,CAAAC,IAAA,EAAAC,CAAA,EAAAC,CAAA;UACA,MAAAC,KAAA,OAAAhD,IAAA,CAAA8C,CAAA,CAAAG,cAAA;UACA,MAAAC,KAAA,OAAAlD,IAAA,CAAA+C,CAAA,CAAAE,cAAA;UACA,OAAAD,KAAA,GAAAE,KAAA;QACA;MACA,SAAAnB,KAAA;QACAC,OAAA,CAAAD,KAAA,kCAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA,MAAAzB,EAAA;QACA,KAAAR,YAAA;MACA;QACA,KAAAF,mBAAA;MACA;IACA;IAEAuD,iBAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,oBAAAxD,SAAA,CAAA8B,EAAA;IACA;IAEA2B,0BAAA;MACA,KAAAF,OAAA,CAAAC,IAAA,oBAAAxD,SAAA,CAAA8B,EAAA;IACA;IAEA4B,eAAAC,QAAA;MACA,KAAAA,QAAA;MAEA,MAAAC,IAAA,OAAAzD,IAAA,CAAAwD,QAAA;MACA,UAAAC,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAvD,OAAA,IAAA2D,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAK,QAAA,IAAAD,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAM,UAAA,IAAAF,QAAA;IACA;IAEA1B,WAAAsB,IAAA;MACA,KAAAA,IAAA;MAEA,UAAAA,IAAA,CAAAC,WAAA,MAAAC,MAAA,CAAAF,IAAA,CAAAG,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAF,IAAA,CAAAvD,OAAA,IAAA2D,QAAA;IACA;IAEA;IACAG,gBAAAC,GAAA;MACA,KAAAA,GAAA;;MAEA;MACA,IAAAA,GAAA,CAAAC,UAAA,eAAAD,GAAA,CAAAC,UAAA;QACA,OAAAD,GAAA;MACA;;MAEA;MACA,IAAAA,GAAA,CAAAC,UAAA;QACA,YAAAhD,OAAA,GAAA+C,GAAA;MACA;;MAEA;MACA,YAAA/C,OAAA,SAAA+C,GAAA;IACA;IAEA;IACAE,oBAAAC,WAAA;MACA;MACA,QAAAA,WAAA,CAAAC,MAAA;QACA;UACA;QAAA;QACA;UACA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;MACA;IACA;IAEA;IACAC,iBAAAF,WAAA;MACA;MACA,QAAAA,WAAA,CAAAC,MAAA;QACA;UACA;QAAA;QACA;UACA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;MACA;IACA;IAEA;IACAE,cAAAH,WAAA;MACA;MACA,QAAAA,WAAA,CAAAC,MAAA;QACA;UACA,YAAA/D,EAAA;QACA;UACA,YAAAA,EAAA;QACA;UACA,YAAAA,EAAA;QACA;UACA,YAAAA,EAAA;QACA;UACA,YAAAA,EAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}