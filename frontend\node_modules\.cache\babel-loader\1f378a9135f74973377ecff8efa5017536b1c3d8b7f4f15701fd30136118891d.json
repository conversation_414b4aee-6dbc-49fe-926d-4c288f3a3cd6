{"ast": null, "code": "import axios from 'axios';\nexport default {\n  name: 'EmailSettings',\n  data() {\n    return {\n      emailSettings: {\n        smtp_server: '',\n        smtp_port: 587,\n        sender_email: '',\n        sender_name: '',\n        smtp_username: '',\n        smtp_password: '',\n        cc_list: '',\n        bcc_list: '',\n        use_ssl: true,\n        enabled: false\n      },\n      testEmail: '',\n      testLoading: false,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    };\n  },\n  created() {\n    this.fetchEmailSettings();\n  },\n  methods: {\n    async fetchEmailSettings() {\n      try {\n        const res = await axios.get('/api/admin/email/settings');\n        this.emailSettings = res.data;\n      } catch (e) {\n        this.$message.error('获取邮件设置失败');\n      }\n    },\n    async saveEmailSettings() {\n      try {\n        await axios.post('/api/admin/email/settings', this.emailSettings);\n        this.$message.success('保存成功');\n      } catch (e) {\n        this.$message.error('保存失败');\n      }\n    },\n    async testEmailSend() {\n      if (!this.testEmail) {\n        this.$message.warning('请输入测试收件人邮箱');\n        return;\n      }\n      this.testLoading = true;\n      try {\n        const payload = {\n          ...this.emailSettings,\n          to_email: this.testEmail\n        };\n        const res = await axios.post('/api/admin/email/test', payload);\n        if (res.data.success) {\n          this.$message.success(res.data.message);\n        } else {\n          this.$message.error(res.data.message);\n        }\n      } catch (e) {\n        this.$message.error('请求失败');\n      } finally {\n        this.testLoading = false;\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "name", "data", "emailSettings", "smtp_server", "smtp_port", "sender_email", "sender_name", "smtp_username", "smtp_password", "cc_list", "bcc_list", "use_ssl", "enabled", "testEmail", "testLoading", "isMobile", "window", "innerWidth", "created", "fetchEmailSettings", "methods", "res", "get", "e", "$message", "error", "saveEmailSettings", "post", "success", "testEmailSend", "warning", "payload", "to_email", "message"], "sources": ["src/views/admin/EmailSettings.vue"], "sourcesContent": ["<template>\r\n  <div class=\"email-settings\">\r\n    <!-- 移动端提示 -->\r\n    <div v-if=\"isMobile\" class=\"mobile-notice\">\r\n      <el-card shadow=\"hover\">\r\n        <div style=\"text-align: center; padding: 40px 20px;\">\r\n          <i class=\"el-icon-setting\" style=\"font-size: 48px; color: #409EFF; margin-bottom: 20px;\"></i>\r\n          <h3>邮件设置</h3>\r\n          <p style=\"color: #666; margin: 20px 0;\">\r\n            邮件设置功能需要在桌面端使用，以获得更好的表单填写体验。\r\n          </p>\r\n          <p style=\"color: #666; font-size: 14px;\">\r\n            请使用电脑或平板设备访问此功能。\r\n          </p>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 桌面端完整功能 -->\r\n    <el-form v-else :model=\"emailSettings\" label-width=\"120px\" @submit.native.prevent=\"saveEmailSettings\">\r\n      <el-form-item label=\"SMTP服务器\">\r\n        <el-input v-model=\"emailSettings.smtp_server\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"端口\">\r\n        <el-input v-model=\"emailSettings.smtp_port\" type=\"number\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发件人邮箱\">\r\n        <el-input v-model=\"emailSettings.sender_email\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"发件人名称\">\r\n        <el-input v-model=\"emailSettings.sender_name\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"SMTP用户名\">\r\n        <el-input v-model=\"emailSettings.smtp_username\"></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"SMTP密码\">\r\n        <el-input v-model=\"emailSettings.smtp_password\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"抄送人列表\">\r\n        <el-input\r\n          v-model=\"emailSettings.cc_list\"\r\n          type=\"textarea\"\r\n          :rows=\"2\"\r\n          placeholder=\"多个邮箱请用逗号分隔，例如：<EMAIL>, <EMAIL>\"\r\n        ></el-input>\r\n        <div class=\"form-tip\">多个邮箱请用逗号分隔，所有发出的邮件都会抄送给这些邮箱</div>\r\n      </el-form-item>\r\n      <el-form-item label=\"密送人列表\">\r\n        <el-input\r\n          v-model=\"emailSettings.bcc_list\"\r\n          type=\"textarea\"\r\n          :rows=\"2\"\r\n          placeholder=\"多个邮箱请用逗号分隔，例如：<EMAIL>, <EMAIL>\"\r\n        ></el-input>\r\n        <div class=\"form-tip\">多个邮箱请用逗号分隔，所有发出的邮件都会密送给这些邮箱</div>\r\n      </el-form-item>\r\n      <el-form-item label=\"使用SSL\">\r\n        <el-switch v-model=\"emailSettings.use_ssl\"></el-switch>\r\n      </el-form-item>\r\n      <el-form-item label=\"启用邮件功能\">\r\n        <el-switch v-model=\"emailSettings.enabled\"></el-switch>\r\n      </el-form-item>\r\n      <el-form-item label=\"测试收件人邮箱\">\r\n        <el-input v-model=\"testEmail\"></el-input>\r\n        <el-button type=\"primary\" @click=\"testEmailSend\" :loading=\"testLoading\" style=\"margin-left:10px;\">测试邮件</el-button>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"saveEmailSettings\">保存</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EmailSettings',\r\n  data() {\r\n    return {\r\n      emailSettings: {\r\n        smtp_server: '',\r\n        smtp_port: 587,\r\n        sender_email: '',\r\n        sender_name: '',\r\n        smtp_username: '',\r\n        smtp_password: '',\r\n        cc_list: '',\r\n        bcc_list: '',\r\n        use_ssl: true,\r\n        enabled: false\r\n      },\r\n      testEmail: '',\r\n      testLoading: false,\r\n      // 响应式布局相关\r\n      isMobile: window.innerWidth <= 768\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchEmailSettings()\r\n  },\r\n  methods: {\r\n    async fetchEmailSettings() {\r\n      try {\r\n        const res = await axios.get('/api/admin/email/settings')\r\n        this.emailSettings = res.data\r\n      } catch (e) {\r\n        this.$message.error('获取邮件设置失败')\r\n      }\r\n    },\r\n    async saveEmailSettings() {\r\n      try {\r\n        await axios.post('/api/admin/email/settings', this.emailSettings)\r\n        this.$message.success('保存成功')\r\n      } catch (e) {\r\n        this.$message.error('保存失败')\r\n      }\r\n    },\r\n    async testEmailSend() {\r\n      if (!this.testEmail) {\r\n        this.$message.warning('请输入测试收件人邮箱')\r\n        return\r\n      }\r\n      this.testLoading = true\r\n      try {\r\n        const payload = {\r\n          ...this.emailSettings,\r\n          to_email: this.testEmail\r\n        }\r\n        const res = await axios.post('/api/admin/email/test', payload)\r\n        if (res.data.success) {\r\n          this.$message.success(res.data.message)\r\n        } else {\r\n          this.$message.error(res.data.message)\r\n        }\r\n      } catch (e) {\r\n        this.$message.error('请求失败')\r\n      } finally {\r\n        this.testLoading = false\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.email-settings {\r\n  padding: 20px;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  line-height: 1.2;\r\n  padding-top: 4px;\r\n}\r\n</style>"], "mappings": "AA0EA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;QACAC,WAAA;QACAC,SAAA;QACAC,YAAA;QACAC,WAAA;QACAC,aAAA;QACAC,aAAA;QACAC,OAAA;QACAC,QAAA;QACAC,OAAA;QACAC,OAAA;MACA;MACAC,SAAA;MACAC,WAAA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,kBAAA;EACA;EACAC,OAAA;IACA,MAAAD,mBAAA;MACA;QACA,MAAAE,GAAA,SAAAtB,KAAA,CAAAuB,GAAA;QACA,KAAApB,aAAA,GAAAmB,GAAA,CAAApB,IAAA;MACA,SAAAsB,CAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IACA,MAAAC,kBAAA;MACA;QACA,MAAA3B,KAAA,CAAA4B,IAAA,mCAAAzB,aAAA;QACA,KAAAsB,QAAA,CAAAI,OAAA;MACA,SAAAL,CAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IACA,MAAAI,cAAA;MACA,UAAAhB,SAAA;QACA,KAAAW,QAAA,CAAAM,OAAA;QACA;MACA;MACA,KAAAhB,WAAA;MACA;QACA,MAAAiB,OAAA;UACA,QAAA7B,aAAA;UACA8B,QAAA,OAAAnB;QACA;QACA,MAAAQ,GAAA,SAAAtB,KAAA,CAAA4B,IAAA,0BAAAI,OAAA;QACA,IAAAV,GAAA,CAAApB,IAAA,CAAA2B,OAAA;UACA,KAAAJ,QAAA,CAAAI,OAAA,CAAAP,GAAA,CAAApB,IAAA,CAAAgC,OAAA;QACA;UACA,KAAAT,QAAA,CAAAC,KAAA,CAAAJ,GAAA,CAAApB,IAAA,CAAAgC,OAAA;QACA;MACA,SAAAV,CAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;QACA,KAAAX,WAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}