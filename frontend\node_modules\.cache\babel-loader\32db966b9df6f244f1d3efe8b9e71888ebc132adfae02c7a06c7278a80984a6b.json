{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    class: {\n      \"admin-route\": _vm.isAdminRoute,\n      \"non-admin-route\": !_vm.isAdminRoute\n    },\n    attrs: {\n      id: \"app\"\n    }\n  }, [_vm.announcements.length && !_vm.isAdminRoute ? _c(\"announcement-bar\", {\n    staticClass: \"announcement-fixed\"\n  }) : _vm._e(), _c(\"el-container\", [!_vm.isAdminRoute ? _c(\"el-header\", [_c(\"app-header\")], 1) : _vm._e(), _c(\"el-main\", [_c(\"router-view\")], 1), !_vm.isAdminRoute ? _c(\"el-footer\", {\n    class: {\n      \"admin-route-hidden\": _vm.isAdminRoute\n    }\n  }, [_c(\"app-footer\")], 1) : _vm._e()], 1)], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "class", "isAdminRoute", "attrs", "id", "announcements", "length", "staticClass", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/Equipment-Reservation-System-main/frontend/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    {\n      class: {\n        \"admin-route\": _vm.isAdminRoute,\n        \"non-admin-route\": !_vm.isAdminRoute,\n      },\n      attrs: { id: \"app\" },\n    },\n    [\n      _vm.announcements.length && !_vm.isAdminRoute\n        ? _c(\"announcement-bar\", { staticClass: \"announcement-fixed\" })\n        : _vm._e(),\n      _c(\n        \"el-container\",\n        [\n          !_vm.isAdminRoute ? _c(\"el-header\", [_c(\"app-header\")], 1) : _vm._e(),\n          _c(\"el-main\", [_c(\"router-view\")], 1),\n          !_vm.isAdminRoute\n            ? _c(\n                \"el-footer\",\n                { class: { \"admin-route-hidden\": _vm.isAdminRoute } },\n                [_c(\"app-footer\")],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IACEE,KAAK,EAAE;MACL,aAAa,EAAEH,GAAG,CAACI,YAAY;MAC/B,iBAAiB,EAAE,CAACJ,GAAG,CAACI;IAC1B,CAAC;IACDC,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAM;EACrB,CAAC,EACD,CACEN,GAAG,CAACO,aAAa,CAACC,MAAM,IAAI,CAACR,GAAG,CAACI,YAAY,GACzCH,EAAE,CAAC,kBAAkB,EAAE;IAAEQ,WAAW,EAAE;EAAqB,CAAC,CAAC,GAC7DT,GAAG,CAACU,EAAE,CAAC,CAAC,EACZT,EAAE,CACA,cAAc,EACd,CACE,CAACD,GAAG,CAACI,YAAY,GAAGH,EAAE,CAAC,WAAW,EAAE,CAACA,EAAE,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGD,GAAG,CAACU,EAAE,CAAC,CAAC,EACrET,EAAE,CAAC,SAAS,EAAE,CAACA,EAAE,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,EACrC,CAACD,GAAG,CAACI,YAAY,GACbH,EAAE,CACA,WAAW,EACX;IAAEE,KAAK,EAAE;MAAE,oBAAoB,EAAEH,GAAG,CAACI;IAAa;EAAE,CAAC,EACrD,CAACH,EAAE,CAAC,YAAY,CAAC,CAAC,EAClB,CACF,CAAC,GACDD,GAAG,CAACU,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBZ,MAAM,CAACa,aAAa,GAAG,IAAI;AAE3B,SAASb,MAAM,EAAEY,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}