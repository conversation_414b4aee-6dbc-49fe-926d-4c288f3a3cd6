{"ast": null, "code": "export default {\n  // Common\n  common: {\n    appName: 'Equipment Reservation System',\n    fullAppName: 'HTNIA Equipment Reservation System',\n    loading: 'Loading...',\n    success: 'Success',\n    downloadSuccess: 'Download successful',\n    error: 'Error',\n    warning: 'Warning',\n    info: 'Information',\n    confirm: 'Confirm',\n    cancel: 'Cancel',\n    save: 'Save',\n    edit: 'Edit',\n    delete: 'Delete',\n    search: 'Search',\n    reset: 'Reset',\n    submit: 'Submit',\n    back: 'Back',\n    more: 'More',\n    all: 'All',\n    yes: 'Yes',\n    no: 'No',\n    noData: 'No Data',\n    operation: 'Operation',\n    actions: 'Actions',\n    status: 'Status',\n    createTime: 'Create Time',\n    updateTime: 'Update Time',\n    userInfo: 'User Information',\n    instructions: 'Instructions',\n    today: 'Today',\n    week: 'Week',\n    month: 'Month',\n    notProvided: 'Not Provided',\n    id: 'ID',\n    lengthLimit: 'Length Limit',\n    view: 'View',\n    toggleTheme: 'Toggle Dark/Light Theme',\n    darkMode: 'Dark Mode',\n    lightMode: 'Light Mode',\n    useBrowserLanguage: 'Use Browser Language',\n    total: 'Total',\n    items: 'items'\n  },\n  // Navigation\n  nav: {\n    home: 'Home',\n    equipment: 'Equipment List',\n    reservation: 'Reservation Management',\n    query: 'Query Reservation',\n    admin: 'Admin Console',\n    login: 'Login',\n    logout: 'Logout',\n    calendar: 'Calendar View'\n  },\n  // Home\n  home: {\n    welcome: 'Welcome to Equipment Reservation System',\n    description: 'This is a simple and easy-to-use equipment reservation system where you can reserve various equipment and places from the school.',\n    reserveEquipment: 'Reserve Equipment',\n    queryReservation: 'Query Reservation',\n    instructions: 'Instructions',\n    step1: 'Browse available equipment and select the one you need',\n    step2: 'Choose reservation time slot',\n    step3: 'Fill in your personal information',\n    step4: 'Submit reservation request',\n    step5: 'Record the reservation code for future query or cancellation',\n    contactInfo: 'If you have any questions, please contact the IT department.',\n    viewReservations: 'View Equipment',\n    viewReservationsDesc: 'View equipment reservation status to help you check and plan your reservations.',\n    browseEquipment: 'Browse Equipment',\n    myReservations: 'My Reservations',\n    myReservationsDesc: 'Create new reservations, query and manage your reservation records, modify or cancel existing reservations.',\n    manageReservations: 'Manage Reservations',\n    calendarView: 'Calendar View',\n    calendarViewDesc: 'View equipment reservations through an intuitive calendar interface to better plan your reservation time.',\n    viewCalendar: 'View Calendar',\n    publicQuery: 'Public Reservation Query',\n    queryResults: 'Query Results',\n    noRecordsFound: 'No matching reservation records found'\n  },\n  // Equipment\n  equipment: {\n    description: 'Browse and reserve various equipment available in the system.',\n    list: 'Equipment List',\n    detail: 'Equipment Detail',\n    name: 'Equipment Name',\n    category: 'Category',\n    model: 'Model',\n    location: 'Location',\n    status: 'Status',\n    description: 'Description',\n    userGuide: 'User Guide',\n    userGuidePlaceholder: 'Please enter detailed usage steps, precautions, etc.',\n    videoTutorial: 'Video Tutorial',\n    image: 'Equipment Image',\n    videoTutorialPlaceholder: 'Please enter video link, supports YouTube, Bilibili, etc.',\n    available: 'Available',\n    maintenance: 'Maintenance',\n    inUse: 'In Use',\n    reserve: 'Reserve This Equipment',\n    viewDetail: 'View Details',\n    noDescription: 'No Description',\n    filter: 'Filter',\n    allCategories: 'All Categories',\n    allStatus: 'All Status',\n    searchPlaceholder: 'Search for equipment name, model or location',\n    currentReservations: 'Current Reservations',\n    noReservations: 'No reservation records',\n    equipmentInfo: 'Equipment Information',\n    simultaneousReservation: 'Simultaneous Reservation',\n    simultaneousReservationInfo: 'Simultaneous Reservation Info',\n    simultaneousReservationDesc: 'This equipment allows up to {max} people to reserve simultaneously. You can make a reservation even during time slots that already have reservations.'\n  },\n  // Reservation\n  reservation: {\n    cancelConfirmation: 'Warning',\n    cancelConfirmationMessage: 'Confirm to cancel reservation?',\n    description: 'Create, manage and track your equipment reservations easily.',\n    queryDescription: 'Find your reservation details using reservation code or contact information.',\n    form: 'Reservation Form',\n    recurringForm: 'Recurring Reservation',\n    detail: 'Reservation Detail',\n    recurringDetail: 'Recurring Reservation Detail',\n    query: 'Query Reservation',\n    publicQuery: 'Public Query',\n    personalManagement: 'Personal Reservation Management',\n    queryResults: 'Query Results',\n    noReservationsFound: 'No Reservations Found',\n    noRecordsFound: 'No matching reservation records found',\n    tryDifferentCriteria: 'Please try different query criteria',\n    dateRange: 'Date Range',\n    startDate: 'Start Date',\n    endDate: 'End Date',\n    selectDateRange: 'Select Date Range',\n    checkCodeAndContact: 'Please check your reservation code and contact information',\n    queryInstructions: 'Query your reservation information using reservation code or contact:',\n    queryInstruction1: 'Enter your reservation code or contact used when reserving',\n    queryInstruction2: 'Click \"Query\" button to view reservation details',\n    queryInstruction3: 'You can view, modify or cancel your reservation on the detail page',\n    code: 'Reservation Code',\n    equipmentName: 'Equipment Name',\n    userName: 'User Name',\n    userDepartment: 'User Department',\n    userContact: 'Contact',\n    userEmail: 'Email (Required)',\n    startTime: 'Start Time',\n    endTime: 'End Time',\n    purpose: 'Purpose (Optional)',\n    status: 'Status',\n    reservationInfo: 'Reservation Information',\n    userInfo: 'User Information',\n    number: 'Reservation Number',\n    active: 'Active',\n    ongoing: 'In Progress',\n    confirmed: 'Confirmed',\n    cancelled: 'Cancelled',\n    expired: 'Expired',\n    inUse: 'In Use',\n    statusConfirmed: 'Confirmed',\n    statusCancelled: 'Cancelled',\n    statusExpired: 'Expired',\n    statusInUse: 'In Use',\n    reserver: 'Reserver',\n    department: 'Department',\n    createReservation: 'Create Reservation',\n    updateReservation: 'Update Reservation',\n    cancelReservation: 'Cancel Reservation',\n    confirmCancel: 'Confirm to cancel the reservation?',\n    cancelReason: 'Cancel Reason',\n    cancelSuccess: 'Reservation cancelled',\n    earlyReturn: 'Early Return',\n    confirmEarlyReturn: 'Confirm to return the equipment early?',\n    returnReason: 'Return Reason',\n    returnSuccess: 'Equipment returned',\n    returnFailed: 'Return failed',\n    createSuccess: 'Reservation created successfully',\n    updateSuccess: 'Reservation updated',\n    updateFailed: 'Failed to update reservation',\n    modifyReservation: 'Modify Reservation',\n    selectStartTime: 'Select start time',\n    selectEndTime: 'Select end time',\n    startTimeRequired: 'Please select start time',\n    endTimeRequired: 'Please select end time',\n    purposePlaceholder: 'Please enter purpose (optional)',\n    emailPlaceholder: 'Please enter email for reservation notifications',\n    queryPlaceholder: 'Please enter reservation code',\n    contactPlaceholder: 'Please enter the contact used when reserving',\n    codeOrContactRequired: 'Please enter reservation code (or contact)',\n    contactOrCodeRequired: 'Please enter contact (or reservation code)',\n    atLeastOneField: 'Please fill in at least one of reservation code or contact',\n    queryTip: 'You can fill in either reservation code or contact for query. If you fill in contact, all your reservations will be displayed.',\n    queryButton: 'Query',\n    reservationNotFound: 'Reservation not found',\n    cancelFailed: 'Failed to cancel reservation',\n    selectDate: 'Select Date',\n    selectTime: 'Select Time',\n    timeConflict: 'The selected time slot has been reserved',\n    invalidTime: 'Start time must be earlier than end time',\n    requiredField: 'This field is required',\n    emailFormat: 'Please enter a valid email format',\n    phoneFormat: 'Please enter a valid phone number format',\n    saveReservationCode: 'Please save your reservation code',\n    viewDetail: 'View Reservation Details',\n    reservationCodeTip: 'You can use the reservation code to query, modify or cancel the reservation.',\n    viewHistory: 'View History',\n    modificationHistory: 'Modification History',\n    noHistory: 'No modification history',\n    historyFetchFailed: 'Failed to fetch history',\n    oldValue: 'Old Value',\n    newValue: 'New Value',\n    modified: 'Modified',\n    created: 'Created',\n    deleted: 'Deleted',\n    admin: 'Admin',\n    user: 'User',\n    // Reservation Type\n    reservationType: 'Reservation Type',\n    singleReservation: 'Single Reservation',\n    recurringReservation: 'Recurring Reservation',\n    // Recurring Reservation\n    recurringForm: 'Recurring Reservation Form',\n    recurringPattern: 'Recurring Pattern',\n    patternType: 'Pattern Type',\n    recurringNotSupported: 'Recurring reservation feature is not available yet, please use single reservation',\n    daily: 'Daily',\n    weekly: 'Weekly',\n    monthly: 'Monthly',\n    daysOfWeek: 'Days of Week',\n    daysOfMonth: 'Days of Month',\n    sunday: 'Sunday',\n    monday: 'Monday',\n    tuesday: 'Tuesday',\n    wednesday: 'Wednesday',\n    thursday: 'Thursday',\n    friday: 'Friday',\n    saturday: 'Saturday',\n    selectDaysOfWeek: 'Please select days of week',\n    selectDaysOfMonth: 'Please select days of month',\n    dateRange: 'Date Range',\n    timeRange: 'Time Range',\n    createRecurringReservation: 'Create Recurring Reservation',\n    recurringReservationSuccess: 'Recurring Reservation Created Successfully',\n    recurringReservationTip: 'The system has created a series of reservations for you. You can view the details on the reservation query page.',\n    timeConflictWarning: 'Note: The system only checked the time conflict for the first day, actual reservations may have partial time conflicts.',\n    recurringReservationNotice: 'Recurring Reservation Notice',\n    partOfRecurringReservation: 'This is part of a recurring reservation',\n    viewRecurringReservation: 'View Recurring Reservation',\n    recurringReservationDetails: 'Recurring Reservation Details',\n    childReservations: 'Child Reservations',\n    noChildReservations: 'No child reservations found',\n    includePast: 'Include past reservations',\n    cancelRecurringReservation: 'Cancel Recurring Reservation',\n    cancelRecurringReservationConfirm: 'Are you sure you want to cancel this recurring reservation? This will cancel all unfinished reservations.',\n    emailForConfirmation: 'Email for confirmation',\n    pattern: 'Reservation Pattern',\n    // Conflict information\n    conflictAlert: 'Some reservations could not be created due to time conflicts',\n    totalPlanned: 'Total planned reservations',\n    createdCount: 'Successfully created',\n    skippedCount: 'Skipped due to conflicts',\n    conflictDates: 'Conflict dates',\n    // Time conflict related\n    timeConflictWith: 'Selected time slot conflicts with {count} reservation(s)',\n    timeSlotOccupied: 'Selected time slot is already reserved',\n    timeSlotAvailable: 'This time slot is available',\n    conflictWithFollowing: 'Conflicts with the following reservations:',\n    conflictTime: 'Time:',\n    conflictUser: 'User:',\n    conflictEmail: 'Email:',\n    conflictPhone: 'Phone:',\n    conflictPurpose: 'Purpose:',\n    conflictTo: 'to',\n    maxSimultaneousReached: 'Maximum simultaneous reservations ({count}) reached for this time slot'\n  },\n  // Admin\n  admin: {\n    uploadImage: 'Upload Image',\n    changeImage: 'Change Image',\n    uploadEquipmentImage: 'Upload Equipment Image',\n    imageTip: 'Recommended size: 300x200px, max 2MB',\n    imageTypeError: 'Only image files are allowed',\n    imageSizeError: 'Image size cannot exceed 2MB',\n    imageUploadSuccess: 'Image uploaded successfully',\n    imageUploadError: 'Failed to upload image',\n    richTextEditorTip: 'You can use the rich text editor to add formatted text, lists, links, etc.',\n    videoTutorialTip: 'Enter video link to display video tutorial on equipment detail page',\n    importExport: 'Import/Export',\n    importEquipment: 'Import Equipment',\n    exportEquipment: 'Export Equipment',\n    downloadTemplate: 'Download Template',\n    importSuccess: 'Equipment imported successfully',\n    importError: 'Failed to import equipment',\n    exportError: 'Failed to export equipment',\n    templateError: 'Failed to download template',\n    login: 'Admin Login',\n    dashboard: 'Dashboard',\n    equipment: 'Equipment Management',\n    reservation: 'Reservation Management',\n    settings: 'System Settings',\n    username: 'Username',\n    password: 'Password',\n    loginButton: 'Login',\n    loginSuccess: 'Login successful',\n    loginFailed: 'Login failed, please check username and password',\n    welcome: 'Welcome, ',\n    statistics: 'Statistics',\n    totalEquipment: 'Total Equipment',\n    availableEquipment: 'Available Equipment',\n    totalReservation: 'Total Reservations',\n    activeReservation: 'Active Reservations',\n    recentReservations: 'Recent Reservations',\n    addEquipment: 'Add Equipment',\n    editEquipment: 'Edit Equipment',\n    deleteEquipment: 'Delete Equipment',\n    confirmDeleteEquipment: 'Confirm to delete this equipment?',\n    equipmentDeleted: 'Equipment deleted',\n    equipmentAdded: 'Equipment added',\n    equipmentUpdated: 'Equipment updated',\n    viewReservation: 'View Reservation',\n    reservationDetail: 'Reservation Detail',\n    systemSettings: 'System Settings',\n    siteName: 'Site Name',\n    maintenanceMode: 'Maintenance Mode',\n    reservationLimitPerDay: 'Reservation Limit Per Day',\n    allowEquipmentConflict: 'Allow Equipment Conflict',\n    advanceReservationDays: 'Advance Reservation Days',\n    settingsSaved: 'Settings saved'\n  },\n  // Error\n  error: {\n    notFound: 'Page Not Found',\n    backHome: 'Back to Home',\n    serverError: 'Server Error',\n    unauthorized: 'Unauthorized Access',\n    forbidden: 'Forbidden Access',\n    errorMessage: 'Oops! Something went wrong!'\n  },\n  // Calendar View\n  calendar: {\n    title: 'Reservation Calendar',\n    month: 'Month',\n    week: 'Week',\n    day: 'Day',\n    today: 'Today',\n    loadFailed: 'Failed to load calendar data',\n    noEvents: 'No reservations found',\n    partOfRecurringReservation: 'This is part of a recurring reservation',\n    reservationInfo: 'Reservation Information',\n    selectEquipment: 'Select Equipment to Filter',\n    allEquipment: 'All Equipment',\n    statusLegend: 'Reservation Status Legend',\n    confirmedStatus: 'Green indicates confirmed reservations',\n    inUseStatus: 'Blue indicates reservations currently in use',\n    cancelTip: 'Click on a reservation and enter the reservation code to cancel it',\n    updateTip: 'Click on a reservation and enter the reservation code to update it'\n  }\n};", "map": {"version": 3, "names": ["common", "appName", "fullAppName", "loading", "success", "downloadSuccess", "error", "warning", "info", "confirm", "cancel", "save", "edit", "delete", "search", "reset", "submit", "back", "more", "all", "yes", "no", "noData", "operation", "actions", "status", "createTime", "updateTime", "userInfo", "instructions", "today", "week", "month", "notProvided", "id", "lengthLimit", "view", "toggleTheme", "darkMode", "lightMode", "useBrowserLanguage", "total", "items", "nav", "home", "equipment", "reservation", "query", "admin", "login", "logout", "calendar", "welcome", "description", "reserveEquipment", "queryReservation", "step1", "step2", "step3", "step4", "step5", "contactInfo", "viewReservations", "viewReservationsDesc", "browseEquipment", "myReservations", "myReservationsDesc", "manageReservations", "calendarView", "calendarViewDesc", "viewCalendar", "publicQuery", "queryResults", "noRecordsFound", "list", "detail", "name", "category", "model", "location", "userGuide", "userGuidePlaceholder", "videoTutorial", "image", "videoTutorialPlaceholder", "available", "maintenance", "inUse", "reserve", "viewDetail", "noDescription", "filter", "allCategories", "allStatus", "searchPlaceholder", "currentReservations", "noReservations", "equipmentInfo", "simultaneousReservation", "simultaneousReservationInfo", "simultaneousReservationDesc", "cancelConfirmation", "cancelConfirmationMessage", "queryDescription", "form", "recurringForm", "recurringDetail", "personalManagement", "noReservationsFound", "tryDifferentCriteria", "date<PERSON><PERSON><PERSON>", "startDate", "endDate", "selectDateRange", "checkCodeAndContact", "queryInstructions", "queryInstruction1", "queryInstruction2", "queryInstruction3", "code", "equipmentName", "userName", "userDepartment", "userContact", "userEmail", "startTime", "endTime", "purpose", "reservationInfo", "number", "active", "ongoing", "confirmed", "cancelled", "expired", "statusConfirmed", "statusCancelled", "statusExpired", "statusInUse", "reserver", "department", "createReservation", "updateReservation", "cancelReservation", "confirmCancel", "cancelReason", "cancelSuccess", "earlyReturn", "confirmEarlyReturn", "returnReason", "returnSuccess", "returnFailed", "createSuccess", "updateSuccess", "updateFailed", "modifyReservation", "selectStartTime", "selectEndTime", "startTimeRequired", "endTimeRequired", "purposePlaceholder", "emailPlaceholder", "queryPlaceholder", "contactPlaceholder", "codeOrContactRequired", "contactOrCodeRequired", "atLeastOneField", "queryTip", "query<PERSON><PERSON><PERSON>", "reservationNotFound", "cancelFailed", "selectDate", "selectTime", "timeConflict", "invalidTime", "requiredField", "emailFormat", "phoneFormat", "saveReservationCode", "reservationCodeTip", "viewHistory", "modificationHistory", "noHistory", "historyFetchFailed", "oldValue", "newValue", "modified", "created", "deleted", "user", "reservationType", "singleReservation", "recurringReservation", "recurringPattern", "patternType", "recurringNotSupported", "daily", "weekly", "monthly", "daysOfWeek", "daysOfMonth", "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "selectDaysOfWeek", "selectDaysOfMonth", "timeRange", "createRecurringReservation", "recurringReservationSuccess", "recurringReservationTip", "timeConflictWarning", "recurringReservationNotice", "partOfRecurringReservation", "viewRecurringReservation", "recurringReservationDetails", "childReservations", "noChildReservations", "includePast", "cancelRecurringReservation", "cancelRecurringReservationConfirm", "emailForConfirmation", "pattern", "conflict<PERSON><PERSON><PERSON>", "totalPlanned", "createdCount", "skippedCount", "conflictDates", "timeConflictWith", "timeSlotOccupied", "timeSlotAvailable", "conflictWithFollowing", "conflictTime", "conflictUser", "conflictEmail", "conflictPhone", "conflictPurpose", "conflictTo", "maxSimultaneousReached", "uploadImage", "changeImage", "uploadEquipmentImage", "imageTip", "imageTypeError", "imageSizeError", "imageUploadSuccess", "imageUploadError", "richTextEditorTip", "videoTutorialTip", "importExport", "importEquipment", "exportEquipment", "downloadTemplate", "importSuccess", "importError", "exportError", "templateError", "dashboard", "settings", "username", "password", "loginButton", "loginSuccess", "loginFailed", "statistics", "totalEquipment", "availableEquipment", "totalReservation", "activeReservation", "recentReservations", "addEquipment", "editEquipment", "deleteEquipment", "confirmDeleteEquipment", "equipmentDeleted", "equipmentAdded", "equipmentUpdated", "viewReservation", "reservationDetail", "systemSettings", "siteName", "maintenanceMode", "reservationLimitPerDay", "allowEquipmentConflict", "advanceReservationDays", "settingsSaved", "notFound", "backHome", "serverError", "unauthorized", "forbidden", "errorMessage", "title", "day", "loadFailed", "noEvents", "selectEquipment", "allEquipment", "statusLegend", "confirmedStatus", "inUseStatus", "cancelTip", "updateTip"], "sources": ["D:/Equipment-Reservation-System-main/frontend/src/locales/en.js"], "sourcesContent": ["export default {\n  // Common\n  common: {\n    appName: 'Equipment Reservation System',\n    fullAppName: 'HTNIA Equipment Reservation System',\n    loading: 'Loading...',\n    success: 'Success',\n    downloadSuccess: 'Download successful',\n    error: 'Error',\n    warning: 'Warning',\n    info: 'Information',\n    confirm: 'Confirm',\n    cancel: 'Cancel',\n    save: 'Save',\n    edit: 'Edit',\n    delete: 'Delete',\n    search: 'Search',\n    reset: 'Reset',\n    submit: 'Submit',\n    back: 'Back',\n    more: 'More',\n    all: 'All',\n    yes: 'Yes',\n    no: 'No',\n    noData: 'No Data',\n    operation: 'Operation',\n    actions: 'Actions',\n    status: 'Status',\n    createTime: 'Create Time',\n    updateTime: 'Update Time',\n    userInfo: 'User Information',\n    instructions: 'Instructions',\n    today: 'Today',\n    week: 'Week',\n    month: 'Month',\n    notProvided: 'Not Provided',\n    id: 'ID',\n    lengthLimit: 'Length Limit',\n    view: 'View',\n    toggleTheme: 'Toggle Dark/Light Theme',\n    darkMode: 'Dark Mode',\n    lightMode: 'Light Mode',\n    useBrowserLanguage: 'Use Browser Language',\n    total: 'Total',\n    items: 'items'\n  },\n\n  // Navigation\n  nav: {\n    home: 'Home',\n    equipment: 'Equipment List',\n    reservation: 'Reservation Management',\n    query: 'Query Reservation',\n    admin: 'Admin Console',\n    login: 'Login',\n    logout: 'Logout',\n    calendar: 'Calendar View'\n  },\n\n  // Home\n  home: {\n    welcome: 'Welcome to Equipment Reservation System',\n    description: 'This is a simple and easy-to-use equipment reservation system where you can reserve various equipment and places from the school.',\n    reserveEquipment: 'Reserve Equipment',\n    queryReservation: 'Query Reservation',\n    instructions: 'Instructions',\n    step1: 'Browse available equipment and select the one you need',\n    step2: 'Choose reservation time slot',\n    step3: 'Fill in your personal information',\n    step4: 'Submit reservation request',\n    step5: 'Record the reservation code for future query or cancellation',\n    contactInfo: 'If you have any questions, please contact the IT department.',\n    viewReservations: 'View Equipment',\n    viewReservationsDesc: 'View equipment reservation status to help you check and plan your reservations.',\n    browseEquipment: 'Browse Equipment',\n    myReservations: 'My Reservations',\n    myReservationsDesc: 'Create new reservations, query and manage your reservation records, modify or cancel existing reservations.',\n    manageReservations: 'Manage Reservations',\n    calendarView: 'Calendar View',\n    calendarViewDesc: 'View equipment reservations through an intuitive calendar interface to better plan your reservation time.',\n    viewCalendar: 'View Calendar',\n    publicQuery: 'Public Reservation Query',\n    queryResults: 'Query Results',\n    noRecordsFound: 'No matching reservation records found'\n  },\n\n  // Equipment\n  equipment: {\n    description: 'Browse and reserve various equipment available in the system.',\n    list: 'Equipment List',\n    detail: 'Equipment Detail',\n    name: 'Equipment Name',\n    category: 'Category',\n    model: 'Model',\n    location: 'Location',\n    status: 'Status',\n    description: 'Description',\n    userGuide: 'User Guide',\n    userGuidePlaceholder: 'Please enter detailed usage steps, precautions, etc.',\n    videoTutorial: 'Video Tutorial',\n    image: 'Equipment Image',\n    videoTutorialPlaceholder: 'Please enter video link, supports YouTube, Bilibili, etc.',\n    available: 'Available',\n    maintenance: 'Maintenance',\n    inUse: 'In Use',\n    reserve: 'Reserve This Equipment',\n    viewDetail: 'View Details',\n    noDescription: 'No Description',\n    filter: 'Filter',\n    allCategories: 'All Categories',\n    allStatus: 'All Status',\n    searchPlaceholder: 'Search for equipment name, model or location',\n    currentReservations: 'Current Reservations',\n    noReservations: 'No reservation records',\n    equipmentInfo: 'Equipment Information',\n    simultaneousReservation: 'Simultaneous Reservation',\n    simultaneousReservationInfo: 'Simultaneous Reservation Info',\n    simultaneousReservationDesc: 'This equipment allows up to {max} people to reserve simultaneously. You can make a reservation even during time slots that already have reservations.'\n  },\n\n  // Reservation\n  reservation: {\n    cancelConfirmation:'Warning',\n    cancelConfirmationMessage:'Confirm to cancel reservation?',\n    description: 'Create, manage and track your equipment reservations easily.',\n    queryDescription: 'Find your reservation details using reservation code or contact information.',\n    form: 'Reservation Form',\n    recurringForm: 'Recurring Reservation',\n    detail: 'Reservation Detail',\n    recurringDetail: 'Recurring Reservation Detail',\n    query: 'Query Reservation',\n    publicQuery: 'Public Query',\n    personalManagement: 'Personal Reservation Management',\n    queryResults: 'Query Results',\n    noReservationsFound: 'No Reservations Found',\n    noRecordsFound: 'No matching reservation records found',\n    tryDifferentCriteria: 'Please try different query criteria',\n    dateRange: 'Date Range',\n    startDate: 'Start Date',\n    endDate: 'End Date',\n    selectDateRange: 'Select Date Range',\n    checkCodeAndContact: 'Please check your reservation code and contact information',\n    queryInstructions: 'Query your reservation information using reservation code or contact:',\n    queryInstruction1: 'Enter your reservation code or contact used when reserving',\n    queryInstruction2: 'Click \"Query\" button to view reservation details',\n    queryInstruction3: 'You can view, modify or cancel your reservation on the detail page',\n    code: 'Reservation Code',\n    equipmentName: 'Equipment Name',\n    userName: 'User Name',\n    userDepartment: 'User Department',\n    userContact: 'Contact',\n    userEmail: 'Email (Required)',\n    startTime: 'Start Time',\n    endTime: 'End Time',\n    purpose: 'Purpose (Optional)',\n    status: 'Status',\n    reservationInfo: 'Reservation Information',\n    userInfo: 'User Information',\n    number: 'Reservation Number',\n    active: 'Active',\n    ongoing: 'In Progress',\n    confirmed: 'Confirmed',\n    cancelled: 'Cancelled',\n    expired: 'Expired',\n    inUse: 'In Use',\n    statusConfirmed: 'Confirmed',\n    statusCancelled: 'Cancelled',\n    statusExpired: 'Expired',\n    statusInUse: 'In Use',\n    reserver: 'Reserver',\n    department: 'Department',\n    createReservation: 'Create Reservation',\n    updateReservation: 'Update Reservation',\n    cancelReservation: 'Cancel Reservation',\n    confirmCancel: 'Confirm to cancel the reservation?',\n    cancelReason: 'Cancel Reason',\n    cancelSuccess: 'Reservation cancelled',\n    earlyReturn: 'Early Return',\n    confirmEarlyReturn: 'Confirm to return the equipment early?',\n    returnReason: 'Return Reason',\n    returnSuccess: 'Equipment returned',\n    returnFailed: 'Return failed',\n    createSuccess: 'Reservation created successfully',\n    updateSuccess: 'Reservation updated',\n    updateFailed: 'Failed to update reservation',\n    modifyReservation: 'Modify Reservation',\n    selectStartTime: 'Select start time',\n    selectEndTime: 'Select end time',\n    startTimeRequired: 'Please select start time',\n    endTimeRequired: 'Please select end time',\n    purposePlaceholder: 'Please enter purpose (optional)',\n    emailPlaceholder: 'Please enter email for reservation notifications',\n    queryPlaceholder: 'Please enter reservation code',\n    contactPlaceholder: 'Please enter the contact used when reserving',\n    codeOrContactRequired: 'Please enter reservation code (or contact)',\n    contactOrCodeRequired: 'Please enter contact (or reservation code)',\n    atLeastOneField: 'Please fill in at least one of reservation code or contact',\n    queryTip: 'You can fill in either reservation code or contact for query. If you fill in contact, all your reservations will be displayed.',\n    queryButton: 'Query',\n    reservationNotFound: 'Reservation not found',\n    cancelFailed: 'Failed to cancel reservation',\n    selectDate: 'Select Date',\n    selectTime: 'Select Time',\n    timeConflict: 'The selected time slot has been reserved',\n    invalidTime: 'Start time must be earlier than end time',\n    requiredField: 'This field is required',\n    emailFormat: 'Please enter a valid email format',\n    phoneFormat: 'Please enter a valid phone number format',\n    saveReservationCode: 'Please save your reservation code',\n    viewDetail: 'View Reservation Details',\n    reservationCodeTip: 'You can use the reservation code to query, modify or cancel the reservation.',\n    viewHistory: 'View History',\n    modificationHistory: 'Modification History',\n    noHistory: 'No modification history',\n    historyFetchFailed: 'Failed to fetch history',\n    oldValue: 'Old Value',\n    newValue: 'New Value',\n    modified: 'Modified',\n    created: 'Created',\n    deleted: 'Deleted',\n    admin: 'Admin',\n    user: 'User',\n\n    // Reservation Type\n    reservationType: 'Reservation Type',\n    singleReservation: 'Single Reservation',\n    recurringReservation: 'Recurring Reservation',\n\n    // Recurring Reservation\n    recurringForm: 'Recurring Reservation Form',\n    recurringPattern: 'Recurring Pattern',\n    patternType: 'Pattern Type',\n    recurringNotSupported: 'Recurring reservation feature is not available yet, please use single reservation',\n    daily: 'Daily',\n    weekly: 'Weekly',\n    monthly: 'Monthly',\n    daysOfWeek: 'Days of Week',\n    daysOfMonth: 'Days of Month',\n    sunday: 'Sunday',\n    monday: 'Monday',\n    tuesday: 'Tuesday',\n    wednesday: 'Wednesday',\n    thursday: 'Thursday',\n    friday: 'Friday',\n    saturday: 'Saturday',\n    selectDaysOfWeek: 'Please select days of week',\n    selectDaysOfMonth: 'Please select days of month',\n    dateRange: 'Date Range',\n    timeRange: 'Time Range',\n    createRecurringReservation: 'Create Recurring Reservation',\n    recurringReservationSuccess: 'Recurring Reservation Created Successfully',\n    recurringReservationTip: 'The system has created a series of reservations for you. You can view the details on the reservation query page.',\n    timeConflictWarning: 'Note: The system only checked the time conflict for the first day, actual reservations may have partial time conflicts.',\n    recurringReservationNotice: 'Recurring Reservation Notice',\n    partOfRecurringReservation: 'This is part of a recurring reservation',\n    viewRecurringReservation: 'View Recurring Reservation',\n    recurringReservationDetails: 'Recurring Reservation Details',\n    childReservations: 'Child Reservations',\n    noChildReservations: 'No child reservations found',\n    includePast: 'Include past reservations',\n    cancelRecurringReservation: 'Cancel Recurring Reservation',\n    cancelRecurringReservationConfirm: 'Are you sure you want to cancel this recurring reservation? This will cancel all unfinished reservations.',\n    emailForConfirmation: 'Email for confirmation',\n    pattern: 'Reservation Pattern',\n    // Conflict information\n    conflictAlert: 'Some reservations could not be created due to time conflicts',\n    totalPlanned: 'Total planned reservations',\n    createdCount: 'Successfully created',\n    skippedCount: 'Skipped due to conflicts',\n    conflictDates: 'Conflict dates',\n    // Time conflict related\n    timeConflictWith: 'Selected time slot conflicts with {count} reservation(s)',\n    timeSlotOccupied: 'Selected time slot is already reserved',\n    timeSlotAvailable: 'This time slot is available',\n    conflictWithFollowing: 'Conflicts with the following reservations:',\n    conflictTime: 'Time:',\n    conflictUser: 'User:',\n    conflictEmail: 'Email:',\n    conflictPhone: 'Phone:',\n    conflictPurpose: 'Purpose:',\n    conflictTo: 'to',\n    maxSimultaneousReached: 'Maximum simultaneous reservations ({count}) reached for this time slot'\n  },\n\n  // Admin\n  admin: {\n    uploadImage: 'Upload Image',\n    changeImage: 'Change Image',\n    uploadEquipmentImage: 'Upload Equipment Image',\n    imageTip: 'Recommended size: 300x200px, max 2MB',\n    imageTypeError: 'Only image files are allowed',\n    imageSizeError: 'Image size cannot exceed 2MB',\n    imageUploadSuccess: 'Image uploaded successfully',\n    imageUploadError: 'Failed to upload image',\n    richTextEditorTip: 'You can use the rich text editor to add formatted text, lists, links, etc.',\n    videoTutorialTip: 'Enter video link to display video tutorial on equipment detail page',\n    importExport: 'Import/Export',\n    importEquipment: 'Import Equipment',\n    exportEquipment: 'Export Equipment',\n    downloadTemplate: 'Download Template',\n    importSuccess: 'Equipment imported successfully',\n    importError: 'Failed to import equipment',\n    exportError: 'Failed to export equipment',\n    templateError: 'Failed to download template',\n    login: 'Admin Login',\n    dashboard: 'Dashboard',\n    equipment: 'Equipment Management',\n    reservation: 'Reservation Management',\n    settings: 'System Settings',\n    username: 'Username',\n    password: 'Password',\n    loginButton: 'Login',\n    loginSuccess: 'Login successful',\n    loginFailed: 'Login failed, please check username and password',\n    welcome: 'Welcome, ',\n    statistics: 'Statistics',\n    totalEquipment: 'Total Equipment',\n    availableEquipment: 'Available Equipment',\n    totalReservation: 'Total Reservations',\n    activeReservation: 'Active Reservations',\n    recentReservations: 'Recent Reservations',\n    addEquipment: 'Add Equipment',\n    editEquipment: 'Edit Equipment',\n    deleteEquipment: 'Delete Equipment',\n    confirmDeleteEquipment: 'Confirm to delete this equipment?',\n    equipmentDeleted: 'Equipment deleted',\n    equipmentAdded: 'Equipment added',\n    equipmentUpdated: 'Equipment updated',\n    viewReservation: 'View Reservation',\n    reservationDetail: 'Reservation Detail',\n    systemSettings: 'System Settings',\n    siteName: 'Site Name',\n    maintenanceMode: 'Maintenance Mode',\n    reservationLimitPerDay: 'Reservation Limit Per Day',\n    allowEquipmentConflict: 'Allow Equipment Conflict',\n    advanceReservationDays: 'Advance Reservation Days',\n    settingsSaved: 'Settings saved'\n  },\n\n  // Error\n  error: {\n    notFound: 'Page Not Found',\n    backHome: 'Back to Home',\n    serverError: 'Server Error',\n    unauthorized: 'Unauthorized Access',\n    forbidden: 'Forbidden Access',\n    errorMessage: 'Oops! Something went wrong!'\n  },\n\n  // Calendar View\n  calendar: {\n    title: 'Reservation Calendar',\n    month: 'Month',\n    week: 'Week',\n    day: 'Day',\n    today: 'Today',\n    loadFailed: 'Failed to load calendar data',\n    noEvents: 'No reservations found',\n    partOfRecurringReservation: 'This is part of a recurring reservation',\n    reservationInfo: 'Reservation Information',\n    selectEquipment: 'Select Equipment to Filter',\n    allEquipment: 'All Equipment',\n    statusLegend: 'Reservation Status Legend',\n    confirmedStatus: 'Green indicates confirmed reservations',\n    inUseStatus: 'Blue indicates reservations currently in use',\n    cancelTip: 'Click on a reservation and enter the reservation code to cancel it',\n    updateTip: 'Click on a reservation and enter the reservation code to update it'\n\n  }\n}\n"], "mappings": "AAAA,eAAe;EACb;EACAA,MAAM,EAAE;IACNC,OAAO,EAAE,8BAA8B;IACvCC,WAAW,EAAE,oCAAoC;IACjDC,OAAO,EAAE,YAAY;IACrBC,OAAO,EAAE,SAAS;IAClBC,eAAe,EAAE,qBAAqB;IACtCC,KAAK,EAAE,OAAO;IACdC,OAAO,EAAE,SAAS;IAClBC,IAAI,EAAE,aAAa;IACnBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE,KAAK;IACVC,GAAG,EAAE,KAAK;IACVC,EAAE,EAAE,IAAI;IACRC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,kBAAkB;IAC5BC,YAAY,EAAE,cAAc;IAC5BC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,OAAO;IACdC,WAAW,EAAE,cAAc;IAC3BC,EAAE,EAAE,IAAI;IACRC,WAAW,EAAE,cAAc;IAC3BC,IAAI,EAAE,MAAM;IACZC,WAAW,EAAE,yBAAyB;IACtCC,QAAQ,EAAE,WAAW;IACrBC,SAAS,EAAE,YAAY;IACvBC,kBAAkB,EAAE,sBAAsB;IAC1CC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE;EACT,CAAC;EAED;EACAC,GAAG,EAAE;IACHC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE,gBAAgB;IAC3BC,WAAW,EAAE,wBAAwB;IACrCC,KAAK,EAAE,mBAAmB;IAC1BC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE;EACZ,CAAC;EAED;EACAP,IAAI,EAAE;IACJQ,OAAO,EAAE,yCAAyC;IAClDC,WAAW,EAAE,mIAAmI;IAChJC,gBAAgB,EAAE,mBAAmB;IACrCC,gBAAgB,EAAE,mBAAmB;IACrC1B,YAAY,EAAE,cAAc;IAC5B2B,KAAK,EAAE,wDAAwD;IAC/DC,KAAK,EAAE,8BAA8B;IACrCC,KAAK,EAAE,mCAAmC;IAC1CC,KAAK,EAAE,4BAA4B;IACnCC,KAAK,EAAE,8DAA8D;IACrEC,WAAW,EAAE,8DAA8D;IAC3EC,gBAAgB,EAAE,gBAAgB;IAClCC,oBAAoB,EAAE,iFAAiF;IACvGC,eAAe,EAAE,kBAAkB;IACnCC,cAAc,EAAE,iBAAiB;IACjCC,kBAAkB,EAAE,6GAA6G;IACjIC,kBAAkB,EAAE,qBAAqB;IACzCC,YAAY,EAAE,eAAe;IAC7BC,gBAAgB,EAAE,2GAA2G;IAC7HC,YAAY,EAAE,eAAe;IAC7BC,WAAW,EAAE,0BAA0B;IACvCC,YAAY,EAAE,eAAe;IAC7BC,cAAc,EAAE;EAClB,CAAC;EAED;EACA5B,SAAS,EAAE;IACTQ,WAAW,EAAE,+DAA+D;IAC5EqB,IAAI,EAAE,gBAAgB;IACtBC,MAAM,EAAE,kBAAkB;IAC1BC,IAAI,EAAE,gBAAgB;IACtBC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,UAAU;IACpBtD,MAAM,EAAE,QAAQ;IAChB4B,WAAW,EAAE,aAAa;IAC1B2B,SAAS,EAAE,YAAY;IACvBC,oBAAoB,EAAE,sDAAsD;IAC5EC,aAAa,EAAE,gBAAgB;IAC/BC,KAAK,EAAE,iBAAiB;IACxBC,wBAAwB,EAAE,2DAA2D;IACrFC,SAAS,EAAE,WAAW;IACtBC,WAAW,EAAE,aAAa;IAC1BC,KAAK,EAAE,QAAQ;IACfC,OAAO,EAAE,wBAAwB;IACjCC,UAAU,EAAE,cAAc;IAC1BC,aAAa,EAAE,gBAAgB;IAC/BC,MAAM,EAAE,QAAQ;IAChBC,aAAa,EAAE,gBAAgB;IAC/BC,SAAS,EAAE,YAAY;IACvBC,iBAAiB,EAAE,8CAA8C;IACjEC,mBAAmB,EAAE,sBAAsB;IAC3CC,cAAc,EAAE,wBAAwB;IACxCC,aAAa,EAAE,uBAAuB;IACtCC,uBAAuB,EAAE,0BAA0B;IACnDC,2BAA2B,EAAE,+BAA+B;IAC5DC,2BAA2B,EAAE;EAC/B,CAAC;EAED;EACAtD,WAAW,EAAE;IACXuD,kBAAkB,EAAC,SAAS;IAC5BC,yBAAyB,EAAC,gCAAgC;IAC1DjD,WAAW,EAAE,8DAA8D;IAC3EkD,gBAAgB,EAAE,8EAA8E;IAChGC,IAAI,EAAE,kBAAkB;IACxBC,aAAa,EAAE,uBAAuB;IACtC9B,MAAM,EAAE,oBAAoB;IAC5B+B,eAAe,EAAE,8BAA8B;IAC/C3D,KAAK,EAAE,mBAAmB;IAC1BwB,WAAW,EAAE,cAAc;IAC3BoC,kBAAkB,EAAE,iCAAiC;IACrDnC,YAAY,EAAE,eAAe;IAC7BoC,mBAAmB,EAAE,uBAAuB;IAC5CnC,cAAc,EAAE,uCAAuC;IACvDoC,oBAAoB,EAAE,qCAAqC;IAC3DC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,UAAU;IACnBC,eAAe,EAAE,mBAAmB;IACpCC,mBAAmB,EAAE,4DAA4D;IACjFC,iBAAiB,EAAE,uEAAuE;IAC1FC,iBAAiB,EAAE,4DAA4D;IAC/EC,iBAAiB,EAAE,kDAAkD;IACrEC,iBAAiB,EAAE,oEAAoE;IACvFC,IAAI,EAAE,kBAAkB;IACxBC,aAAa,EAAE,gBAAgB;IAC/BC,QAAQ,EAAE,WAAW;IACrBC,cAAc,EAAE,iBAAiB;IACjCC,WAAW,EAAE,SAAS;IACtBC,SAAS,EAAE,kBAAkB;IAC7BC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,UAAU;IACnBC,OAAO,EAAE,oBAAoB;IAC7BtG,MAAM,EAAE,QAAQ;IAChBuG,eAAe,EAAE,yBAAyB;IAC1CpG,QAAQ,EAAE,kBAAkB;IAC5BqG,MAAM,EAAE,oBAAoB;IAC5BC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,aAAa;IACtBC,SAAS,EAAE,WAAW;IACtBC,SAAS,EAAE,WAAW;IACtBC,OAAO,EAAE,SAAS;IAClB/C,KAAK,EAAE,QAAQ;IACfgD,eAAe,EAAE,WAAW;IAC5BC,eAAe,EAAE,WAAW;IAC5BC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE,QAAQ;IACrBC,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,YAAY;IACxBC,iBAAiB,EAAE,oBAAoB;IACvCC,iBAAiB,EAAE,oBAAoB;IACvCC,iBAAiB,EAAE,oBAAoB;IACvCC,aAAa,EAAE,oCAAoC;IACnDC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,uBAAuB;IACtCC,WAAW,EAAE,cAAc;IAC3BC,kBAAkB,EAAE,wCAAwC;IAC5DC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,oBAAoB;IACnCC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,kCAAkC;IACjDC,aAAa,EAAE,qBAAqB;IACpCC,YAAY,EAAE,8BAA8B;IAC5CC,iBAAiB,EAAE,oBAAoB;IACvCC,eAAe,EAAE,mBAAmB;IACpCC,aAAa,EAAE,iBAAiB;IAChCC,iBAAiB,EAAE,0BAA0B;IAC7CC,eAAe,EAAE,wBAAwB;IACzCC,kBAAkB,EAAE,iCAAiC;IACrDC,gBAAgB,EAAE,kDAAkD;IACpEC,gBAAgB,EAAE,+BAA+B;IACjDC,kBAAkB,EAAE,8CAA8C;IAClEC,qBAAqB,EAAE,4CAA4C;IACnEC,qBAAqB,EAAE,4CAA4C;IACnEC,eAAe,EAAE,4DAA4D;IAC7EC,QAAQ,EAAE,gIAAgI;IAC1IC,WAAW,EAAE,OAAO;IACpBC,mBAAmB,EAAE,uBAAuB;IAC5CC,YAAY,EAAE,8BAA8B;IAC5CC,UAAU,EAAE,aAAa;IACzBC,UAAU,EAAE,aAAa;IACzBC,YAAY,EAAE,0CAA0C;IACxDC,WAAW,EAAE,0CAA0C;IACvDC,aAAa,EAAE,wBAAwB;IACvCC,WAAW,EAAE,mCAAmC;IAChDC,WAAW,EAAE,0CAA0C;IACvDC,mBAAmB,EAAE,mCAAmC;IACxDzF,UAAU,EAAE,0BAA0B;IACtC0F,kBAAkB,EAAE,8EAA8E;IAClGC,WAAW,EAAE,cAAc;IAC3BC,mBAAmB,EAAE,sBAAsB;IAC3CC,SAAS,EAAE,yBAAyB;IACpCC,kBAAkB,EAAE,yBAAyB;IAC7CC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,WAAW;IACrBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,SAAS;IAClBC,OAAO,EAAE,SAAS;IAClB5I,KAAK,EAAE,OAAO;IACd6I,IAAI,EAAE,MAAM;IAEZ;IACAC,eAAe,EAAE,kBAAkB;IACnCC,iBAAiB,EAAE,oBAAoB;IACvCC,oBAAoB,EAAE,uBAAuB;IAE7C;IACAvF,aAAa,EAAE,4BAA4B;IAC3CwF,gBAAgB,EAAE,mBAAmB;IACrCC,WAAW,EAAE,cAAc;IAC3BC,qBAAqB,EAAE,mFAAmF;IAC1GC,KAAK,EAAE,OAAO;IACdC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,UAAU,EAAE,cAAc;IAC1BC,WAAW,EAAE,eAAe;IAC5BC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,UAAU;IACpBC,gBAAgB,EAAE,4BAA4B;IAC9CC,iBAAiB,EAAE,6BAA6B;IAChDnG,SAAS,EAAE,YAAY;IACvBoG,SAAS,EAAE,YAAY;IACvBC,0BAA0B,EAAE,8BAA8B;IAC1DC,2BAA2B,EAAE,4CAA4C;IACzEC,uBAAuB,EAAE,kHAAkH;IAC3IC,mBAAmB,EAAE,yHAAyH;IAC9IC,0BAA0B,EAAE,8BAA8B;IAC1DC,0BAA0B,EAAE,yCAAyC;IACrEC,wBAAwB,EAAE,4BAA4B;IACtDC,2BAA2B,EAAE,+BAA+B;IAC5DC,iBAAiB,EAAE,oBAAoB;IACvCC,mBAAmB,EAAE,6BAA6B;IAClDC,WAAW,EAAE,2BAA2B;IACxCC,0BAA0B,EAAE,8BAA8B;IAC1DC,iCAAiC,EAAE,2GAA2G;IAC9IC,oBAAoB,EAAE,wBAAwB;IAC9CC,OAAO,EAAE,qBAAqB;IAC9B;IACAC,aAAa,EAAE,8DAA8D;IAC7EC,YAAY,EAAE,4BAA4B;IAC1CC,YAAY,EAAE,sBAAsB;IACpCC,YAAY,EAAE,0BAA0B;IACxCC,aAAa,EAAE,gBAAgB;IAC/B;IACAC,gBAAgB,EAAE,0DAA0D;IAC5EC,gBAAgB,EAAE,wCAAwC;IAC1DC,iBAAiB,EAAE,6BAA6B;IAChDC,qBAAqB,EAAE,4CAA4C;IACnEC,YAAY,EAAE,OAAO;IACrBC,YAAY,EAAE,OAAO;IACrBC,aAAa,EAAE,QAAQ;IACvBC,aAAa,EAAE,QAAQ;IACvBC,eAAe,EAAE,UAAU;IAC3BC,UAAU,EAAE,IAAI;IAChBC,sBAAsB,EAAE;EAC1B,CAAC;EAED;EACAjM,KAAK,EAAE;IACLkM,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,cAAc;IAC3BC,oBAAoB,EAAE,wBAAwB;IAC9CC,QAAQ,EAAE,sCAAsC;IAChDC,cAAc,EAAE,8BAA8B;IAC9CC,cAAc,EAAE,8BAA8B;IAC9CC,kBAAkB,EAAE,6BAA6B;IACjDC,gBAAgB,EAAE,wBAAwB;IAC1CC,iBAAiB,EAAE,4EAA4E;IAC/FC,gBAAgB,EAAE,qEAAqE;IACvFC,YAAY,EAAE,eAAe;IAC7BC,eAAe,EAAE,kBAAkB;IACnCC,eAAe,EAAE,kBAAkB;IACnCC,gBAAgB,EAAE,mBAAmB;IACrCC,aAAa,EAAE,iCAAiC;IAChDC,WAAW,EAAE,4BAA4B;IACzCC,WAAW,EAAE,4BAA4B;IACzCC,aAAa,EAAE,6BAA6B;IAC5ClN,KAAK,EAAE,aAAa;IACpBmN,SAAS,EAAE,WAAW;IACtBvN,SAAS,EAAE,sBAAsB;IACjCC,WAAW,EAAE,wBAAwB;IACrCuN,QAAQ,EAAE,iBAAiB;IAC3BC,QAAQ,EAAE,UAAU;IACpBC,QAAQ,EAAE,UAAU;IACpBC,WAAW,EAAE,OAAO;IACpBC,YAAY,EAAE,kBAAkB;IAChCC,WAAW,EAAE,kDAAkD;IAC/DtN,OAAO,EAAE,WAAW;IACpBuN,UAAU,EAAE,YAAY;IACxBC,cAAc,EAAE,iBAAiB;IACjCC,kBAAkB,EAAE,qBAAqB;IACzCC,gBAAgB,EAAE,oBAAoB;IACtCC,iBAAiB,EAAE,qBAAqB;IACxCC,kBAAkB,EAAE,qBAAqB;IACzCC,YAAY,EAAE,eAAe;IAC7BC,aAAa,EAAE,gBAAgB;IAC/BC,eAAe,EAAE,kBAAkB;IACnCC,sBAAsB,EAAE,mCAAmC;IAC3DC,gBAAgB,EAAE,mBAAmB;IACrCC,cAAc,EAAE,iBAAiB;IACjCC,gBAAgB,EAAE,mBAAmB;IACrCC,eAAe,EAAE,kBAAkB;IACnCC,iBAAiB,EAAE,oBAAoB;IACvCC,cAAc,EAAE,iBAAiB;IACjCC,QAAQ,EAAE,WAAW;IACrBC,eAAe,EAAE,kBAAkB;IACnCC,sBAAsB,EAAE,2BAA2B;IACnDC,sBAAsB,EAAE,0BAA0B;IAClDC,sBAAsB,EAAE,0BAA0B;IAClDC,aAAa,EAAE;EACjB,CAAC;EAED;EACA1R,KAAK,EAAE;IACL2R,QAAQ,EAAE,gBAAgB;IAC1BC,QAAQ,EAAE,cAAc;IACxBC,WAAW,EAAE,cAAc;IAC3BC,YAAY,EAAE,qBAAqB;IACnCC,SAAS,EAAE,kBAAkB;IAC7BC,YAAY,EAAE;EAChB,CAAC;EAED;EACAnP,QAAQ,EAAE;IACRoP,KAAK,EAAE,sBAAsB;IAC7BvQ,KAAK,EAAE,OAAO;IACdD,IAAI,EAAE,MAAM;IACZyQ,GAAG,EAAE,KAAK;IACV1Q,KAAK,EAAE,OAAO;IACd2Q,UAAU,EAAE,8BAA8B;IAC1CC,QAAQ,EAAE,uBAAuB;IACjClF,0BAA0B,EAAE,yCAAyC;IACrExF,eAAe,EAAE,yBAAyB;IAC1C2K,eAAe,EAAE,4BAA4B;IAC7CC,YAAY,EAAE,eAAe;IAC7BC,YAAY,EAAE,2BAA2B;IACzCC,eAAe,EAAE,wCAAwC;IACzDC,WAAW,EAAE,8CAA8C;IAC3DC,SAAS,EAAE,oEAAoE;IAC/EC,SAAS,EAAE;EAEb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}