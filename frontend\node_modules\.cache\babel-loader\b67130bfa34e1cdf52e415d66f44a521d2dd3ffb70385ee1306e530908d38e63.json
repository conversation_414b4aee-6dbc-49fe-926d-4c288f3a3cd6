{"ast": null, "code": "import axios from 'axios';\nexport default {\n  name: 'EmailTemplates',\n  data() {\n    return {\n      isMobile: false,\n      templateList: [],\n      templateDialogVisible: false,\n      templateDialogTitle: '新增模板',\n      templateForm: {\n        id: null,\n        name: '',\n        template_key: '',\n        subject: '',\n        content_html: '',\n        language: 'zh_CN'\n      },\n      templateRules: {\n        name: [{\n          required: true,\n          message: '请输入模板名称',\n          trigger: 'blur'\n        }],\n        template_key: [{\n          required: true,\n          message: '请输入模板键名',\n          trigger: 'blur'\n        }],\n        subject: [{\n          required: true,\n          message: '请输入主题',\n          trigger: 'blur'\n        }],\n        content_html: [{\n          required: true,\n          message: '请输入HTML内容',\n          trigger: 'blur'\n        }],\n        language: [{\n          required: true,\n          message: '请选择语言',\n          trigger: 'change'\n        }]\n      },\n      editRow: null,\n      editCache: {}\n    };\n  },\n  created() {\n    this.checkMobile();\n    this.fetchTemplates();\n  },\n  mounted() {\n    window.addEventListener('resize', this.checkMobile);\n  },\n  beforeDestroy() {\n    window.removeEventListener('resize', this.checkMobile);\n  },\n  methods: {\n    checkMobile() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    async fetchTemplates() {\n      try {\n        const res = await axios.get('/api/admin/email/templates');\n        this.templateList = res.data;\n      } catch (e) {\n        this.$message.error('获取模板失败');\n      }\n    },\n    openTemplateDialog(row) {\n      if (row) {\n        this.templateDialogTitle = '编辑模板';\n        this.templateForm = {\n          ...row\n        };\n      } else {\n        this.templateDialogTitle = '新增模板';\n        this.templateForm = {\n          id: null,\n          name: '',\n          template_key: '',\n          subject: '',\n          content_html: '',\n          language: 'zh_CN'\n        };\n      }\n      this.templateDialogVisible = true;\n    },\n    async saveTemplate() {\n      this.$refs.templateFormRef.validate(async valid => {\n        if (!valid) return;\n        try {\n          if (this.templateForm.id) {\n            await axios.put(`/api/admin/email/templates/${this.templateForm.id}`, this.templateForm);\n            this.$message.success('更新成功');\n          } else {\n            await axios.post('/api/admin/email/templates', this.templateForm);\n            this.$message.success('新增成功');\n          }\n          this.templateDialogVisible = false;\n          this.fetchTemplates();\n        } catch (e) {\n          this.$message.error(e.response?.data?.detail || '保存失败');\n        }\n      });\n    },\n    async deleteTemplate(row) {\n      this.$confirm('确定要删除该模板吗？', '提示', {\n        type: 'warning'\n      }).then(async () => {\n        await axios.delete(`/api/admin/email/templates/${row.id}`);\n        this.$message.success('删除成功');\n        this.fetchTemplates();\n      }).catch(() => {});\n    },\n    startEdit(row) {\n      this.editRow = row.id;\n      this.editCache = {\n        ...row\n      };\n    },\n    cancelEdit() {\n      this.editRow = null;\n      this.editCache = {};\n    },\n    async saveEdit(row) {\n      try {\n        await this.$confirm('确定保存修改吗？', '提示', {\n          type: 'warning'\n        });\n        await axios.put(`/api/admin/email/templates/${row.id}`, this.editCache);\n        this.$message.success('保存成功');\n        this.editRow = null;\n        this.editCache = {};\n        this.fetchTemplates();\n      } catch (e) {\n        this.$message.error(e.response?.data?.detail || '保存失败');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "name", "data", "isMobile", "templateList", "templateDialogVisible", "templateDialogTitle", "templateForm", "id", "template_key", "subject", "content_html", "language", "templateRules", "required", "message", "trigger", "editRow", "editCache", "created", "checkMobile", "fetchTemplates", "mounted", "window", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "innerWidth", "res", "get", "e", "$message", "error", "openTemplateDialog", "row", "saveTemplate", "$refs", "templateFormRef", "validate", "valid", "put", "success", "post", "response", "detail", "deleteTemplate", "$confirm", "type", "then", "delete", "catch", "startEdit", "cancelEdit", "saveEdit"], "sources": ["src/views/admin/EmailTemplates.vue"], "sourcesContent": ["<template>\r\n  <div class=\"email-templates\">\r\n    <!-- 移动端提示 -->\r\n    <div v-if=\"isMobile\" class=\"mobile-notice\">\r\n      <el-card shadow=\"hover\">\r\n        <div style=\"text-align: center; padding: 40px 20px;\">\r\n          <i class=\"el-icon-document\" style=\"font-size: 48px; color: #409EFF; margin-bottom: 20px;\"></i>\r\n          <h3>邮件模板</h3>\r\n          <p style=\"color: #666; margin: 20px 0;\">\r\n            邮件模板管理功能需要在桌面端使用，以获得更好的编辑体验。\r\n          </p>\r\n          <p style=\"color: #666; font-size: 14px;\">\r\n            请使用电脑或平板设备访问此功能。\r\n          </p>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <div v-else>\r\n      <div style=\"margin-bottom: 10px;\">\r\n        <el-button type=\"primary\" @click=\"openTemplateDialog()\">新增模板</el-button>\r\n      </div>\r\n    <el-table :data=\"templateList\" border style=\"width: 100%\">\r\n      <el-table-column prop=\"name\" label=\"模板名称\" min-width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"editRow !== scope.row.id\">{{ scope.row.name }}</span>\r\n          <el-input v-else v-model=\"editCache.name\" size=\"small\"></el-input>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"template_key\" label=\"模板键名\" min-width=\"120\"/>\r\n      <el-table-column prop=\"subject\" label=\"主题\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"editRow !== scope.row.id\">{{ scope.row.subject }}</span>\r\n          <el-input v-else v-model=\"editCache.subject\" size=\"small\"></el-input>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"language\" label=\"语言\" min-width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"editRow !== scope.row.id\">{{ scope.row.language }}</span>\r\n          <el-select v-else v-model=\"editCache.language\" size=\"small\" style=\"width:100px\">\r\n            <el-option label=\"中文\" value=\"zh_CN\"/>\r\n            <el-option label=\"English\" value=\"en\"/>\r\n          </el-select>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"content_html\" label=\"HTML内容\" min-width=\"250\">\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"editRow !== scope.row.id\" style=\"white-space:pre-line;word-break:break-all;max-width:400px;display:inline-block;\">\r\n            {{ scope.row.content_html }}\r\n          </span>\r\n          <el-input\r\n            v-else\r\n            type=\"textarea\"\r\n            v-model=\"editCache.content_html\"\r\n            :rows=\"4\"\r\n            size=\"small\"\r\n          ></el-input>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <template v-if=\"editRow === scope.row.id\">\r\n            <el-button type=\"primary\" size=\"mini\" @click=\"saveEdit(scope.row)\">保存</el-button>\r\n            <el-button size=\"mini\" @click=\"cancelEdit\">取消</el-button>\r\n          </template>\r\n          <template v-else>\r\n            <el-button type=\"text\" @click=\"startEdit(scope.row)\">编辑</el-button>\r\n            <el-button type=\"text\" style=\"color:red\" @click=\"deleteTemplate(scope.row)\">删除</el-button>\r\n          </template>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <el-dialog :title=\"templateDialogTitle\" :visible.sync=\"templateDialogVisible\" width=\"600px\">\r\n      <el-form :model=\"templateForm\" label-width=\"100px\" :rules=\"templateRules\" ref=\"templateFormRef\">\r\n        <el-form-item label=\"模板名称\" prop=\"name\">\r\n          <el-input v-model=\"templateForm.name\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"模板键名\" prop=\"template_key\">\r\n          <el-input v-model=\"templateForm.template_key\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"主题\" prop=\"subject\">\r\n          <el-input v-model=\"templateForm.subject\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"语言\" prop=\"language\">\r\n          <el-select v-model=\"templateForm.language\" placeholder=\"请选择\">\r\n            <el-option label=\"中文\" value=\"zh_CN\"/>\r\n            <el-option label=\"English\" value=\"en\"/>\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item label=\"HTML内容\" prop=\"content_html\">\r\n          <el-input type=\"textarea\" v-model=\"templateForm.content_html\" :rows=\"8\" placeholder=\"支持Jinja2变量，如 reservation.user_name\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"templateDialogVisible = false\">取消</el-button>\r\n        <el-button type=\"primary\" @click=\"saveTemplate\">保存</el-button>\r\n      </div>\r\n    </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EmailTemplates',\r\n  data() {\r\n    return {\r\n      isMobile: false,\r\n      templateList: [],\r\n      templateDialogVisible: false,\r\n      templateDialogTitle: '新增模板',\r\n      templateForm: {\r\n        id: null,\r\n        name: '',\r\n        template_key: '',\r\n        subject: '',\r\n        content_html: '',\r\n        language: 'zh_CN'\r\n      },\r\n      templateRules: {\r\n        name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],\r\n        template_key: [{ required: true, message: '请输入模板键名', trigger: 'blur' }],\r\n        subject: [{ required: true, message: '请输入主题', trigger: 'blur' }],\r\n        content_html: [{ required: true, message: '请输入HTML内容', trigger: 'blur' }],\r\n        language: [{ required: true, message: '请选择语言', trigger: 'change' }]\r\n      },\r\n      editRow: null,\r\n      editCache: {}\r\n    }\r\n  },\r\n  created() {\r\n    this.checkMobile()\r\n    this.fetchTemplates()\r\n  },\r\n\r\n  mounted() {\r\n    window.addEventListener('resize', this.checkMobile)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    window.removeEventListener('resize', this.checkMobile)\r\n  },\r\n  methods: {\r\n    checkMobile() {\r\n      this.isMobile = window.innerWidth <= 768\r\n    },\r\n\r\n    async fetchTemplates() {\r\n      try {\r\n        const res = await axios.get('/api/admin/email/templates')\r\n        this.templateList = res.data\r\n      } catch (e) {\r\n        this.$message.error('获取模板失败')\r\n      }\r\n    },\r\n    openTemplateDialog(row) {\r\n      if (row) {\r\n        this.templateDialogTitle = '编辑模板'\r\n        this.templateForm = { ...row }\r\n      } else {\r\n        this.templateDialogTitle = '新增模板'\r\n        this.templateForm = {\r\n          id: null,\r\n          name: '',\r\n          template_key: '',\r\n          subject: '',\r\n          content_html: '',\r\n          language: 'zh_CN'\r\n        }\r\n      }\r\n      this.templateDialogVisible = true\r\n    },\r\n    async saveTemplate() {\r\n      this.$refs.templateFormRef.validate(async valid => {\r\n        if (!valid) return\r\n        try {\r\n          if (this.templateForm.id) {\r\n            await axios.put(`/api/admin/email/templates/${this.templateForm.id}`, this.templateForm)\r\n            this.$message.success('更新成功')\r\n          } else {\r\n            await axios.post('/api/admin/email/templates', this.templateForm)\r\n            this.$message.success('新增成功')\r\n          }\r\n          this.templateDialogVisible = false\r\n          this.fetchTemplates()\r\n        } catch (e) {\r\n          this.$message.error(e.response?.data?.detail || '保存失败')\r\n        }\r\n      })\r\n    },\r\n    async deleteTemplate(row) {\r\n      this.$confirm('确定要删除该模板吗？', '提示', { type: 'warning' })\r\n        .then(async () => {\r\n          await axios.delete(`/api/admin/email/templates/${row.id}`)\r\n          this.$message.success('删除成功')\r\n          this.fetchTemplates()\r\n        })\r\n        .catch(() => {})\r\n    },\r\n    startEdit(row) {\r\n      this.editRow = row.id\r\n      this.editCache = { ...row }\r\n    },\r\n    cancelEdit() {\r\n      this.editRow = null\r\n      this.editCache = {}\r\n    },\r\n    async saveEdit(row) {\r\n      try {\r\n        await this.$confirm('确定保存修改吗？', '提示', { type: 'warning' })\r\n        await axios.put(`/api/admin/email/templates/${row.id}`, this.editCache)\r\n        this.$message.success('保存成功')\r\n        this.editRow = null\r\n        this.editCache = {}\r\n        this.fetchTemplates()\r\n      } catch (e) {\r\n        this.$message.error(e.response?.data?.detail || '保存失败')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.email-templates {\r\n  padding: 20px;\r\n}\r\n\r\n.mobile-notice {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.mobile-notice .el-alert {\r\n  border-radius: 8px;\r\n}\r\n\r\n.mobile-notice p {\r\n  margin: 8px 0;\r\n  line-height: 1.6;\r\n}\r\n</style>"], "mappings": "AAwGA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,YAAA;MACAC,qBAAA;MACAC,mBAAA;MACAC,YAAA;QACAC,EAAA;QACAP,IAAA;QACAQ,YAAA;QACAC,OAAA;QACAC,YAAA;QACAC,QAAA;MACA;MACAC,aAAA;QACAZ,IAAA;UAAAa,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAP,YAAA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAN,OAAA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAL,YAAA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;QACAJ,QAAA;UAAAE,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MACA;MACAC,OAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,WAAA;IACA,KAAAC,cAAA;EACA;EAEAC,QAAA;IACAC,MAAA,CAAAC,gBAAA,gBAAAJ,WAAA;EACA;EAEAK,cAAA;IACAF,MAAA,CAAAG,mBAAA,gBAAAN,WAAA;EACA;EACAO,OAAA;IACAP,YAAA;MACA,KAAAjB,QAAA,GAAAoB,MAAA,CAAAK,UAAA;IACA;IAEA,MAAAP,eAAA;MACA;QACA,MAAAQ,GAAA,SAAA7B,KAAA,CAAA8B,GAAA;QACA,KAAA1B,YAAA,GAAAyB,GAAA,CAAA3B,IAAA;MACA,SAAA6B,CAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IACAC,mBAAAC,GAAA;MACA,IAAAA,GAAA;QACA,KAAA7B,mBAAA;QACA,KAAAC,YAAA;UAAA,GAAA4B;QAAA;MACA;QACA,KAAA7B,mBAAA;QACA,KAAAC,YAAA;UACAC,EAAA;UACAP,IAAA;UACAQ,YAAA;UACAC,OAAA;UACAC,YAAA;UACAC,QAAA;QACA;MACA;MACA,KAAAP,qBAAA;IACA;IACA,MAAA+B,aAAA;MACA,KAAAC,KAAA,CAAAC,eAAA,CAAAC,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;QACA;UACA,SAAAjC,YAAA,CAAAC,EAAA;YACA,MAAAR,KAAA,CAAAyC,GAAA,oCAAAlC,YAAA,CAAAC,EAAA,SAAAD,YAAA;YACA,KAAAyB,QAAA,CAAAU,OAAA;UACA;YACA,MAAA1C,KAAA,CAAA2C,IAAA,oCAAApC,YAAA;YACA,KAAAyB,QAAA,CAAAU,OAAA;UACA;UACA,KAAArC,qBAAA;UACA,KAAAgB,cAAA;QACA,SAAAU,CAAA;UACA,KAAAC,QAAA,CAAAC,KAAA,CAAAF,CAAA,CAAAa,QAAA,EAAA1C,IAAA,EAAA2C,MAAA;QACA;MACA;IACA;IACA,MAAAC,eAAAX,GAAA;MACA,KAAAY,QAAA;QAAAC,IAAA;MAAA,GACAC,IAAA;QACA,MAAAjD,KAAA,CAAAkD,MAAA,+BAAAf,GAAA,CAAA3B,EAAA;QACA,KAAAwB,QAAA,CAAAU,OAAA;QACA,KAAArB,cAAA;MACA,GACA8B,KAAA;IACA;IACAC,UAAAjB,GAAA;MACA,KAAAlB,OAAA,GAAAkB,GAAA,CAAA3B,EAAA;MACA,KAAAU,SAAA;QAAA,GAAAiB;MAAA;IACA;IACAkB,WAAA;MACA,KAAApC,OAAA;MACA,KAAAC,SAAA;IACA;IACA,MAAAoC,SAAAnB,GAAA;MACA;QACA,WAAAY,QAAA;UAAAC,IAAA;QAAA;QACA,MAAAhD,KAAA,CAAAyC,GAAA,+BAAAN,GAAA,CAAA3B,EAAA,SAAAU,SAAA;QACA,KAAAc,QAAA,CAAAU,OAAA;QACA,KAAAzB,OAAA;QACA,KAAAC,SAAA;QACA,KAAAG,cAAA;MACA,SAAAU,CAAA;QACA,KAAAC,QAAA,CAAAC,KAAA,CAAAF,CAAA,CAAAa,QAAA,EAAA1C,IAAA,EAAA2C,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}