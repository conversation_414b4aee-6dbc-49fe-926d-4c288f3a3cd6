{"ast": null, "code": "import { mapGetters } from 'vuex';\nimport { categoryApi } from '@/api';\nexport default {\n  name: 'AdminCategory',\n  data() {\n    return {\n      loading: false,\n      submitting: false,\n      categories: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 10,\n      filter: {\n        search: ''\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n      dialogVisible: false,\n      dialogType: 'add',\n      // 'add' or 'edit'\n      form: {\n        id: null,\n        name: '',\n        description: ''\n      },\n      rules: {\n        name: [{\n          required: true,\n          message: '请输入类别名称',\n          trigger: 'blur'\n        }, {\n          min: 1,\n          max: 50,\n          message: '长度在 1 到 50 个字符',\n          trigger: 'blur'\n        }]\n      }\n    };\n  },\n  computed: {\n    ...mapGetters(['getToken']),\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile ? 'prev, next' : 'total, prev, pager, next';\n    }\n  },\n  created() {\n    this.fetchData();\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 获取类别列表\n    async fetchData() {\n      try {\n        this.loading = true;\n        const params = {\n          skip: (this.currentPage - 1) * this.pageSize,\n          limit: this.pageSize,\n          search: this.filter.search || undefined\n        };\n        const response = await categoryApi.getCategories(params);\n        this.categories = response.data.items;\n        this.total = response.data.total;\n      } catch (error) {\n        console.error('获取类别列表失败:', error);\n        this.$message.error('获取类别列表失败');\n      } finally {\n        this.loading = false;\n      }\n    },\n    // 处理筛选条件变化\n    handleFilterChange() {\n      this.currentPage = 1;\n      this.fetchData();\n    },\n    // 处理页码变化\n    handlePageChange(page) {\n      this.currentPage = page;\n      this.fetchData();\n    },\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    // 添加类别\n    handleAdd() {\n      this.dialogType = 'add';\n      this.form = {\n        id: null,\n        name: '',\n        description: ''\n      };\n      this.dialogVisible = true;\n    },\n    // 编辑类别\n    handleEdit(row) {\n      this.dialogType = 'edit';\n      this.form = {\n        ...row\n      };\n      this.dialogVisible = true;\n    },\n    // 删除类别\n    handleDelete(row) {\n      this.$confirm('确定要删除该类别吗？删除后不可恢复，且可能影响已使用该类别的设备。', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          this.loading = true;\n          await categoryApi.deleteCategory(row.id);\n          this.$message.success('类别已删除');\n          this.fetchData();\n        } catch (error) {\n          console.error('删除类别失败:', error);\n          this.$message.error('删除类别失败');\n        } finally {\n          this.loading = false;\n        }\n      }).catch(() => {\n        // 取消删除，不做任何处理\n      });\n    },\n    // 提交表单\n    submitForm() {\n      this.$refs.form.validate(async valid => {\n        if (!valid) return;\n        try {\n          this.submitting = true;\n          if (this.dialogType === 'add') {\n            // 创建类别\n            await categoryApi.createCategory(this.form);\n            this.$message.success('类别添加成功');\n          } else {\n            // 更新类别\n            await categoryApi.updateCategory(this.form.id, this.form);\n            this.$message.success('类别更新成功');\n          }\n          this.dialogVisible = false;\n          this.fetchData();\n        } catch (error) {\n          console.error('保存类别失败:', error);\n          if (error.response && error.response.data && error.response.data.detail) {\n            this.$message.error(error.response.data.detail);\n          } else {\n            this.$message.error('保存类别失败');\n          }\n        } finally {\n          this.submitting = false;\n        }\n      });\n    },\n    // 重置表单\n    resetForm() {\n      if (this.$refs.form) {\n        this.$refs.form.resetFields();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "categoryApi", "name", "data", "loading", "submitting", "categories", "total", "currentPage", "pageSize", "filter", "search", "isMobile", "window", "innerWidth", "dialogVisible", "dialogType", "form", "id", "description", "rules", "required", "message", "trigger", "min", "max", "computed", "paginationLayout", "created", "fetchData", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "params", "skip", "limit", "undefined", "response", "getCategories", "items", "error", "console", "$message", "handleFilterChange", "handlePageChange", "page", "handleAdd", "handleEdit", "row", "handleDelete", "$confirm", "confirmButtonText", "cancelButtonText", "type", "then", "deleteCategory", "success", "catch", "submitForm", "$refs", "validate", "valid", "createCategory", "updateCategory", "detail", "resetForm", "resetFields"], "sources": ["src/views/admin/AdminCategory.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-category\">\n    <h1 class=\"page-title\">设备类别管理</h1>\n\n    <!-- 操作栏 -->\n    <div class=\"action-bar\">\n      <el-button type=\"primary\" @click=\"handleAdd\">\n        <i class=\"el-icon-plus\"></i> 添加类别\n      </el-button>\n\n      <div class=\"search-box\">\n        <el-input\n          v-model=\"filter.search\"\n          placeholder=\"搜索类别名称\"\n          clearable\n          @clear=\"handleFilterChange\"\n          @keyup.enter.native=\"handleFilterChange\"\n        >\n          <el-button\n            slot=\"append\"\n            icon=\"el-icon-search\"\n            @click=\"handleFilterChange\"\n          ></el-button>\n        </el-input>\n      </div>\n    </div>\n\n    <!-- 移动端卡片视图 -->\n    <div v-if=\"isMobile\" class=\"mobile-card-container\">\n      <div\n        v-for=\"category in categories\"\n        :key=\"category.id\"\n        class=\"category-mobile-card\"\n      >\n        <div class=\"card-header\">\n          <div class=\"category-info\">\n            <div class=\"category-id\">ID: {{ category.id }}</div>\n            <div class=\"category-name\">{{ category.name }}</div>\n          </div>\n        </div>\n\n        <div class=\"card-content\">\n          <div class=\"info-row\">\n            <span class=\"label\">描述:</span>\n            <span class=\"value\">{{ category.description || '无' }}</span>\n          </div>\n        </div>\n\n        <div class=\"card-actions\">\n          <el-button\n            type=\"primary\"\n            size=\"small\"\n            @click=\"handleEdit(category)\"\n          >\n            编辑\n          </el-button>\n          <el-button\n            type=\"danger\"\n            size=\"small\"\n            @click=\"handleDelete(category)\"\n          >\n            删除\n          </el-button>\n        </div>\n      </div>\n    </div>\n\n    <!-- 桌面端表格视图 -->\n    <el-table\n      v-else\n      v-loading=\"loading\"\n      :data=\"categories\"\n      border\n      stripe\n      style=\"width: 100%\"\n      header-align=\"center\"\n      cell-class-name=\"text-center\"\n    >\n      <el-table-column\n        prop=\"id\"\n        label=\"ID\"\n        width=\"80\"\n      ></el-table-column>\n\n      <el-table-column\n        prop=\"name\"\n        label=\"类别名称\"\n        min-width=\"150\"\n      ></el-table-column>\n\n      <el-table-column\n        prop=\"description\"\n        label=\"描述\"\n        min-width=\"250\"\n      >\n        <template slot-scope=\"scope\">\n          {{ scope.row.description || '无' }}\n        </template>\n      </el-table-column>\n\n      <el-table-column\n        label=\"操作\"\n        width=\"200\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-button\n            size=\"mini\"\n            type=\"primary\"\n            @click=\"handleEdit(scope.row)\"\n          >\n            编辑\n          </el-button>\n          <el-button\n            size=\"mini\"\n            type=\"danger\"\n            @click=\"handleDelete(scope.row)\"\n          >\n            删除\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        background\n        :layout=\"paginationLayout\"\n        :total=\"total\"\n        :current-page.sync=\"currentPage\"\n        :page-size=\"pageSize\"\n        @current-change=\"handlePageChange\"\n      ></el-pagination>\n    </div>\n\n    <!-- 添加/编辑类别对话框 -->\n    <el-dialog\n      :title=\"dialogType === 'add' ? '添加类别' : '编辑类别'\"\n      :visible.sync=\"dialogVisible\"\n      width=\"40%\"\n      @close=\"resetForm\"\n    >\n      <el-form\n        ref=\"form\"\n        :model=\"form\"\n        :rules=\"rules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"类别名称\" prop=\"name\">\n          <el-input v-model=\"form.name\"></el-input>\n        </el-form-item>\n\n        <el-form-item label=\"描述\" prop=\"description\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"form.description\"\n            :rows=\"4\"\n          ></el-input>\n        </el-form-item>\n      </el-form>\n\n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"dialogVisible = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">保存</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { mapGetters } from 'vuex'\nimport { categoryApi } from '@/api'\n\nexport default {\n  name: 'AdminCategory',\n\n  data() {\n    return {\n      loading: false,\n      submitting: false,\n      categories: [],\n      total: 0,\n      currentPage: 1,\n      pageSize: 10,\n      filter: {\n        search: ''\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768,\n\n      dialogVisible: false,\n      dialogType: 'add', // 'add' or 'edit'\n      form: {\n        id: null,\n        name: '',\n        description: ''\n      },\n      rules: {\n        name: [\n          { required: true, message: '请输入类别名称', trigger: 'blur' },\n          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n\n  computed: {\n    ...mapGetters(['getToken']),\n\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile\n        ? 'prev, next'\n        : 'total, prev, pager, next';\n    }\n  },\n\n  created() {\n    this.fetchData()\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize)\n  },\n\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize)\n  },\n\n  methods: {\n    // 获取类别列表\n    async fetchData() {\n      try {\n        this.loading = true\n\n        const params = {\n          skip: (this.currentPage - 1) * this.pageSize,\n          limit: this.pageSize,\n          search: this.filter.search || undefined\n        }\n\n        const response = await categoryApi.getCategories(params)\n        this.categories = response.data.items\n        this.total = response.data.total\n      } catch (error) {\n        console.error('获取类别列表失败:', error)\n        this.$message.error('获取类别列表失败')\n      } finally {\n        this.loading = false\n      }\n    },\n\n    // 处理筛选条件变化\n    handleFilterChange() {\n      this.currentPage = 1\n      this.fetchData()\n    },\n\n    // 处理页码变化\n    handlePageChange(page) {\n      this.currentPage = page\n      this.fetchData()\n    },\n\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768\n    },\n\n    // 添加类别\n    handleAdd() {\n      this.dialogType = 'add'\n      this.form = {\n        id: null,\n        name: '',\n        description: ''\n      }\n      this.dialogVisible = true\n    },\n\n    // 编辑类别\n    handleEdit(row) {\n      this.dialogType = 'edit'\n      this.form = { ...row }\n      this.dialogVisible = true\n    },\n\n    // 删除类别\n    handleDelete(row) {\n      this.$confirm(\n        '确定要删除该类别吗？删除后不可恢复，且可能影响已使用该类别的设备。',\n        '警告',\n        {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          type: 'warning'\n        }\n      ).then(async () => {\n        try {\n          this.loading = true\n\n          await categoryApi.deleteCategory(row.id)\n\n          this.$message.success('类别已删除')\n          this.fetchData()\n        } catch (error) {\n          console.error('删除类别失败:', error)\n          this.$message.error('删除类别失败')\n        } finally {\n          this.loading = false\n        }\n      }).catch(() => {\n        // 取消删除，不做任何处理\n      })\n    },\n\n    // 提交表单\n    submitForm() {\n      this.$refs.form.validate(async valid => {\n        if (!valid) return\n\n        try {\n          this.submitting = true\n\n          if (this.dialogType === 'add') {\n            // 创建类别\n            await categoryApi.createCategory(this.form)\n            this.$message.success('类别添加成功')\n          } else {\n            // 更新类别\n            await categoryApi.updateCategory(this.form.id, this.form)\n            this.$message.success('类别更新成功')\n          }\n\n          this.dialogVisible = false\n          this.fetchData()\n        } catch (error) {\n          console.error('保存类别失败:', error)\n          if (error.response && error.response.data && error.response.data.detail) {\n            this.$message.error(error.response.data.detail)\n          } else {\n            this.$message.error('保存类别失败')\n          }\n        } finally {\n          this.submitting = false\n        }\n      })\n    },\n\n    // 重置表单\n    resetForm() {\n      if (this.$refs.form) {\n        this.$refs.form.resetFields()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.admin-category {\n  padding: 20px;\n  width: 100%;\n  max-width: 100%;\n}\n\n.page-title {\n  margin-bottom: 20px;\n  font-size: 24px;\n  color: #303133;\n}\n\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20px;\n}\n\n.search-box {\n  width: 300px;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  text-align: right;\n}\n\n/* 移动端卡片样式 */\n.mobile-card-container {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.category-mobile-card {\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  padding: 15px;\n  border: 1px solid #e8e8e8;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  margin-bottom: 15px;\n  padding-bottom: 10px;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.category-info {\n  flex: 1;\n}\n\n.category-id {\n  font-size: 12px;\n  color: #666;\n  margin-bottom: 5px;\n}\n\n.category-name {\n  font-weight: bold;\n  font-size: 14px;\n  color: #333;\n}\n\n.card-content {\n  margin-bottom: 15px;\n}\n\n.info-row {\n  display: flex;\n  margin-bottom: 8px;\n  align-items: flex-start;\n}\n\n.info-row .label {\n  min-width: 50px;\n  font-size: 13px;\n  color: #666;\n  margin-right: 10px;\n  flex-shrink: 0;\n}\n\n.info-row .value {\n  font-size: 13px;\n  color: #333;\n  flex: 1;\n  word-break: break-all;\n}\n\n.card-actions {\n  text-align: center;\n  padding-top: 10px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.card-actions .el-button {\n  margin: 0 5px;\n}\n\n.text-center {\n  text-align: center !important;\n}\n\n@media (max-width: 768px) {\n  .admin-category {\n    padding: 10px;\n    padding-top: 150px !important;\n    padding-bottom: 100px !important;\n  }\n\n  .admin-category h1 {\n    margin-top: 80px !important;\n  }\n\n  .action-bar {\n    flex-direction: column;\n    gap: 10px;\n  }\n\n  .search-box {\n    width: 100%;\n  }\n}\n</style>\n"], "mappings": "AA2KA,SAAAA,UAAA;AACA,SAAAC,WAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,OAAA;MACAC,UAAA;MACAC,UAAA;MACAC,KAAA;MACAC,WAAA;MACAC,QAAA;MACAC,MAAA;QACAC,MAAA;MACA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;MAEAC,aAAA;MACAC,UAAA;MAAA;MACAC,IAAA;QACAC,EAAA;QACAhB,IAAA;QACAiB,WAAA;MACA;MACAC,KAAA;QACAlB,IAAA,GACA;UAAAmB,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EAEAG,QAAA;IACA,GAAA1B,UAAA;IAEA;IACA2B,iBAAA;MACA,YAAAf,QAAA,GACA,eACA;IACA;EACA;EAEAgB,QAAA;IACA,KAAAC,SAAA;IACA;IACAhB,MAAA,CAAAiB,gBAAA,gBAAAC,YAAA;EACA;EAEAC,cAAA;IACA;IACAnB,MAAA,CAAAoB,mBAAA,gBAAAF,YAAA;EACA;EAEAG,OAAA;IACA;IACA,MAAAL,UAAA;MACA;QACA,KAAAzB,OAAA;QAEA,MAAA+B,MAAA;UACAC,IAAA,QAAA5B,WAAA,aAAAC,QAAA;UACA4B,KAAA,OAAA5B,QAAA;UACAE,MAAA,OAAAD,MAAA,CAAAC,MAAA,IAAA2B;QACA;QAEA,MAAAC,QAAA,SAAAtC,WAAA,CAAAuC,aAAA,CAAAL,MAAA;QACA,KAAA7B,UAAA,GAAAiC,QAAA,CAAApC,IAAA,CAAAsC,KAAA;QACA,KAAAlC,KAAA,GAAAgC,QAAA,CAAApC,IAAA,CAAAI,KAAA;MACA,SAAAmC,KAAA;QACAC,OAAA,CAAAD,KAAA,cAAAA,KAAA;QACA,KAAAE,QAAA,CAAAF,KAAA;MACA;QACA,KAAAtC,OAAA;MACA;IACA;IAEA;IACAyC,mBAAA;MACA,KAAArC,WAAA;MACA,KAAAqB,SAAA;IACA;IAEA;IACAiB,iBAAAC,IAAA;MACA,KAAAvC,WAAA,GAAAuC,IAAA;MACA,KAAAlB,SAAA;IACA;IAEA;IACAE,aAAA;MACA,KAAAnB,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IAEA;IACAkC,UAAA;MACA,KAAAhC,UAAA;MACA,KAAAC,IAAA;QACAC,EAAA;QACAhB,IAAA;QACAiB,WAAA;MACA;MACA,KAAAJ,aAAA;IACA;IAEA;IACAkC,WAAAC,GAAA;MACA,KAAAlC,UAAA;MACA,KAAAC,IAAA;QAAA,GAAAiC;MAAA;MACA,KAAAnC,aAAA;IACA;IAEA;IACAoC,aAAAD,GAAA;MACA,KAAAE,QAAA,CACA,qCACA,MACA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,CACA,EAAAC,IAAA;QACA;UACA,KAAApD,OAAA;UAEA,MAAAH,WAAA,CAAAwD,cAAA,CAAAP,GAAA,CAAAhC,EAAA;UAEA,KAAA0B,QAAA,CAAAc,OAAA;UACA,KAAA7B,SAAA;QACA,SAAAa,KAAA;UACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA;QACA;UACA,KAAAtC,OAAA;QACA;MACA,GAAAuD,KAAA;QACA;MAAA,CACA;IACA;IAEA;IACAC,WAAA;MACA,KAAAC,KAAA,CAAA5C,IAAA,CAAA6C,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;QAEA;UACA,KAAA1D,UAAA;UAEA,SAAAW,UAAA;YACA;YACA,MAAAf,WAAA,CAAA+D,cAAA,MAAA/C,IAAA;YACA,KAAA2B,QAAA,CAAAc,OAAA;UACA;YACA;YACA,MAAAzD,WAAA,CAAAgE,cAAA,MAAAhD,IAAA,CAAAC,EAAA,OAAAD,IAAA;YACA,KAAA2B,QAAA,CAAAc,OAAA;UACA;UAEA,KAAA3C,aAAA;UACA,KAAAc,SAAA;QACA,SAAAa,KAAA;UACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;UACA,IAAAA,KAAA,CAAAH,QAAA,IAAAG,KAAA,CAAAH,QAAA,CAAApC,IAAA,IAAAuC,KAAA,CAAAH,QAAA,CAAApC,IAAA,CAAA+D,MAAA;YACA,KAAAtB,QAAA,CAAAF,KAAA,CAAAA,KAAA,CAAAH,QAAA,CAAApC,IAAA,CAAA+D,MAAA;UACA;YACA,KAAAtB,QAAA,CAAAF,KAAA;UACA;QACA;UACA,KAAArC,UAAA;QACA;MACA;IACA;IAEA;IACA8D,UAAA;MACA,SAAAN,KAAA,CAAA5C,IAAA;QACA,KAAA4C,KAAA,CAAA5C,IAAA,CAAAmD,WAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}