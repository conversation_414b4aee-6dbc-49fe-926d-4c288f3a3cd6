{"ast": null, "code": "import { reservationApi, recurringReservation<PERSON>pi } from '@/api';\nimport { isReservationExpired } from '@/utils/date';\nexport default {\n  name: 'ReservationQuery',\n  data() {\n    return {\n      // 个人预约管理相关\n      personalLoading: false,\n      showInstructions: true,\n      notFound: false,\n      personalQueryForm: {\n        reservationCode: '',\n        userContact: ''\n      },\n      personalQueryResults: [],\n      // 新增：存放多条预约结果\n\n      // 分页相关\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 0,\n      personalQueryRules: {\n        reservationCode: [{\n          required: false,\n          message: this.$t('reservation.requiredField'),\n          trigger: 'blur'\n        }, {\n          min: 6,\n          max: 20,\n          message: this.$t('common.lengthLimit', {\n            min: 6,\n            max: 20\n          }),\n          trigger: 'blur'\n        }],\n        userContact: [{\n          required: false,\n          message: this.$t('reservation.requiredField'),\n          trigger: 'blur'\n        }]\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    };\n  },\n  computed: {\n    // 分页后的结果\n    paginatedResults() {\n      const start = (this.currentPage - 1) * this.pageSize;\n      const end = start + this.pageSize;\n      return this.personalQueryResults.slice(start, end);\n    },\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile ? 'prev, next' : 'total, sizes, prev, pager, next, jumper';\n    }\n  },\n  created() {\n    // 如果URL中有查询参数，自动填充并查询\n    const code = this.$route.query.code;\n    const reservationCode = this.$route.query.reservationCode;\n    const userContact = this.$route.query.userContact;\n    const autoQuery = this.$route.query.autoQuery;\n\n    // 优先使用 reservationCode 参数（从详情页面返回时传递的）\n    if (reservationCode) {\n      this.personalQueryForm.reservationCode = reservationCode;\n    } else if (code) {\n      this.personalQueryForm.reservationCode = code;\n    }\n    if (userContact) {\n      this.personalQueryForm.userContact = userContact;\n    }\n\n    // 如果有 autoQuery 标记或者有查询参数，自动执行查询\n    if (autoQuery === 'true' || code || reservationCode || userContact) {\n      this.$nextTick(() => {\n        this.handlePersonalQuery();\n      });\n    }\n\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    // 处理个人预约查询\n    handlePersonalQuery() {\n      // 自定义验证：预定码和联系方式至少填写一项\n      if (!this.personalQueryForm.reservationCode && !this.personalQueryForm.userContact) {\n        this.$message.error(this.$t('reservation.atLeastOneField'));\n        return false;\n      }\n      this.$refs.personalQueryForm.validate(async valid => {\n        if (!valid) {\n          return false;\n        }\n        this.personalLoading = true;\n        this.showInstructions = false;\n        this.notFound = false;\n        try {\n          // 如果有预定码，优先使用预定码查询\n          if (this.personalQueryForm.reservationCode) {\n            try {\n              // 先查普通预约\n              const response = await reservationApi.getReservationByCode(this.personalQueryForm.reservationCode);\n              if (response.data.success) {\n                // 普通预约，导航到预定详情页\n                console.log('找到普通预约，跳转到预约详情页:', this.personalQueryForm.reservationCode);\n                this.$router.push({\n                  path: `/reservation/${this.personalQueryForm.reservationCode}`,\n                  query: {\n                    code: this.personalQueryForm.reservationCode,\n                    from: 'query'\n                  }\n                });\n                return;\n              } else {\n                // 检查是否是循环预约\n                if (response.data.data && response.data.data.is_recurring === true && response.data.data.recurring_id) {\n                  // 这是一个循环预约，跳转到循环预约详情页\n                  console.log('检测到循环预约标记，跳转到循环预约详情页:', response.data.data.recurring_id);\n                  this.$router.push({\n                    path: `/recurring-reservation/${response.data.data.recurring_id}`,\n                    query: {\n                      code: this.personalQueryForm.reservationCode,\n                      from: 'query'\n                    }\n                  });\n                  return;\n                }\n\n                // 如果普通预约查不到，再查循环预约\n                try {\n                  const recurringResponse = await recurringReservationApi.getRecurringReservationByCode(this.personalQueryForm.reservationCode);\n                  if (recurringResponse.data.success) {\n                    // 导航到循环预约详情页\n                    console.log('找到循环预约，跳转到循环预约详情页:', recurringResponse.data.data.id);\n                    this.$router.push({\n                      path: `/recurring-reservation/${recurringResponse.data.data.id}`,\n                      query: {\n                        code: this.personalQueryForm.reservationCode,\n                        from: 'query'\n                      }\n                    });\n                    return;\n                  }\n                } catch (recurringError) {\n                  console.error('Failed to query by reservation code (recurring):', recurringError);\n                }\n              }\n            } catch (error) {\n              // 检查错误是否包含循环预约信息\n              if (error.response && error.response.data && error.response.data.is_recurring === true && error.response.data.recurring_id) {\n                // 这是一个循环预约，跳转到循环预约详情页\n                console.log('错误响应中检测到循环预约标记，跳转到循环预约详情页:', error.response.data.recurring_id);\n                this.$router.push({\n                  path: `/recurring-reservation/${error.response.data.recurring_id}`,\n                  query: {\n                    code: this.personalQueryForm.reservationCode,\n                    from: 'query'\n                  }\n                });\n                return;\n              }\n\n              // 普通预约接口报错时也查循环预约\n              try {\n                const recurringResponse = await recurringReservationApi.getRecurringReservationByCode(this.personalQueryForm.reservationCode);\n                if (recurringResponse.data.success) {\n                  // 导航到循环预约详情页\n                  console.log('找到循环预约，跳转到循环预约详情页:', recurringResponse.data.data.id);\n                  this.$router.push({\n                    path: `/recurring-reservation/${recurringResponse.data.data.id}`,\n                    query: {\n                      code: this.personalQueryForm.reservationCode,\n                      from: 'query'\n                    }\n                  });\n                  return;\n                }\n              } catch (recurringError) {\n                console.error('Failed to query by reservation code (recurring):', recurringError);\n              }\n            }\n          }\n\n          // 如果没有预定码或预定码查询失败，尝试使用联系方式查询\n          if (this.personalQueryForm.userContact) {\n            try {\n              // 这里需要调用后端接口根据联系方式查询预定\n              const response = await reservationApi.getReservations({\n                user_contact: this.personalQueryForm.userContact,\n                limit: 1000 // 设置一个足够大的limit来获取所有记录\n              });\n              if (response.data.items && response.data.items.length > 0) {\n                if (response.data.items.length === 1) {\n                  // 只有一条，直接跳转\n                  const firstReservation = response.data.items[0];\n                  this.$router.push({\n                    path: `/reservation/${firstReservation.reservation_code}`,\n                    query: {\n                      userContact: this.personalQueryForm.userContact,\n                      from: 'query'\n                    }\n                  });\n                  return;\n                } else {\n                  // 多条，展示表格并设置分页\n                  console.log('查询到多条预约结果，检查数据结构:', response.data.items);\n\n                  // 检查并修复数据中的字段混淆问题\n                  const fixedItems = response.data.items.map(item => {\n                    console.log('原始预约数据:', {\n                      id: item.id,\n                      reservation_code: item.reservation_code,\n                      reservation_number: item.reservation_number\n                    });\n\n                    // 如果 reservation_code 是预约序号格式，但 reservation_number 为空或不正确\n                    if (item.reservation_code && item.reservation_code.startsWith('RN-') && (!item.reservation_number || !item.reservation_number.startsWith('RN-'))) {\n                      console.log('检测到数据字段混淆，进行修复:', item.reservation_code);\n                      // 这种情况下，reservation_code 实际上是 reservation_number\n                      return {\n                        ...item,\n                        reservation_number: item.reservation_code\n                        // 保持原有的 reservation_code，如果它确实是预约序号，后续逻辑会处理\n                      };\n                    }\n                    return item;\n                  });\n                  this.personalQueryResults = fixedItems;\n                  this.totalResults = fixedItems.length;\n                  this.currentPage = 1; // 重置到第一页\n                  return;\n                }\n              }\n            } catch (contactError) {\n              console.error('Failed to query by contact:', contactError);\n            }\n          }\n\n          // 如果两种方式都查询失败，显示未找到\n          this.notFound = true;\n        } catch (error) {\n          console.error('Failed to query personal reservation:', error);\n          this.notFound = true;\n        } finally {\n          this.personalLoading = false;\n        }\n      });\n    },\n    // 格式化日期时间\n    formatDateTime(row, column, cellValue) {\n      if (!cellValue) return '';\n      const date = new Date(cellValue);\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;\n    },\n    // 获取状态类型\n    getStatusType(reservation) {\n      // 统一小写处理，兼容后端大小写不一致\n      const status = (reservation.status || '').toLowerCase();\n      console.log('status for switch:', reservation.status, '->', status);\n      switch (status) {\n        case 'cancelled':\n          return 'danger';\n        // 已取消 - 红色\n        case 'expired':\n          return 'warning';\n        // 已过期 - 橙色\n        case 'in_use':\n          return 'primary';\n        // 使用中 - 蓝色\n        case 'confirmed':\n          return 'success';\n        // 已确认 - 绿色\n        default:\n          return 'info';\n        // 其他状态 - 灰色\n      }\n    },\n    // 获取状态文本\n    getStatusText(reservation) {\n      // 统一小写处理，兼容后端大小写不一致\n      const status = (reservation.status || '').toLowerCase();\n      console.log('status for switch:', reservation.status, '->', status);\n      switch (status) {\n        case 'cancelled':\n          return this.$t('reservation.cancelled');\n        // 已取消\n        case 'expired':\n          return this.$t('reservation.expired');\n        // 已过期\n        case 'in_use':\n          return this.$t('reservation.inUse');\n        // 使用中\n        case 'confirmed':\n          return this.$t('reservation.confirmed');\n        // 已确认\n        default:\n          return reservation.status;\n        // 其他状态直接显示\n      }\n    },\n    // 重置表单\n    resetForm() {\n      // 手动清空表单数据\n      this.personalQueryForm.reservationCode = '';\n      this.personalQueryForm.userContact = '';\n\n      // 重置表单验证状态\n      this.$refs.personalQueryForm.clearValidate();\n      this.notFound = false;\n      this.showInstructions = true;\n      this.personalQueryResults = []; // 重置结果\n      this.totalResults = 0; // 重置总数\n      this.currentPage = 1; // 重置页码\n\n      // 清除URL参数，避免页面刷新时重新填充表单\n      if (this.$route.query.userContact || this.$route.query.reservationCode || this.$route.query.autoQuery) {\n        this.$router.replace({\n          path: this.$route.path,\n          query: {} // 清空所有查询参数\n        });\n      }\n    },\n    // 分页大小改变\n    handleSizeChange(newSize) {\n      this.pageSize = newSize;\n      this.currentPage = 1; // 重置到第一页\n    },\n    // 当前页改变\n    handleCurrentChange(newPage) {\n      console.log('分页改变:', newPage);\n      this.currentPage = newPage;\n    },\n    // 查看预约详情\n    async viewReservationDetail(reservation) {\n      console.log('查看预约详情:', reservation);\n      console.log('预约详情字段分析:', {\n        id: reservation.id,\n        reservation_code: reservation.reservation_code,\n        reservation_number: reservation.reservation_number,\n        reservation_code_type: reservation.reservation_code ? reservation.reservation_code.startsWith('RN-') ? '预约序号格式' : '预约码格式' : '空',\n        reservation_number_type: reservation.reservation_number ? reservation.reservation_number.startsWith('RN-') ? '预约序号格式' : '其他格式' : '空'\n      });\n      try {\n        // 检查是否有预约序号，如果有，说明是子预约，直接跳转到子预约详情页面\n        if (reservation.reservation_number) {\n          console.log('检测到预约序号，直接跳转到子预约详情页面:', reservation.reservation_number);\n\n          // 构建查询参数\n          const query = {\n            userContact: this.personalQueryForm.userContact,\n            reservationCode: this.personalQueryForm.reservationCode,\n            // 保留预定码\n            from: 'query'\n          };\n\n          // 如果有循环预约ID，添加到查询参数中\n          if (reservation.recurring_reservation_id) {\n            query.recurringId = reservation.recurring_reservation_id;\n            query.child = 'true';\n          }\n\n          // 跳转到子预约详情页面\n          this.$router.push({\n            path: `/reservation/number/${reservation.reservation_number}`,\n            query: query\n          });\n          return;\n        }\n\n        // 检查 reservation_code 是否实际上是预约序号（以RN-开头）\n        if (reservation.reservation_code && reservation.reservation_code.startsWith('RN-')) {\n          console.log('检测到reservation_code实际上是预约序号，直接跳转到子预约详情页面:', reservation.reservation_code);\n\n          // 构建查询参数\n          const query = {\n            userContact: this.personalQueryForm.userContact,\n            reservationCode: this.personalQueryForm.reservationCode,\n            // 保留预定码\n            from: 'query'\n          };\n\n          // 如果有循环预约ID，添加到查询参数中\n          if (reservation.recurring_reservation_id) {\n            query.recurringId = reservation.recurring_reservation_id;\n            query.child = 'true';\n          }\n\n          // 跳转到子预约详情页面，使用reservation_code作为预约序号\n          this.$router.push({\n            path: `/reservation/number/${reservation.reservation_code}`,\n            query: query\n          });\n          return;\n        }\n\n        // 如果没有预约序号，按原有逻辑处理\n        // 先查询预约详情，检查是否是循环预约\n        const response = await reservationApi.getReservationByCode(reservation.reservation_code);\n        if (response.data.success) {\n          // 普通预约，导航到预定详情页\n          console.log('找到普通预约，跳转到预约详情页:', reservation.reservation_code);\n          this.$router.push({\n            path: `/reservation/${reservation.reservation_code}`,\n            query: {\n              userContact: this.personalQueryForm.userContact,\n              reservationCode: this.personalQueryForm.reservationCode,\n              // 保留预定码\n              from: 'query'\n            }\n          });\n        } else {\n          // 检查是否是循环预约\n          if (response.data.data && response.data.data.is_recurring === true && response.data.data.recurring_id) {\n            // 这是一个循环预约，跳转到循环预约详情页\n            console.log('检测到循环预约标记，跳转到循环预约详情页:', response.data.data.recurring_id);\n            this.$router.push({\n              path: `/recurring-reservation/${response.data.data.recurring_id}`,\n              query: {\n                userContact: this.personalQueryForm.userContact,\n                reservationCode: this.personalQueryForm.reservationCode,\n                // 保留预定码\n                from: 'query'\n              }\n            });\n          } else {\n            // 如果普通预约查不到，再查循环预约\n            try {\n              const recurringResponse = await recurringReservationApi.getRecurringReservationByCode(reservation.reservation_code);\n              if (recurringResponse.data.success) {\n                // 导航到循环预约详情页\n                console.log('找到循环预约，跳转到循环预约详情页:', recurringResponse.data.data.id);\n                this.$router.push(`/recurring-reservation/${recurringResponse.data.data.id}`);\n              } else {\n                this.$message.error(this.$t('reservation.notFound'));\n              }\n            } catch (recurringError) {\n              console.error('Failed to query by reservation code (recurring):', recurringError);\n              this.$message.error(this.$t('reservation.notFound'));\n            }\n          }\n        }\n      } catch (error) {\n        // 检查错误是否包含循环预约信息\n        if (error.response && error.response.data && error.response.data.data && error.response.data.data.is_recurring === true && error.response.data.data.recurring_id) {\n          // 这是一个循环预约，跳转到循环预约详情页\n          console.log('错误响应中检测到循环预约标记，跳转到循环预约详情页:', error.response.data.data.recurring_id);\n          this.$router.push(`/recurring-reservation/${error.response.data.data.recurring_id}`);\n        } else {\n          console.error('查看预约详情失败:', error);\n          this.$message.error(this.$t('common.error'));\n        }\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["reservationApi", "recurringReservationApi", "isReservationExpired", "name", "data", "personalLoading", "showInstructions", "notFound", "personalQueryForm", "reservationCode", "userContact", "personalQueryResults", "currentPage", "pageSize", "totalResults", "personalQueryRules", "required", "message", "$t", "trigger", "min", "max", "isMobile", "window", "innerWidth", "computed", "paginatedResults", "start", "end", "slice", "paginationLayout", "created", "code", "$route", "query", "autoQuery", "$nextTick", "handlePersonal<PERSON><PERSON>y", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "$message", "error", "$refs", "validate", "valid", "response", "getReservationByCode", "success", "console", "log", "$router", "push", "path", "from", "is_recurring", "recurring_id", "recurringResponse", "getRecurringReservationByCode", "id", "recurring<PERSON><PERSON><PERSON>", "getReservations", "user_contact", "limit", "items", "length", "firstReservation", "reservation_code", "fixedItems", "map", "item", "reservation_number", "startsWith", "contactError", "formatDateTime", "row", "column", "cellValue", "date", "Date", "getFullYear", "String", "getMonth", "padStart", "getDate", "getHours", "getMinutes", "getStatusType", "reservation", "status", "toLowerCase", "getStatusText", "resetForm", "clearValidate", "replace", "handleSizeChange", "newSize", "handleCurrentChange", "newPage", "viewReservationDetail", "reservation_code_type", "reservation_number_type", "recurring_reservation_id", "recurringId", "child"], "sources": ["src/views/reservation/ReservationQuery.vue"], "sourcesContent": ["<template>\n  <div class=\"reservation-query\">\n    <h1 class=\"page-title\">{{ $t('reservation.personalManagement') }}</h1>\n\n    <div class=\"query-card\">\n      <el-card shadow=\"never\">\n        <!-- 个人预约管理表单 -->\n        <el-form\n          ref=\"personalQueryForm\"\n          :model=\"personalQueryForm\"\n          :rules=\"personalQueryRules\"\n          label-position=\"top\"\n          @submit.native.prevent=\"handlePersonalQuery\"\n        >\n          <el-form-item :label=\"$t('reservation.code')\" prop=\"reservationCode\">\n            <el-input\n              v-model=\"personalQueryForm.reservationCode\"\n              :placeholder=\"$t('reservation.codeOrContactRequired')\"\n            ></el-input>\n          </el-form-item>\n\n          <el-form-item :label=\"$t('reservation.userContact')\" prop=\"userContact\">\n            <el-input\n              v-model=\"personalQueryForm.userContact\"\n              :placeholder=\"$t('reservation.contactOrCodeRequired')\"\n            ></el-input>\n          </el-form-item>\n\n          <el-form-item>\n            <div class=\"form-tip\">\n              <i class=\"el-icon-info\"></i>\n              <span>{{ $t('reservation.queryTip') }}</span>\n            </div>\n          </el-form-item>\n\n          <el-form-item>\n            <el-button\n              type=\"primary\"\n              icon=\"el-icon-search\"\n              @click=\"handlePersonalQuery\"\n              :loading=\"personalLoading\"\n            >\n              {{ $t('reservation.queryButton') }}\n            </el-button>\n            <el-button @click=\"resetForm\" icon=\"el-icon-refresh-left\">{{ $t('common.reset') }}</el-button>\n          </el-form-item>\n        </el-form>\n      </el-card>\n    </div>\n\n    <!-- 个人预约结果表格 -->\n    <div v-if=\"personalQueryResults.length > 1\" class=\"query-results\">\n      <el-card shadow=\"never\">\n        <div slot=\"header\" style=\"font-size: 16px; font-weight: bold;\">\n          <i class=\"el-icon-document\"></i>\n          <span>{{ $t('reservation.queryResults') }}</span>\n          <span style=\"color: #909399; font-weight: normal; margin-left: 10px;\">\n            ({{ $t('common.total') }} {{ totalResults }} {{ $t('common.items') }})\n          </span>\n        </div>\n        <!-- 桌面端表格视图 -->\n        <el-table\n          v-if=\"!isMobile\"\n          :data=\"paginatedResults\"\n          style=\"width: 100%\"\n          border\n          stripe\n          v-loading=\"personalLoading\"\n        >\n          <el-table-column prop=\"reservation_code\" :label=\"$t('reservation.code')\" min-width=\"120\" />\n          <el-table-column prop=\"equipment_name\" :label=\"$t('reservation.equipmentName')\" min-width=\"120\" />\n          <el-table-column prop=\"start_datetime\" :label=\"$t('reservation.startTime')\" min-width=\"160\" :formatter=\"formatDateTime\" />\n          <el-table-column prop=\"end_datetime\" :label=\"$t('reservation.endTime')\" min-width=\"160\" :formatter=\"formatDateTime\" />\n          <el-table-column prop=\"status\" :label=\"$t('common.status')\" width=\"80\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"getStatusType(scope.row)\">{{ getStatusText(scope.row) }}</el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column :label=\"$t('common.operation')\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"primary\" size=\"mini\" @click=\"viewReservationDetail(scope.row)\">\n                {{ $t('common.view') }}\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n\n        <!-- 移动端卡片视图 -->\n        <div v-else class=\"mobile-card-container\" v-loading=\"personalLoading\">\n          <div\n            v-for=\"reservation in paginatedResults\"\n            :key=\"reservation.reservation_code || reservation.id\"\n            class=\"reservation-mobile-card\"\n          >\n            <div class=\"card-header\">\n              <div class=\"card-title\">\n                <span class=\"equipment-name\">{{ reservation.equipment_name }}</span>\n                <el-tag\n                  :type=\"getStatusType(reservation)\"\n                  size=\"small\"\n                  class=\"status-tag\"\n                >\n                  {{ getStatusText(reservation) }}\n                </el-tag>\n              </div>\n              <div class=\"reservation-code\">{{ reservation.reservation_code }}</div>\n            </div>\n\n            <div class=\"card-content\">\n              <div class=\"time-info\">\n                <div class=\"time-row\">\n                  <i class=\"el-icon-time\"></i>\n                  <span class=\"time-label\">{{ $t('reservation.startTime') }}:</span>\n                  <span class=\"time-value\">{{ formatDateTime(null, null, reservation.start_datetime) }}</span>\n                </div>\n                <div class=\"time-row\">\n                  <i class=\"el-icon-time\"></i>\n                  <span class=\"time-label\">{{ $t('reservation.endTime') }}:</span>\n                  <span class=\"time-value\">{{ formatDateTime(null, null, reservation.end_datetime) }}</span>\n                </div>\n              </div>\n\n              <div class=\"card-actions\">\n                <el-button\n                  type=\"primary\"\n                  size=\"small\"\n                  @click=\"viewReservationDetail(reservation)\"\n                  class=\"view-button\"\n                >\n                  <i class=\"el-icon-view\"></i>\n                  {{ $t('common.view') }}\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <!-- 分页组件 -->\n        <div class=\"pagination-container\" v-if=\"totalResults > 0\">\n          <el-pagination\n            @size-change=\"handleSizeChange\"\n            @current-change=\"handleCurrentChange\"\n            :current-page=\"currentPage\"\n            :page-sizes=\"[10, 20, 50, 100]\"\n            :page-size=\"pageSize\"\n            :layout=\"paginationLayout\"\n            :total=\"totalResults\">\n          </el-pagination>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 个人预约管理说明 -->\n    <div v-if=\"showInstructions\" class=\"instructions-card\">\n      <el-card shadow=\"hover\">\n        <div slot=\"header\" style=\"font-size: 16px; font-weight: bold;\">\n          <i class=\"el-icon-info\" style=\"color: #409EFF; margin-right: 5px;\"></i>\n          <span>{{ $t('common.instructions') }}</span>\n        </div>\n\n        <div class=\"instructions-content\">\n          <p>{{ $t('reservation.queryInstructions') }}</p>\n          <ul>\n            <li><i class=\"el-icon-arrow-right\" style=\"color: #409EFF; margin-right: 5px;\"></i>{{ $t('reservation.queryInstruction1') }}</li>\n            <li><i class=\"el-icon-arrow-right\" style=\"color: #409EFF; margin-right: 5px;\"></i>{{ $t('reservation.queryInstruction2') }}</li>\n            <li><i class=\"el-icon-arrow-right\" style=\"color: #409EFF; margin-right: 5px;\"></i>{{ $t('reservation.queryInstruction3') }}</li>\n          </ul>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 个人预约未找到提示 -->\n    <div v-if=\"notFound\" class=\"not-found-card\">\n      <el-card shadow=\"never\">\n        <el-result\n          icon=\"error\"\n          :title=\"$t('reservation.reservationNotFound')\"\n          :sub-title=\"$t('reservation.checkCodeAndContact')\"\n        ></el-result>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { reservationApi, recurringReservationApi } from '@/api'\nimport { isReservationExpired } from '@/utils/date'\n\nexport default {\n  name: 'ReservationQuery',\n\n  data() {\n    return {\n      // 个人预约管理相关\n      personalLoading: false,\n      showInstructions: true,\n      notFound: false,\n      personalQueryForm: {\n        reservationCode: '',\n        userContact: ''\n      },\n      personalQueryResults: [], // 新增：存放多条预约结果\n\n      // 分页相关\n      currentPage: 1,\n      pageSize: 10,\n      totalResults: 0,\n\n      personalQueryRules: {\n        reservationCode: [\n          { required: false, message: this.$t('reservation.requiredField'), trigger: 'blur' },\n          { min: 6, max: 20, message: this.$t('common.lengthLimit', { min: 6, max: 20 }), trigger: 'blur' }\n        ],\n        userContact: [\n          { required: false, message: this.$t('reservation.requiredField'), trigger: 'blur' }\n        ]\n      },\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    }\n  },\n\n  computed: {\n    // 分页后的结果\n    paginatedResults() {\n      const start = (this.currentPage - 1) * this.pageSize\n      const end = start + this.pageSize\n      return this.personalQueryResults.slice(start, end)\n    },\n\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile\n        ? 'prev, next'\n        : 'total, sizes, prev, pager, next, jumper';\n    }\n  },\n\n  created() {\n    // 如果URL中有查询参数，自动填充并查询\n    const code = this.$route.query.code\n    const reservationCode = this.$route.query.reservationCode\n    const userContact = this.$route.query.userContact\n    const autoQuery = this.$route.query.autoQuery\n\n    // 优先使用 reservationCode 参数（从详情页面返回时传递的）\n    if (reservationCode) {\n      this.personalQueryForm.reservationCode = reservationCode\n    } else if (code) {\n      this.personalQueryForm.reservationCode = code\n    }\n\n    if (userContact) {\n      this.personalQueryForm.userContact = userContact\n    }\n\n    // 如果有 autoQuery 标记或者有查询参数，自动执行查询\n    if (autoQuery === 'true' || code || reservationCode || userContact) {\n      this.$nextTick(() => {\n        this.handlePersonalQuery()\n      })\n    }\n\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize)\n  },\n\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize)\n  },\n\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768\n    },\n\n    // 处理个人预约查询\n    handlePersonalQuery() {\n      // 自定义验证：预定码和联系方式至少填写一项\n      if (!this.personalQueryForm.reservationCode && !this.personalQueryForm.userContact) {\n        this.$message.error(this.$t('reservation.atLeastOneField'))\n        return false\n      }\n\n      this.$refs.personalQueryForm.validate(async (valid) => {\n        if (!valid) {\n          return false\n        }\n\n        this.personalLoading = true\n        this.showInstructions = false\n        this.notFound = false\n\n        try {\n          // 如果有预定码，优先使用预定码查询\n          if (this.personalQueryForm.reservationCode) {\n            try {\n              // 先查普通预约\n              const response = await reservationApi.getReservationByCode(this.personalQueryForm.reservationCode)\n              if (response.data.success) {\n                // 普通预约，导航到预定详情页\n                console.log('找到普通预约，跳转到预约详情页:', this.personalQueryForm.reservationCode)\n                this.$router.push({\n                  path: `/reservation/${this.personalQueryForm.reservationCode}`,\n                  query: {\n                    code: this.personalQueryForm.reservationCode,\n                    from: 'query'\n                  }\n                })\n                return\n              } else {\n                // 检查是否是循环预约\n                if (response.data.data && response.data.data.is_recurring === true && response.data.data.recurring_id) {\n                  // 这是一个循环预约，跳转到循环预约详情页\n                  console.log('检测到循环预约标记，跳转到循环预约详情页:', response.data.data.recurring_id)\n                  this.$router.push({\n                    path: `/recurring-reservation/${response.data.data.recurring_id}`,\n                    query: {\n                      code: this.personalQueryForm.reservationCode,\n                      from: 'query'\n                    }\n                  })\n                  return\n                }\n\n                // 如果普通预约查不到，再查循环预约\n                try {\n                  const recurringResponse = await recurringReservationApi.getRecurringReservationByCode(this.personalQueryForm.reservationCode)\n                  if (recurringResponse.data.success) {\n                    // 导航到循环预约详情页\n                    console.log('找到循环预约，跳转到循环预约详情页:', recurringResponse.data.data.id)\n                    this.$router.push({\n                      path: `/recurring-reservation/${recurringResponse.data.data.id}`,\n                      query: {\n                        code: this.personalQueryForm.reservationCode,\n                        from: 'query'\n                      }\n                    })\n                    return\n                  }\n                } catch (recurringError) {\n                  console.error('Failed to query by reservation code (recurring):', recurringError)\n                }\n              }\n            } catch (error) {\n              // 检查错误是否包含循环预约信息\n              if (error.response && error.response.data && error.response.data.is_recurring === true && error.response.data.recurring_id) {\n                // 这是一个循环预约，跳转到循环预约详情页\n                console.log('错误响应中检测到循环预约标记，跳转到循环预约详情页:', error.response.data.recurring_id)\n                this.$router.push({\n                  path: `/recurring-reservation/${error.response.data.recurring_id}`,\n                  query: {\n                    code: this.personalQueryForm.reservationCode,\n                    from: 'query'\n                  }\n                })\n                return\n              }\n\n              // 普通预约接口报错时也查循环预约\n              try {\n                const recurringResponse = await recurringReservationApi.getRecurringReservationByCode(this.personalQueryForm.reservationCode)\n                if (recurringResponse.data.success) {\n                  // 导航到循环预约详情页\n                  console.log('找到循环预约，跳转到循环预约详情页:', recurringResponse.data.data.id)\n                  this.$router.push({\n                    path: `/recurring-reservation/${recurringResponse.data.data.id}`,\n                    query: {\n                      code: this.personalQueryForm.reservationCode,\n                      from: 'query'\n                    }\n                  })\n                  return\n                }\n              } catch (recurringError) {\n                console.error('Failed to query by reservation code (recurring):', recurringError)\n              }\n            }\n          }\n\n          // 如果没有预定码或预定码查询失败，尝试使用联系方式查询\n          if (this.personalQueryForm.userContact) {\n            try {\n              // 这里需要调用后端接口根据联系方式查询预定\n              const response = await reservationApi.getReservations({\n                user_contact: this.personalQueryForm.userContact,\n                limit: 1000 // 设置一个足够大的limit来获取所有记录\n              })\n\n              if (response.data.items && response.data.items.length > 0) {\n                if (response.data.items.length === 1) {\n                  // 只有一条，直接跳转\n                  const firstReservation = response.data.items[0]\n                  this.$router.push({\n                    path: `/reservation/${firstReservation.reservation_code}`,\n                    query: {\n                      userContact: this.personalQueryForm.userContact,\n                      from: 'query'\n                    }\n                  })\n                  return\n                } else {\n                  // 多条，展示表格并设置分页\n                  console.log('查询到多条预约结果，检查数据结构:', response.data.items);\n\n                  // 检查并修复数据中的字段混淆问题\n                  const fixedItems = response.data.items.map(item => {\n                    console.log('原始预约数据:', {\n                      id: item.id,\n                      reservation_code: item.reservation_code,\n                      reservation_number: item.reservation_number\n                    });\n\n                    // 如果 reservation_code 是预约序号格式，但 reservation_number 为空或不正确\n                    if (item.reservation_code && item.reservation_code.startsWith('RN-') &&\n                        (!item.reservation_number || !item.reservation_number.startsWith('RN-'))) {\n                      console.log('检测到数据字段混淆，进行修复:', item.reservation_code);\n                      // 这种情况下，reservation_code 实际上是 reservation_number\n                      return {\n                        ...item,\n                        reservation_number: item.reservation_code,\n                        // 保持原有的 reservation_code，如果它确实是预约序号，后续逻辑会处理\n                      };\n                    }\n\n                    return item;\n                  });\n\n                  this.personalQueryResults = fixedItems\n                  this.totalResults = fixedItems.length\n                  this.currentPage = 1 // 重置到第一页\n                  return\n                }\n              }\n            } catch (contactError) {\n              console.error('Failed to query by contact:', contactError)\n            }\n          }\n\n          // 如果两种方式都查询失败，显示未找到\n          this.notFound = true\n\n        } catch (error) {\n          console.error('Failed to query personal reservation:', error)\n          this.notFound = true\n        } finally {\n          this.personalLoading = false\n        }\n      })\n    },\n\n    // 格式化日期时间\n    formatDateTime(row, column, cellValue) {\n      if (!cellValue) return ''\n\n      const date = new Date(cellValue)\n      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`\n    },\n\n    // 获取状态类型\n    getStatusType(reservation) {\n      // 统一小写处理，兼容后端大小写不一致\n      const status = (reservation.status || '').toLowerCase()\n      console.log('status for switch:', reservation.status, '->', status)\n      switch (status) {\n        case 'cancelled':\n          return 'danger';  // 已取消 - 红色\n        case 'expired':\n          return 'warning'; // 已过期 - 橙色\n        case 'in_use':\n          return 'primary'; // 使用中 - 蓝色\n        case 'confirmed':\n          return 'success'; // 已确认 - 绿色\n        default:\n          return 'info';    // 其他状态 - 灰色\n      }\n    },\n\n    // 获取状态文本\n    getStatusText(reservation) {\n      // 统一小写处理，兼容后端大小写不一致\n      const status = (reservation.status || '').toLowerCase()\n      console.log('status for switch:', reservation.status, '->', status)\n      switch (status) {\n        case 'cancelled':\n          return this.$t('reservation.cancelled'); // 已取消\n        case 'expired':\n          return this.$t('reservation.expired');   // 已过期\n        case 'in_use':\n          return this.$t('reservation.inUse');     // 使用中\n        case 'confirmed':\n          return this.$t('reservation.confirmed'); // 已确认\n        default:\n          return reservation.status; // 其他状态直接显示\n      }\n    },\n\n    // 重置表单\n    resetForm() {\n      // 手动清空表单数据\n      this.personalQueryForm.reservationCode = ''\n      this.personalQueryForm.userContact = ''\n\n      // 重置表单验证状态\n      this.$refs.personalQueryForm.clearValidate()\n\n      this.notFound = false\n      this.showInstructions = true\n      this.personalQueryResults = [] // 重置结果\n      this.totalResults = 0 // 重置总数\n      this.currentPage = 1 // 重置页码\n\n      // 清除URL参数，避免页面刷新时重新填充表单\n      if (this.$route.query.userContact || this.$route.query.reservationCode || this.$route.query.autoQuery) {\n        this.$router.replace({\n          path: this.$route.path,\n          query: {} // 清空所有查询参数\n        })\n      }\n    },\n\n    // 分页大小改变\n    handleSizeChange(newSize) {\n      this.pageSize = newSize\n      this.currentPage = 1 // 重置到第一页\n    },\n\n    // 当前页改变\n    handleCurrentChange(newPage) {\n      console.log('分页改变:', newPage)\n      this.currentPage = newPage\n    },\n\n    // 查看预约详情\n    async viewReservationDetail(reservation) {\n      console.log('查看预约详情:', reservation);\n      console.log('预约详情字段分析:', {\n        id: reservation.id,\n        reservation_code: reservation.reservation_code,\n        reservation_number: reservation.reservation_number,\n        reservation_code_type: reservation.reservation_code ? (reservation.reservation_code.startsWith('RN-') ? '预约序号格式' : '预约码格式') : '空',\n        reservation_number_type: reservation.reservation_number ? (reservation.reservation_number.startsWith('RN-') ? '预约序号格式' : '其他格式') : '空'\n      });\n\n      try {\n        // 检查是否有预约序号，如果有，说明是子预约，直接跳转到子预约详情页面\n        if (reservation.reservation_number) {\n          console.log('检测到预约序号，直接跳转到子预约详情页面:', reservation.reservation_number);\n\n          // 构建查询参数\n          const query = {\n            userContact: this.personalQueryForm.userContact,\n            reservationCode: this.personalQueryForm.reservationCode, // 保留预定码\n            from: 'query'\n          };\n\n          // 如果有循环预约ID，添加到查询参数中\n          if (reservation.recurring_reservation_id) {\n            query.recurringId = reservation.recurring_reservation_id;\n            query.child = 'true';\n          }\n\n          // 跳转到子预约详情页面\n          this.$router.push({\n            path: `/reservation/number/${reservation.reservation_number}`,\n            query: query\n          });\n\n          return;\n        }\n\n        // 检查 reservation_code 是否实际上是预约序号（以RN-开头）\n        if (reservation.reservation_code && reservation.reservation_code.startsWith('RN-')) {\n          console.log('检测到reservation_code实际上是预约序号，直接跳转到子预约详情页面:', reservation.reservation_code);\n\n          // 构建查询参数\n          const query = {\n            userContact: this.personalQueryForm.userContact,\n            reservationCode: this.personalQueryForm.reservationCode, // 保留预定码\n            from: 'query'\n          };\n\n          // 如果有循环预约ID，添加到查询参数中\n          if (reservation.recurring_reservation_id) {\n            query.recurringId = reservation.recurring_reservation_id;\n            query.child = 'true';\n          }\n\n          // 跳转到子预约详情页面，使用reservation_code作为预约序号\n          this.$router.push({\n            path: `/reservation/number/${reservation.reservation_code}`,\n            query: query\n          });\n\n          return;\n        }\n\n        // 如果没有预约序号，按原有逻辑处理\n        // 先查询预约详情，检查是否是循环预约\n        const response = await reservationApi.getReservationByCode(reservation.reservation_code);\n\n        if (response.data.success) {\n          // 普通预约，导航到预定详情页\n          console.log('找到普通预约，跳转到预约详情页:', reservation.reservation_code);\n          this.$router.push({\n            path: `/reservation/${reservation.reservation_code}`,\n            query: {\n              userContact: this.personalQueryForm.userContact,\n              reservationCode: this.personalQueryForm.reservationCode, // 保留预定码\n              from: 'query'\n            }\n          });\n        } else {\n          // 检查是否是循环预约\n          if (response.data.data && response.data.data.is_recurring === true && response.data.data.recurring_id) {\n            // 这是一个循环预约，跳转到循环预约详情页\n            console.log('检测到循环预约标记，跳转到循环预约详情页:', response.data.data.recurring_id);\n            this.$router.push({\n              path: `/recurring-reservation/${response.data.data.recurring_id}`,\n              query: {\n                userContact: this.personalQueryForm.userContact,\n                reservationCode: this.personalQueryForm.reservationCode, // 保留预定码\n                from: 'query'\n              }\n            });\n          } else {\n            // 如果普通预约查不到，再查循环预约\n            try {\n              const recurringResponse = await recurringReservationApi.getRecurringReservationByCode(reservation.reservation_code);\n              if (recurringResponse.data.success) {\n                // 导航到循环预约详情页\n                console.log('找到循环预约，跳转到循环预约详情页:', recurringResponse.data.data.id);\n                this.$router.push(`/recurring-reservation/${recurringResponse.data.data.id}`);\n              } else {\n                this.$message.error(this.$t('reservation.notFound'));\n              }\n            } catch (recurringError) {\n              console.error('Failed to query by reservation code (recurring):', recurringError);\n              this.$message.error(this.$t('reservation.notFound'));\n            }\n          }\n        }\n      } catch (error) {\n        // 检查错误是否包含循环预约信息\n        if (error.response && error.response.data && error.response.data.data &&\n            error.response.data.data.is_recurring === true && error.response.data.data.recurring_id) {\n          // 这是一个循环预约，跳转到循环预约详情页\n          console.log('错误响应中检测到循环预约标记，跳转到循环预约详情页:', error.response.data.data.recurring_id);\n          this.$router.push(`/recurring-reservation/${error.response.data.data.recurring_id}`);\n        } else {\n          console.error('查看预约详情失败:', error);\n          this.$message.error(this.$t('common.error'));\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.reservation-query {\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.page-title {\n  margin-bottom: 20px;\n  font-size: 24px;\n  color: #303133;\n}\n\n.query-card {\n  margin-bottom: 20px;\n}\n\n.query-results,\n.no-results-card,\n.instructions-card,\n.not-found-card {\n  margin-top: 30px;\n}\n\n.result-card {\n  margin-bottom: 20px;\n}\n\n.instructions-content {\n  color: #606266;\n}\n\n.instructions-content p {\n  margin-top: 0;\n  margin-bottom: 20px;\n  font-size: 15px;\n  color: #606266;\n  font-weight: 500;\n}\n\n.instructions-content ul {\n  padding-left: 20px;\n  margin-top: 15px;\n}\n\n.instructions-content li {\n  margin-bottom: 15px;\n  line-height: 1.6;\n  color: #606266;\n}\n\n/* 移动端卡片样式 */\n.mobile-card-container {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.reservation-mobile-card {\n  background: #ffffff;\n  border-radius: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n  border: 1px solid #e4e7ed;\n  overflow: hidden;\n  transition: box-shadow 0.3s ease;\n}\n\n.reservation-mobile-card:hover {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.card-header {\n  padding: 16px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.card-title {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  flex: 1;\n}\n\n.equipment-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #303133;\n  line-height: 1.4;\n}\n\n.status-tag {\n  align-self: flex-start;\n  font-weight: 500;\n}\n\n.reservation-code {\n  font-size: 12px;\n  color: #409eff;\n  font-weight: 600;\n  margin-left: 12px;\n  background: #ecf5ff;\n  padding: 4px 8px;\n  border-radius: 4px;\n  font-family: monospace;\n  border: 1px solid #b3d8ff;\n}\n\n.card-content {\n  padding: 16px;\n}\n\n.time-info {\n  background: #f8f9fa;\n  border-radius: 6px;\n  padding: 12px;\n  margin-bottom: 16px;\n}\n\n.time-row {\n  display: flex;\n  align-items: center;\n  margin-bottom: 8px;\n  font-size: 13px;\n}\n\n.time-row:last-child {\n  margin-bottom: 0;\n}\n\n.time-row i {\n  color: #409eff;\n  margin-right: 8px;\n  font-size: 14px;\n}\n\n.time-label {\n  color: #606266;\n  margin-right: 8px;\n  font-weight: 500;\n  min-width: 60px;\n}\n\n.time-value {\n  color: #303133;\n  font-weight: 500;\n}\n\n.card-actions {\n  display: flex;\n  justify-content: center;\n  padding-top: 8px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.view-button {\n  width: 100%;\n  font-weight: 500;\n}\n\n.pagination-container {\n  margin-top: 20px;\n  text-align: center;\n}\n\n/* 表单提示样式 */\n.form-tip {\n  font-size: 13px;\n  color: #909399;\n  margin-bottom: 15px;\n  line-height: 1.5;\n  display: flex;\n  align-items: flex-start;\n}\n\n.form-tip i {\n  margin-right: 5px;\n  margin-top: 3px;\n  color: #409EFF;\n}\n\n/* 选项卡样式 */\n.el-tabs__item {\n  font-size: 16px;\n  padding: 0 20px;\n}\n\n/* 表格样式 */\n.el-table {\n  margin-top: 10px;\n}\n\n/* 日期选择器样式 */\n.el-date-editor--daterange {\n  width: 100% !important;\n}\n\n/* 分页容器样式 */\n.pagination-container {\n  margin-top: 20px;\n  text-align: center;\n}\n\n/* 移动端优化 - 确保点击事件正常工作 */\n@media (max-width: 768px) {\n  /* 确保遮罩层不会阻挡点击事件 */\n  :deep(.v-modal) {\n    pointer-events: none !important;\n  }\n\n  /* 确保分页组件可以接收点击事件 */\n  .pagination-container {\n    position: relative;\n    z-index: 10;\n    pointer-events: auto;\n  }\n\n  /* 确保移动端卡片和按钮可以接收点击事件 */\n  .mobile-card-container {\n    position: relative;\n    z-index: 10;\n    pointer-events: auto;\n  }\n\n  .reservation-mobile-card {\n    position: relative;\n    z-index: 11;\n    pointer-events: auto;\n  }\n\n  .view-button {\n    position: relative;\n    z-index: 12;\n    pointer-events: auto;\n  }\n\n  /* 确保悬浮菜单按钮层级正确 */\n  :deep(.mobile-nav-toggle) {\n    z-index: 1000 !important;\n  }\n\n  /* 分页组件移动端优化 */\n  :deep(.el-pagination) {\n    position: relative;\n    z-index: 10;\n  }\n\n  :deep(.el-pagination .btn-prev),\n  :deep(.el-pagination .btn-next),\n  :deep(.el-pagination .el-pager li) {\n    pointer-events: auto !important;\n    position: relative;\n    z-index: 11;\n  }\n}\n</style>\n"], "mappings": "AAyLA,SAAAA,cAAA,EAAAC,uBAAA;AACA,SAAAC,oBAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACA;MACAC,eAAA;MACAC,gBAAA;MACAC,QAAA;MACAC,iBAAA;QACAC,eAAA;QACAC,WAAA;MACA;MACAC,oBAAA;MAAA;;MAEA;MACAC,WAAA;MACAC,QAAA;MACAC,YAAA;MAEAC,kBAAA;QACAN,eAAA,GACA;UAAAO,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAJ,OAAA,OAAAC,EAAA;YAAAE,GAAA;YAAAC,GAAA;UAAA;UAAAF,OAAA;QAAA,EACA;QACAT,WAAA,GACA;UAAAM,QAAA;UAAAC,OAAA,OAAAC,EAAA;UAAAC,OAAA;QAAA;MAEA;MACA;MACAG,QAAA,EAAAC,MAAA,CAAAC,UAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,iBAAA;MACA,MAAAC,KAAA,SAAAf,WAAA,aAAAC,QAAA;MACA,MAAAe,GAAA,GAAAD,KAAA,QAAAd,QAAA;MACA,YAAAF,oBAAA,CAAAkB,KAAA,CAAAF,KAAA,EAAAC,GAAA;IACA;IAEA;IACAE,iBAAA;MACA,YAAAR,QAAA,GACA,eACA;IACA;EACA;EAEAS,QAAA;IACA;IACA,MAAAC,IAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAF,IAAA;IACA,MAAAvB,eAAA,QAAAwB,MAAA,CAAAC,KAAA,CAAAzB,eAAA;IACA,MAAAC,WAAA,QAAAuB,MAAA,CAAAC,KAAA,CAAAxB,WAAA;IACA,MAAAyB,SAAA,QAAAF,MAAA,CAAAC,KAAA,CAAAC,SAAA;;IAEA;IACA,IAAA1B,eAAA;MACA,KAAAD,iBAAA,CAAAC,eAAA,GAAAA,eAAA;IACA,WAAAuB,IAAA;MACA,KAAAxB,iBAAA,CAAAC,eAAA,GAAAuB,IAAA;IACA;IAEA,IAAAtB,WAAA;MACA,KAAAF,iBAAA,CAAAE,WAAA,GAAAA,WAAA;IACA;;IAEA;IACA,IAAAyB,SAAA,eAAAH,IAAA,IAAAvB,eAAA,IAAAC,WAAA;MACA,KAAA0B,SAAA;QACA,KAAAC,mBAAA;MACA;IACA;;IAEA;IACAd,MAAA,CAAAe,gBAAA,gBAAAC,YAAA;EACA;EAEAC,cAAA;IACA;IACAjB,MAAA,CAAAkB,mBAAA,gBAAAF,YAAA;EACA;EAEAG,OAAA;IACA;IACAH,aAAA;MACA,KAAAjB,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IAEA;IACAa,oBAAA;MACA;MACA,UAAA7B,iBAAA,CAAAC,eAAA,UAAAD,iBAAA,CAAAE,WAAA;QACA,KAAAiC,QAAA,CAAAC,KAAA,MAAA1B,EAAA;QACA;MACA;MAEA,KAAA2B,KAAA,CAAArC,iBAAA,CAAAsC,QAAA,OAAAC,KAAA;QACA,KAAAA,KAAA;UACA;QACA;QAEA,KAAA1C,eAAA;QACA,KAAAC,gBAAA;QACA,KAAAC,QAAA;QAEA;UACA;UACA,SAAAC,iBAAA,CAAAC,eAAA;YACA;cACA;cACA,MAAAuC,QAAA,SAAAhD,cAAA,CAAAiD,oBAAA,MAAAzC,iBAAA,CAAAC,eAAA;cACA,IAAAuC,QAAA,CAAA5C,IAAA,CAAA8C,OAAA;gBACA;gBACAC,OAAA,CAAAC,GAAA,0BAAA5C,iBAAA,CAAAC,eAAA;gBACA,KAAA4C,OAAA,CAAAC,IAAA;kBACAC,IAAA,uBAAA/C,iBAAA,CAAAC,eAAA;kBACAyB,KAAA;oBACAF,IAAA,OAAAxB,iBAAA,CAAAC,eAAA;oBACA+C,IAAA;kBACA;gBACA;gBACA;cACA;gBACA;gBACA,IAAAR,QAAA,CAAA5C,IAAA,CAAAA,IAAA,IAAA4C,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAqD,YAAA,aAAAT,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;kBACA;kBACAP,OAAA,CAAAC,GAAA,0BAAAJ,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;kBACA,KAAAL,OAAA,CAAAC,IAAA;oBACAC,IAAA,4BAAAP,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;oBACAxB,KAAA;sBACAF,IAAA,OAAAxB,iBAAA,CAAAC,eAAA;sBACA+C,IAAA;oBACA;kBACA;kBACA;gBACA;;gBAEA;gBACA;kBACA,MAAAG,iBAAA,SAAA1D,uBAAA,CAAA2D,6BAAA,MAAApD,iBAAA,CAAAC,eAAA;kBACA,IAAAkD,iBAAA,CAAAvD,IAAA,CAAA8C,OAAA;oBACA;oBACAC,OAAA,CAAAC,GAAA,uBAAAO,iBAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAyD,EAAA;oBACA,KAAAR,OAAA,CAAAC,IAAA;sBACAC,IAAA,4BAAAI,iBAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAyD,EAAA;sBACA3B,KAAA;wBACAF,IAAA,OAAAxB,iBAAA,CAAAC,eAAA;wBACA+C,IAAA;sBACA;oBACA;oBACA;kBACA;gBACA,SAAAM,cAAA;kBACAX,OAAA,CAAAP,KAAA,qDAAAkB,cAAA;gBACA;cACA;YACA,SAAAlB,KAAA;cACA;cACA,IAAAA,KAAA,CAAAI,QAAA,IAAAJ,KAAA,CAAAI,QAAA,CAAA5C,IAAA,IAAAwC,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAqD,YAAA,aAAAb,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAsD,YAAA;gBACA;gBACAP,OAAA,CAAAC,GAAA,+BAAAR,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAsD,YAAA;gBACA,KAAAL,OAAA,CAAAC,IAAA;kBACAC,IAAA,4BAAAX,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAsD,YAAA;kBACAxB,KAAA;oBACAF,IAAA,OAAAxB,iBAAA,CAAAC,eAAA;oBACA+C,IAAA;kBACA;gBACA;gBACA;cACA;;cAEA;cACA;gBACA,MAAAG,iBAAA,SAAA1D,uBAAA,CAAA2D,6BAAA,MAAApD,iBAAA,CAAAC,eAAA;gBACA,IAAAkD,iBAAA,CAAAvD,IAAA,CAAA8C,OAAA;kBACA;kBACAC,OAAA,CAAAC,GAAA,uBAAAO,iBAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAyD,EAAA;kBACA,KAAAR,OAAA,CAAAC,IAAA;oBACAC,IAAA,4BAAAI,iBAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAyD,EAAA;oBACA3B,KAAA;sBACAF,IAAA,OAAAxB,iBAAA,CAAAC,eAAA;sBACA+C,IAAA;oBACA;kBACA;kBACA;gBACA;cACA,SAAAM,cAAA;gBACAX,OAAA,CAAAP,KAAA,qDAAAkB,cAAA;cACA;YACA;UACA;;UAEA;UACA,SAAAtD,iBAAA,CAAAE,WAAA;YACA;cACA;cACA,MAAAsC,QAAA,SAAAhD,cAAA,CAAA+D,eAAA;gBACAC,YAAA,OAAAxD,iBAAA,CAAAE,WAAA;gBACAuD,KAAA;cACA;cAEA,IAAAjB,QAAA,CAAA5C,IAAA,CAAA8D,KAAA,IAAAlB,QAAA,CAAA5C,IAAA,CAAA8D,KAAA,CAAAC,MAAA;gBACA,IAAAnB,QAAA,CAAA5C,IAAA,CAAA8D,KAAA,CAAAC,MAAA;kBACA;kBACA,MAAAC,gBAAA,GAAApB,QAAA,CAAA5C,IAAA,CAAA8D,KAAA;kBACA,KAAAb,OAAA,CAAAC,IAAA;oBACAC,IAAA,kBAAAa,gBAAA,CAAAC,gBAAA;oBACAnC,KAAA;sBACAxB,WAAA,OAAAF,iBAAA,CAAAE,WAAA;sBACA8C,IAAA;oBACA;kBACA;kBACA;gBACA;kBACA;kBACAL,OAAA,CAAAC,GAAA,sBAAAJ,QAAA,CAAA5C,IAAA,CAAA8D,KAAA;;kBAEA;kBACA,MAAAI,UAAA,GAAAtB,QAAA,CAAA5C,IAAA,CAAA8D,KAAA,CAAAK,GAAA,CAAAC,IAAA;oBACArB,OAAA,CAAAC,GAAA;sBACAS,EAAA,EAAAW,IAAA,CAAAX,EAAA;sBACAQ,gBAAA,EAAAG,IAAA,CAAAH,gBAAA;sBACAI,kBAAA,EAAAD,IAAA,CAAAC;oBACA;;oBAEA;oBACA,IAAAD,IAAA,CAAAH,gBAAA,IAAAG,IAAA,CAAAH,gBAAA,CAAAK,UAAA,YACA,CAAAF,IAAA,CAAAC,kBAAA,KAAAD,IAAA,CAAAC,kBAAA,CAAAC,UAAA;sBACAvB,OAAA,CAAAC,GAAA,oBAAAoB,IAAA,CAAAH,gBAAA;sBACA;sBACA;wBACA,GAAAG,IAAA;wBACAC,kBAAA,EAAAD,IAAA,CAAAH;wBACA;sBACA;oBACA;oBAEA,OAAAG,IAAA;kBACA;kBAEA,KAAA7D,oBAAA,GAAA2D,UAAA;kBACA,KAAAxD,YAAA,GAAAwD,UAAA,CAAAH,MAAA;kBACA,KAAAvD,WAAA;kBACA;gBACA;cACA;YACA,SAAA+D,YAAA;cACAxB,OAAA,CAAAP,KAAA,gCAAA+B,YAAA;YACA;UACA;;UAEA;UACA,KAAApE,QAAA;QAEA,SAAAqC,KAAA;UACAO,OAAA,CAAAP,KAAA,0CAAAA,KAAA;UACA,KAAArC,QAAA;QACA;UACA,KAAAF,eAAA;QACA;MACA;IACA;IAEA;IACAuE,eAAAC,GAAA,EAAAC,MAAA,EAAAC,SAAA;MACA,KAAAA,SAAA;MAEA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,SAAA;MACA,UAAAC,IAAA,CAAAE,WAAA,MAAAC,MAAA,CAAAH,IAAA,CAAAI,QAAA,QAAAC,QAAA,YAAAF,MAAA,CAAAH,IAAA,CAAAM,OAAA,IAAAD,QAAA,YAAAF,MAAA,CAAAH,IAAA,CAAAO,QAAA,IAAAF,QAAA,YAAAF,MAAA,CAAAH,IAAA,CAAAQ,UAAA,IAAAH,QAAA;IACA;IAEA;IACAI,cAAAC,WAAA;MACA;MACA,MAAAC,MAAA,IAAAD,WAAA,CAAAC,MAAA,QAAAC,WAAA;MACAzC,OAAA,CAAAC,GAAA,uBAAAsC,WAAA,CAAAC,MAAA,QAAAA,MAAA;MACA,QAAAA,MAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;QACA;UACA;QAAA;MACA;IACA;IAEA;IACAE,cAAAH,WAAA;MACA;MACA,MAAAC,MAAA,IAAAD,WAAA,CAAAC,MAAA,QAAAC,WAAA;MACAzC,OAAA,CAAAC,GAAA,uBAAAsC,WAAA,CAAAC,MAAA,QAAAA,MAAA;MACA,QAAAA,MAAA;QACA;UACA,YAAAzE,EAAA;QAAA;QACA;UACA,YAAAA,EAAA;QAAA;QACA;UACA,YAAAA,EAAA;QAAA;QACA;UACA,YAAAA,EAAA;QAAA;QACA;UACA,OAAAwE,WAAA,CAAAC,MAAA;QAAA;MACA;IACA;IAEA;IACAG,UAAA;MACA;MACA,KAAAtF,iBAAA,CAAAC,eAAA;MACA,KAAAD,iBAAA,CAAAE,WAAA;;MAEA;MACA,KAAAmC,KAAA,CAAArC,iBAAA,CAAAuF,aAAA;MAEA,KAAAxF,QAAA;MACA,KAAAD,gBAAA;MACA,KAAAK,oBAAA;MACA,KAAAG,YAAA;MACA,KAAAF,WAAA;;MAEA;MACA,SAAAqB,MAAA,CAAAC,KAAA,CAAAxB,WAAA,SAAAuB,MAAA,CAAAC,KAAA,CAAAzB,eAAA,SAAAwB,MAAA,CAAAC,KAAA,CAAAC,SAAA;QACA,KAAAkB,OAAA,CAAA2C,OAAA;UACAzC,IAAA,OAAAtB,MAAA,CAAAsB,IAAA;UACArB,KAAA;QACA;MACA;IACA;IAEA;IACA+D,iBAAAC,OAAA;MACA,KAAArF,QAAA,GAAAqF,OAAA;MACA,KAAAtF,WAAA;IACA;IAEA;IACAuF,oBAAAC,OAAA;MACAjD,OAAA,CAAAC,GAAA,UAAAgD,OAAA;MACA,KAAAxF,WAAA,GAAAwF,OAAA;IACA;IAEA;IACA,MAAAC,sBAAAX,WAAA;MACAvC,OAAA,CAAAC,GAAA,YAAAsC,WAAA;MACAvC,OAAA,CAAAC,GAAA;QACAS,EAAA,EAAA6B,WAAA,CAAA7B,EAAA;QACAQ,gBAAA,EAAAqB,WAAA,CAAArB,gBAAA;QACAI,kBAAA,EAAAiB,WAAA,CAAAjB,kBAAA;QACA6B,qBAAA,EAAAZ,WAAA,CAAArB,gBAAA,GAAAqB,WAAA,CAAArB,gBAAA,CAAAK,UAAA;QACA6B,uBAAA,EAAAb,WAAA,CAAAjB,kBAAA,GAAAiB,WAAA,CAAAjB,kBAAA,CAAAC,UAAA;MACA;MAEA;QACA;QACA,IAAAgB,WAAA,CAAAjB,kBAAA;UACAtB,OAAA,CAAAC,GAAA,0BAAAsC,WAAA,CAAAjB,kBAAA;;UAEA;UACA,MAAAvC,KAAA;YACAxB,WAAA,OAAAF,iBAAA,CAAAE,WAAA;YACAD,eAAA,OAAAD,iBAAA,CAAAC,eAAA;YAAA;YACA+C,IAAA;UACA;;UAEA;UACA,IAAAkC,WAAA,CAAAc,wBAAA;YACAtE,KAAA,CAAAuE,WAAA,GAAAf,WAAA,CAAAc,wBAAA;YACAtE,KAAA,CAAAwE,KAAA;UACA;;UAEA;UACA,KAAArD,OAAA,CAAAC,IAAA;YACAC,IAAA,yBAAAmC,WAAA,CAAAjB,kBAAA;YACAvC,KAAA,EAAAA;UACA;UAEA;QACA;;QAEA;QACA,IAAAwD,WAAA,CAAArB,gBAAA,IAAAqB,WAAA,CAAArB,gBAAA,CAAAK,UAAA;UACAvB,OAAA,CAAAC,GAAA,8CAAAsC,WAAA,CAAArB,gBAAA;;UAEA;UACA,MAAAnC,KAAA;YACAxB,WAAA,OAAAF,iBAAA,CAAAE,WAAA;YACAD,eAAA,OAAAD,iBAAA,CAAAC,eAAA;YAAA;YACA+C,IAAA;UACA;;UAEA;UACA,IAAAkC,WAAA,CAAAc,wBAAA;YACAtE,KAAA,CAAAuE,WAAA,GAAAf,WAAA,CAAAc,wBAAA;YACAtE,KAAA,CAAAwE,KAAA;UACA;;UAEA;UACA,KAAArD,OAAA,CAAAC,IAAA;YACAC,IAAA,yBAAAmC,WAAA,CAAArB,gBAAA;YACAnC,KAAA,EAAAA;UACA;UAEA;QACA;;QAEA;QACA;QACA,MAAAc,QAAA,SAAAhD,cAAA,CAAAiD,oBAAA,CAAAyC,WAAA,CAAArB,gBAAA;QAEA,IAAArB,QAAA,CAAA5C,IAAA,CAAA8C,OAAA;UACA;UACAC,OAAA,CAAAC,GAAA,qBAAAsC,WAAA,CAAArB,gBAAA;UACA,KAAAhB,OAAA,CAAAC,IAAA;YACAC,IAAA,kBAAAmC,WAAA,CAAArB,gBAAA;YACAnC,KAAA;cACAxB,WAAA,OAAAF,iBAAA,CAAAE,WAAA;cACAD,eAAA,OAAAD,iBAAA,CAAAC,eAAA;cAAA;cACA+C,IAAA;YACA;UACA;QACA;UACA;UACA,IAAAR,QAAA,CAAA5C,IAAA,CAAAA,IAAA,IAAA4C,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAqD,YAAA,aAAAT,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;YACA;YACAP,OAAA,CAAAC,GAAA,0BAAAJ,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;YACA,KAAAL,OAAA,CAAAC,IAAA;cACAC,IAAA,4BAAAP,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;cACAxB,KAAA;gBACAxB,WAAA,OAAAF,iBAAA,CAAAE,WAAA;gBACAD,eAAA,OAAAD,iBAAA,CAAAC,eAAA;gBAAA;gBACA+C,IAAA;cACA;YACA;UACA;YACA;YACA;cACA,MAAAG,iBAAA,SAAA1D,uBAAA,CAAA2D,6BAAA,CAAA8B,WAAA,CAAArB,gBAAA;cACA,IAAAV,iBAAA,CAAAvD,IAAA,CAAA8C,OAAA;gBACA;gBACAC,OAAA,CAAAC,GAAA,uBAAAO,iBAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAyD,EAAA;gBACA,KAAAR,OAAA,CAAAC,IAAA,2BAAAK,iBAAA,CAAAvD,IAAA,CAAAA,IAAA,CAAAyD,EAAA;cACA;gBACA,KAAAlB,QAAA,CAAAC,KAAA,MAAA1B,EAAA;cACA;YACA,SAAA4C,cAAA;cACAX,OAAA,CAAAP,KAAA,qDAAAkB,cAAA;cACA,KAAAnB,QAAA,CAAAC,KAAA,MAAA1B,EAAA;YACA;UACA;QACA;MACA,SAAA0B,KAAA;QACA;QACA,IAAAA,KAAA,CAAAI,QAAA,IAAAJ,KAAA,CAAAI,QAAA,CAAA5C,IAAA,IAAAwC,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAA,IAAA,IACAwC,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAqD,YAAA,aAAAb,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;UACA;UACAP,OAAA,CAAAC,GAAA,+BAAAR,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;UACA,KAAAL,OAAA,CAAAC,IAAA,2BAAAV,KAAA,CAAAI,QAAA,CAAA5C,IAAA,CAAAA,IAAA,CAAAsD,YAAA;QACA;UACAP,OAAA,CAAAP,KAAA,cAAAA,KAAA;UACA,KAAAD,QAAA,CAAAC,KAAA,MAAA1B,EAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}