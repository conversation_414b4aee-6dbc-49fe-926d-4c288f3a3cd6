{"ast": null, "code": "import AppHeader from '@/components/layout/AppHeader.vue';\nimport AppFooter from '@/components/layout/AppFooter.vue';\nimport AnnouncementBar from '@/components/common/AnnouncementBar.vue';\nimport { fetchAnnouncements } from '@/api/announcement';\nimport { mapGetters } from 'vuex';\nexport default {\n  name: 'App',\n  components: {\n    AppHeader,\n    AppFooter,\n    AnnouncementBar\n  },\n  data() {\n    return {\n      announcements: []\n    };\n  },\n  computed: {\n    ...mapGetters(['isDarkMode']),\n    isAdminRoute() {\n      return this.$route.path.startsWith('/admin');\n    }\n  },\n  created() {\n    this.loadAnnouncements();\n    this.applyTheme();\n  },\n  methods: {\n    async loadAnnouncements() {\n      try {\n        const res = await fetchAnnouncements();\n        this.announcements = Array.isArray(res) ? res : [];\n        console.log('App组件加载到公告数据:', this.announcements);\n      } catch (error) {\n        console.error('加载公告失败:', error);\n      }\n    },\n    applyTheme() {\n      // 应用暗色主题\n      if (this.isDarkMode) {\n        document.documentElement.classList.add('dark-mode');\n      } else {\n        document.documentElement.classList.remove('dark-mode');\n      }\n    }\n  },\n  watch: {\n    isDarkMode: {\n      handler(newValue) {\n        // 当isDarkMode变化时重新应用主题\n        if (newValue) {\n          document.documentElement.classList.add('dark-mode');\n        } else {\n          document.documentElement.classList.remove('dark-mode');\n        }\n      },\n      immediate: true\n    }\n  }\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "AppFooter", "AnnouncementBar", "fetchAnnouncements", "mapGetters", "name", "components", "data", "announcements", "computed", "isAdminRoute", "$route", "path", "startsWith", "created", "loadAnnouncements", "applyTheme", "methods", "res", "Array", "isArray", "console", "log", "error", "isDarkMode", "document", "documentElement", "classList", "add", "remove", "watch", "handler", "newValue", "immediate"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\" :class=\"{ 'admin-route': isAdminRoute }\">\n    <announcement-bar v-if=\"announcements.length && !isAdminRoute\" class=\"announcement-fixed\" />\n    <el-container>\n      <el-header v-if=\"!isAdminRoute\">\n        <app-header />\n      </el-header>\n      <el-main>\n        <router-view />\n      </el-main>\n      <el-footer v-if=\"!isAdminRoute\" :class=\"{ 'admin-route-hidden': isAdminRoute }\">\n        <app-footer />\n      </el-footer>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport AppHeader from '@/components/layout/AppHeader.vue'\nimport AppFooter from '@/components/layout/AppFooter.vue'\nimport AnnouncementBar from '@/components/common/AnnouncementBar.vue'\nimport { fetchAnnouncements } from '@/api/announcement'\nimport { mapGetters } from 'vuex'\n\nexport default {\n  name: 'App',\n  components: {\n    AppHeader,\n    <PERSON><PERSON><PERSON>ooter,\n    Announcement<PERSON><PERSON>\n  },\n  data() {\n    return {\n      announcements: []\n    }\n  },\n  computed: {\n    ...mapGetters(['isDarkMode']),\n    isAdminRoute() {\n      return this.$route.path.startsWith('/admin')\n    }\n  },\n  created() {\n    this.loadAnnouncements()\n    this.applyTheme()\n  },\n  methods: {\n    async loadAnnouncements() {\n      try {\n        const res = await fetchAnnouncements()\n        this.announcements = Array.isArray(res) ? res : []\n        console.log('App组件加载到公告数据:', this.announcements)\n      } catch (error) {\n        console.error('加载公告失败:', error)\n      }\n    },\n    applyTheme() {\n      // 应用暗色主题\n      if (this.isDarkMode) {\n        document.documentElement.classList.add('dark-mode')\n      } else {\n        document.documentElement.classList.remove('dark-mode')\n      }\n    }\n  },\n  watch: {\n    isDarkMode: {\n      handler(newValue) {\n        // 当isDarkMode变化时重新应用主题\n        if (newValue) {\n          document.documentElement.classList.add('dark-mode')\n        } else {\n          document.documentElement.classList.remove('dark-mode')\n        }\n      },\n      immediate: true\n    }\n  }\n}\n</script>\n\n<style>\n/* 全局样式 */\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\nbody {\n  margin: 0 !important;\n  padding: 0 !important;\n  overflow-x: hidden !important;\n  min-height: 100vh;\n}\n\nhtml, body {\n  margin: 0;\n  padding: 0;\n  height: 100vh;\n  width: 100%;\n  overflow-x: hidden;\n  overflow-y: hidden;\n  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;\n}\n\n#app {\n  height: 100vh;\n  width: 100vw;\n  max-width: 100vw;\n  margin: 0;\n  padding: 0;\n  overflow: hidden;\n}\n\n.el-container {\n  height: 100vh;\n  width: 100%;\n  max-width: 100%;\n  padding: 0;\n  margin: 0;\n  overflow: hidden;\n  display: flex;\n  flex-direction: column;\n}\n\n.el-header {\n  padding: 0 !important;\n  margin: 0 !important;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n  width: 100vw !important;\n  max-width: 100vw !important;\n  overflow: hidden;\n  background-color: #FFFFFF !important;\n  border-bottom: 1px solid #EBEEF5;\n  flex-shrink: 0;\n}\n\n.el-main {\n  padding: 20px;\n  background-color: #f5f7fa;\n  width: 100%;\n  max-width: 100%;\n  flex: 1;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n\n.el-footer {\n  padding: 20px;\n  background-color: #f5f7fa;\n  border-top: 1px solid #e6e6e6;\n  flex-shrink: 0;\n}\n\n/* 响应式调整 */\n@media (max-width: 768px) {\n  .el-main {\n    padding: 10px;\n    overflow-y: auto;\n    overflow-x: hidden;\n    /* 在移动端，header高度为0，所以main区域需要为footer留出空间 */\n    max-height: calc(100vh - 80px); /* 只减去footer的高度 */\n    flex: 1;\n  }\n\n  .el-footer {\n    padding: 10px;\n    flex-shrink: 0;\n    min-height: 60px; /* 确保footer有最小高度 */\n    background-color: #f5f7fa;\n    border-top: 1px solid #e6e6e6;\n  }\n\n  .el-header {\n    height: 0 !important; /* 移动端header高度为0，但不完全隐藏，保留MobileNav */\n    padding: 0 !important;\n    margin: 0 !important;\n    border: none !important;\n    box-shadow: none !important;\n    overflow: visible !important; /* 允许MobileNav悬浮按钮显示 */\n  }\n\n  /* 确保移动端不能滚动超过footer */\n  html, body {\n    overflow: hidden;\n    position: fixed;\n    width: 100%;\n    height: 100%;\n  }\n\n  #app {\n    overflow: hidden;\n    position: fixed;\n    width: 100%;\n    height: 100%;\n  }\n\n  .el-container {\n    height: 100vh;\n    display: flex;\n    flex-direction: column;\n  }\n}\n\n/* 通用样式 */\n.page-title {\n  margin-bottom: 20px;\n  font-weight: bold;\n  color: #303133;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.mb-20 {\n  margin-bottom: 20px;\n}\n\n.mt-20 {\n  margin-top: 20px;\n}\n\n/* 卡片样式 */\n.custom-card {\n  border-radius: 4px;\n  border: 1px solid #ebeef5;\n  background-color: #fff;\n  overflow: hidden;\n  color: #303133;\n  transition: 0.3s;\n  margin-bottom: 20px;\n}\n\n.custom-card:hover {\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n\n.custom-card-header {\n  padding: 18px 20px;\n  border-bottom: 1px solid #ebeef5;\n  box-sizing: border-box;\n}\n\n.custom-card-body {\n  padding: 20px;\n}\n\n/* 表单样式 */\n.form-container {\n  max-width: 600px;\n  margin: 0 auto;\n}\n\n/* 按钮样式 */\n.action-button {\n  margin-right: 10px;\n}\n\n/* 加载动画容器 */\n.loading-container {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  height: 100px;\n}\n\n/* 隐藏表格右侧的空单元格 */\n.el-table__body tr td:last-child:empty,\n.el-table__header tr th:last-child:empty,\n.el-table__body tr td:empty,\n.el-table__header tr th:empty {\n  display: none !important;\n}\n\n/* 设置表格内容居中 */\n.el-table th, .el-table td {\n  text-align: center !important;\n}\n\n/* 表格标题行样式 */\n.el-table th {\n  background-color: #f2f2f2 !important;\n  color: #333;\n  font-weight: bold;\n}\n\n/* 表格隔行变色 */\n.el-table--striped .el-table__body tr.el-table__row--striped td {\n  background-color: #fafafa;\n}\n\n/* 表格内容居中显示 */\n.el-table th.gutter,\n.el-table colgroup col:last-child {\n  display: none !important;\n}\n\n/* 隐藏表格右侧的空列 */\n.el-table__body-wrapper .el-table__body td:last-child:empty {\n  display: none !important;\n}\n\n/* 隐藏表格右侧的空列 */\n.el-table__fixed-right-patch {\n  display: none !important;\n}\n\n/* 固定公告栏在顶部 */\n.announcement-fixed {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  z-index: 2000;\n}\n\n/* 当公告栏显示时，给header添加上边距 */\n.announcement-fixed + .el-container .el-header {\n  margin-top: 40px;\n}\n\n/* 手机端适配 */\n@media (max-width: 768px) {\n  .announcement-fixed + .el-container .el-header {\n    margin-top: 60px;\n  }\n\n  /* 移动端非管理员路由隐藏header和footer */\n  .non-admin-route .el-header {\n    display: none !important;\n  }\n\n  .non-admin-route .el-footer {\n    display: none !important;\n  }\n\n  /* 移动端非管理员路由调整main区域 */\n  .non-admin-route .el-main {\n    height: 100vh !important;\n    max-height: 100vh !important;\n    padding: 0 !important;\n    margin: 0 !important;\n  }\n\n  /* 移动端管理员路由保持footer隐藏 */\n  .admin-route .el-footer {\n    display: none !important;\n  }\n}\n\n/* 管理员路由隐藏footer */\n.admin-route-hidden {\n  display: none !important;\n}\n\n/* 管理员路由的特殊样式 */\n.admin-route .el-container {\n  height: 100vh !important;\n}\n\n.admin-route .el-main {\n  height: 100vh !important;\n  max-height: 100vh !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n</style>\n"], "mappings": "AAkBA,OAAAA,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,eAAA;AACA,SAAAC,kBAAA;AACA,SAAAC,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAN,SAAA;IACAC,SAAA;IACAC;EACA;EACAK,KAAA;IACA;MACAC,aAAA;IACA;EACA;EACAC,QAAA;IACA,GAAAL,UAAA;IACAM,aAAA;MACA,YAAAC,MAAA,CAAAC,IAAA,CAAAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,iBAAA;IACA,KAAAC,UAAA;EACA;EACAC,OAAA;IACA,MAAAF,kBAAA;MACA;QACA,MAAAG,GAAA,SAAAf,kBAAA;QACA,KAAAK,aAAA,GAAAW,KAAA,CAAAC,OAAA,CAAAF,GAAA,IAAAA,GAAA;QACAG,OAAA,CAAAC,GAAA,uBAAAd,aAAA;MACA,SAAAe,KAAA;QACAF,OAAA,CAAAE,KAAA,YAAAA,KAAA;MACA;IACA;IACAP,WAAA;MACA;MACA,SAAAQ,UAAA;QACAC,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,GAAA;MACA;QACAH,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAE,MAAA;MACA;IACA;EACA;EACAC,KAAA;IACAN,UAAA;MACAO,QAAAC,QAAA;QACA;QACA,IAAAA,QAAA;UACAP,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAC,GAAA;QACA;UACAH,QAAA,CAAAC,eAAA,CAAAC,SAAA,CAAAE,MAAA;QACA;MACA;MACAI,SAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}