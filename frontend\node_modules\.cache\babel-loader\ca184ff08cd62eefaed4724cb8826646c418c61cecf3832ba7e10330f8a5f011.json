{"ast": null, "code": "import axios from 'axios';\nexport default {\n  name: 'EmailLogs',\n  data() {\n    return {\n      logList: [],\n      logTotal: 0,\n      logPage: 1,\n      logPageSize: 10,\n      logFilter: {\n        status: '',\n        event_type: ''\n      },\n      logContentDialogVisible: false,\n      selectedLog: null,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    };\n  },\n  computed: {\n    // 根据屏幕宽度动态调整分页组件布局\n    paginationLayout() {\n      return this.isMobile ? 'prev, next' : 'prev, pager, next, jumper';\n    }\n  },\n  created() {\n    this.fetchLogs();\n    // 添加窗口大小变化的监听器\n    window.addEventListener('resize', this.handleResize);\n  },\n  beforeDestroy() {\n    // 移除窗口大小变化的监听器\n    window.removeEventListener('resize', this.handleResize);\n  },\n  methods: {\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    },\n    async fetchLogs() {\n      try {\n        const params = {\n          skip: (this.logPage - 1) * this.logPageSize,\n          limit: this.logPageSize,\n          status: this.logFilter.status,\n          event_type: this.logFilter.event_type\n        };\n        const res = await axios.get('/api/admin/email/logs', {\n          params\n        });\n        this.logList = res.data.items;\n        this.logTotal = res.data.total;\n      } catch (e) {\n        this.$message.error('获取日志失败');\n      }\n    },\n    clearLogFilter() {\n      this.logFilter = {\n        status: '',\n        event_type: ''\n      };\n      this.logPage = 1;\n      this.fetchLogs();\n    },\n    formatDate(val) {\n      if (!val) return '';\n      // 将日期字符串拆分并手动构建Date对象，避免自动时区转换\n      try {\n        // 假设输入格式为 \"YYYY-MM-DD HH:MM:SS\" 或 ISO格式\n        let dateTimeStr = val;\n        // 如果是ISO格式带T和Z，则去掉\n        if (typeof val === 'string' && val.includes('T')) {\n          dateTimeStr = val.replace('T', ' ').replace('Z', '');\n        }\n\n        // 将日期时间字符串分割为组件\n        const parts = /(\\d{4})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2})(?::(\\d{2}))?/.exec(dateTimeStr);\n        if (parts) {\n          const year = parseInt(parts[1]);\n          const month = parseInt(parts[2]) - 1; // 月份从0开始\n          const day = parseInt(parts[3]);\n          const hour = parseInt(parts[4]);\n          const minute = parseInt(parts[5]);\n          const second = parts[6] ? parseInt(parts[6]) : 0;\n\n          // 直接使用提取的时间组件，避免时区转换\n          return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;\n        }\n      } catch (e) {\n        console.error('解析日期失败:', e);\n      }\n\n      // 如果解析失败，回退到简单方法\n      const d = new Date(val);\n      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;\n    },\n    showLogContent(log) {\n      this.selectedLog = log;\n      this.logContentDialogVisible = true;\n    },\n    // 处理窗口大小变化\n    handleResize() {\n      this.isMobile = window.innerWidth <= 768;\n    }\n  }\n};", "map": {"version": 3, "names": ["axios", "name", "data", "logList", "logTotal", "logPage", "logPageSize", "logFilter", "status", "event_type", "logContentDialogVisible", "<PERSON><PERSON><PERSON>", "isMobile", "window", "innerWidth", "computed", "paginationLayout", "created", "fetchLogs", "addEventListener", "handleResize", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "methods", "params", "skip", "limit", "res", "get", "items", "total", "e", "$message", "error", "clearLog<PERSON><PERSON>er", "formatDate", "val", "dateTimeStr", "includes", "replace", "parts", "exec", "year", "parseInt", "month", "day", "hour", "minute", "second", "String", "padStart", "console", "d", "Date", "getFullYear", "getMonth", "getDate", "getHours", "getMinutes", "show<PERSON><PERSON><PERSON><PERSON><PERSON>", "log"], "sources": ["src/views/admin/EmailLogs.vue"], "sourcesContent": ["<template>\r\n  <div class=\"email-logs\">\r\n    <!-- 移动端提示 -->\r\n    <div v-if=\"isMobile\" class=\"mobile-notice\">\r\n      <el-card shadow=\"hover\">\r\n        <div style=\"text-align: center; padding: 40px 20px;\">\r\n          <i class=\"el-icon-tickets\" style=\"font-size: 48px; color: #409EFF; margin-bottom: 20px;\"></i>\r\n          <h3>邮件日志</h3>\r\n          <p style=\"color: #666; margin: 20px 0;\">\r\n            邮件日志功能需要在桌面端使用，以获得更好的表格显示效果。\r\n          </p>\r\n          <p style=\"color: #666; font-size: 14px;\">\r\n            请使用电脑或平板设备访问此功能。\r\n          </p>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 桌面端完整功能 -->\r\n    <div v-else>\r\n      <el-form :inline=\"true\" size=\"small\" style=\"margin-bottom:10px;\">\r\n      <el-form-item label=\"状态\">\r\n        <el-select v-model=\"logFilter.status\" clearable placeholder=\"全部\">\r\n          <el-option label=\"成功\" value=\"success\"/>\r\n          <el-option label=\"失败\" value=\"failed\"/>\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"事件类型\">\r\n        <el-input v-model=\"logFilter.event_type\" placeholder=\"如 reservation_created\" clearable></el-input>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button @click=\"fetchLogs\" type=\"primary\">查询</el-button>\r\n        <el-button @click=\"clearLogFilter\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    <el-table :data=\"logList\" border style=\"width: 100%\">\r\n      <el-table-column prop=\"recipient\" label=\"收件人\" min-width=\"120\"/>\r\n      <el-table-column prop=\"subject\" label=\"主题\" min-width=\"150\"/>\r\n      <el-table-column prop=\"event_type\" label=\"事件类型\" min-width=\"120\"/>\r\n      <el-table-column prop=\"status\" label=\"状态\" min-width=\"40\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.status === 'success' ? 'success' : 'danger'\">\r\n            {{ scope.row.status === 'success' ? '成功' : '失败' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"created_at\" label=\"时间\" min-width=\"40\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatDate(scope.row.created_at) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"showLogContent(scope.row)\">查看内容</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <el-pagination\r\n      style=\"margin-top:10px;text-align:right\"\r\n      background\r\n      :layout=\"paginationLayout\"\r\n      :total=\"logTotal\"\r\n      :page-size=\"logPageSize\"\r\n      :current-page.sync=\"logPage\"\r\n      @current-change=\"fetchLogs\"\r\n    />\r\n    <el-dialog\r\n      title=\"邮件内容\"\r\n      :visible.sync=\"logContentDialogVisible\"\r\n      width=\"60%\">\r\n      <div v-if=\"selectedLog && selectedLog.content_html\" v-html=\"selectedLog.content_html\"></div>\r\n      <div v-else>暂无内容</div>\r\n    </el-dialog>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EmailLogs',\r\n  data() {\r\n    return {\r\n      logList: [],\r\n      logTotal: 0,\r\n      logPage: 1,\r\n      logPageSize: 10,\r\n      logFilter: {\r\n        status: '',\r\n        event_type: ''\r\n      },\r\n      logContentDialogVisible: false,\r\n      selectedLog: null,\r\n      // 响应式布局相关\r\n      isMobile: window.innerWidth <= 768\r\n    }\r\n  },\r\n\r\n  computed: {\r\n    // 根据屏幕宽度动态调整分页组件布局\r\n    paginationLayout() {\r\n      return this.isMobile\r\n        ? 'prev, next'\r\n        : 'prev, pager, next, jumper';\r\n    }\r\n  },\r\n  created() {\r\n    this.fetchLogs()\r\n    // 添加窗口大小变化的监听器\r\n    window.addEventListener('resize', this.handleResize)\r\n  },\r\n\r\n  beforeDestroy() {\r\n    // 移除窗口大小变化的监听器\r\n    window.removeEventListener('resize', this.handleResize)\r\n  },\r\n  methods: {\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      this.isMobile = window.innerWidth <= 768\r\n    },\r\n\r\n    async fetchLogs() {\r\n      try {\r\n        const params = {\r\n          skip: (this.logPage - 1) * this.logPageSize,\r\n          limit: this.logPageSize,\r\n          status: this.logFilter.status,\r\n          event_type: this.logFilter.event_type\r\n        }\r\n        const res = await axios.get('/api/admin/email/logs', { params })\r\n        this.logList = res.data.items\r\n        this.logTotal = res.data.total\r\n      } catch (e) {\r\n        this.$message.error('获取日志失败')\r\n      }\r\n    },\r\n    clearLogFilter() {\r\n      this.logFilter = { status: '', event_type: '' }\r\n      this.logPage = 1\r\n      this.fetchLogs()\r\n    },\r\n    formatDate(val) {\r\n      if (!val) return ''\r\n      // 将日期字符串拆分并手动构建Date对象，避免自动时区转换\r\n      try {\r\n        // 假设输入格式为 \"YYYY-MM-DD HH:MM:SS\" 或 ISO格式\r\n        let dateTimeStr = val;\r\n        // 如果是ISO格式带T和Z，则去掉\r\n        if (typeof val === 'string' && val.includes('T')) {\r\n          dateTimeStr = val.replace('T', ' ').replace('Z', '');\r\n        }\r\n\r\n        // 将日期时间字符串分割为组件\r\n        const parts = /(\\d{4})-(\\d{2})-(\\d{2}) (\\d{2}):(\\d{2})(?::(\\d{2}))?/.exec(dateTimeStr);\r\n        if (parts) {\r\n          const year = parseInt(parts[1]);\r\n          const month = parseInt(parts[2]) - 1; // 月份从0开始\r\n          const day = parseInt(parts[3]);\r\n          const hour = parseInt(parts[4]);\r\n          const minute = parseInt(parts[5]);\r\n          const second = parts[6] ? parseInt(parts[6]) : 0;\r\n\r\n          // 直接使用提取的时间组件，避免时区转换\r\n          return `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')} ${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}`;\r\n        }\r\n      } catch (e) {\r\n        console.error('解析日期失败:', e);\r\n      }\r\n\r\n      // 如果解析失败，回退到简单方法\r\n      const d = new Date(val);\r\n      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;\r\n    },\r\n    showLogContent(log) {\r\n      this.selectedLog = log;\r\n      this.logContentDialogVisible = true;\r\n    },\r\n\r\n    // 处理窗口大小变化\r\n    handleResize() {\r\n      this.isMobile = window.innerWidth <= 768;\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.email-logs {\r\n  padding: 20px;\r\n}\r\n</style>"], "mappings": "AA8EA,OAAAA,KAAA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;MACAC,OAAA;MACAC,WAAA;MACAC,SAAA;QACAC,MAAA;QACAC,UAAA;MACA;MACAC,uBAAA;MACAC,WAAA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;IACA;EACA;EAEAC,QAAA;IACA;IACAC,iBAAA;MACA,YAAAJ,QAAA,GACA,eACA;IACA;EACA;EACAK,QAAA;IACA,KAAAC,SAAA;IACA;IACAL,MAAA,CAAAM,gBAAA,gBAAAC,YAAA;EACA;EAEAC,cAAA;IACA;IACAR,MAAA,CAAAS,mBAAA,gBAAAF,YAAA;EACA;EACAG,OAAA;IACA;IACAH,aAAA;MACA,KAAAR,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;IAEA,MAAAI,UAAA;MACA;QACA,MAAAM,MAAA;UACAC,IAAA,QAAApB,OAAA,aAAAC,WAAA;UACAoB,KAAA,OAAApB,WAAA;UACAE,MAAA,OAAAD,SAAA,CAAAC,MAAA;UACAC,UAAA,OAAAF,SAAA,CAAAE;QACA;QACA,MAAAkB,GAAA,SAAA3B,KAAA,CAAA4B,GAAA;UAAAJ;QAAA;QACA,KAAArB,OAAA,GAAAwB,GAAA,CAAAzB,IAAA,CAAA2B,KAAA;QACA,KAAAzB,QAAA,GAAAuB,GAAA,CAAAzB,IAAA,CAAA4B,KAAA;MACA,SAAAC,CAAA;QACA,KAAAC,QAAA,CAAAC,KAAA;MACA;IACA;IACAC,eAAA;MACA,KAAA3B,SAAA;QAAAC,MAAA;QAAAC,UAAA;MAAA;MACA,KAAAJ,OAAA;MACA,KAAAa,SAAA;IACA;IACAiB,WAAAC,GAAA;MACA,KAAAA,GAAA;MACA;MACA;QACA;QACA,IAAAC,WAAA,GAAAD,GAAA;QACA;QACA,WAAAA,GAAA,iBAAAA,GAAA,CAAAE,QAAA;UACAD,WAAA,GAAAD,GAAA,CAAAG,OAAA,WAAAA,OAAA;QACA;;QAEA;QACA,MAAAC,KAAA,0DAAAC,IAAA,CAAAJ,WAAA;QACA,IAAAG,KAAA;UACA,MAAAE,IAAA,GAAAC,QAAA,CAAAH,KAAA;UACA,MAAAI,KAAA,GAAAD,QAAA,CAAAH,KAAA;UACA,MAAAK,GAAA,GAAAF,QAAA,CAAAH,KAAA;UACA,MAAAM,IAAA,GAAAH,QAAA,CAAAH,KAAA;UACA,MAAAO,MAAA,GAAAJ,QAAA,CAAAH,KAAA;UACA,MAAAQ,MAAA,GAAAR,KAAA,MAAAG,QAAA,CAAAH,KAAA;;UAEA;UACA,UAAAE,IAAA,IAAAO,MAAA,CAAAL,KAAA,MAAAM,QAAA,YAAAD,MAAA,CAAAJ,GAAA,EAAAK,QAAA,YAAAD,MAAA,CAAAH,IAAA,EAAAI,QAAA,YAAAD,MAAA,CAAAF,MAAA,EAAAG,QAAA;QACA;MACA,SAAAnB,CAAA;QACAoB,OAAA,CAAAlB,KAAA,YAAAF,CAAA;MACA;;MAEA;MACA,MAAAqB,CAAA,OAAAC,IAAA,CAAAjB,GAAA;MACA,UAAAgB,CAAA,CAAAE,WAAA,MAAAL,MAAA,CAAAG,CAAA,CAAAG,QAAA,QAAAL,QAAA,YAAAD,MAAA,CAAAG,CAAA,CAAAI,OAAA,IAAAN,QAAA,YAAAD,MAAA,CAAAG,CAAA,CAAAK,QAAA,IAAAP,QAAA,YAAAD,MAAA,CAAAG,CAAA,CAAAM,UAAA,IAAAR,QAAA;IACA;IACAS,eAAAC,GAAA;MACA,KAAAjD,WAAA,GAAAiD,GAAA;MACA,KAAAlD,uBAAA;IACA;IAEA;IACAU,aAAA;MACA,KAAAR,QAAA,GAAAC,MAAA,CAAAC,UAAA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}