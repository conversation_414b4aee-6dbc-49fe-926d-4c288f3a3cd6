{"ast": null, "code": "import { fetchAllAnnouncements, createAnnouncement, updateAnnouncement, deleteAnnouncement } from '@/api/announcement';\nexport default {\n  name: 'AnnouncementManage',\n  data() {\n    return {\n      announcements: [],\n      form: {\n        id: null,\n        title: '',\n        content: '',\n        is_active: true\n      },\n      editMode: false,\n      // 响应式布局相关\n      isMobile: window.innerWidth <= 768\n    };\n  },\n  created() {\n    this.loadAnnouncements();\n  },\n  methods: {\n    // 格式化日期为YYYY-MM-DD HH:mm格式\n    formatDate(dateStr) {\n      if (!dateStr) return '';\n      const date = new Date(dateStr);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    },\n    async loadAnnouncements() {\n      try {\n        const res = await fetchAllAnnouncements();\n        this.announcements = Array.isArray(res) ? res : [];\n        console.log('加载到所有公告数据:', this.announcements);\n      } catch (e) {\n        console.error('公告加载失败:', e);\n        this.$message.error('公告加载失败');\n      }\n    },\n    addAnnouncement() {\n      this.form = {\n        id: null,\n        title: '',\n        content: '',\n        is_active: true\n      };\n      this.editMode = true;\n    },\n    editAnnouncement(row) {\n      this.form = {\n        ...row\n      };\n      this.editMode = true;\n    },\n    cancelEdit() {\n      this.editMode = false;\n      this.form = {\n        id: null,\n        title: '',\n        content: '',\n        is_active: true\n      };\n    },\n    async submitForm() {\n      if (!this.form.title || !this.form.content) {\n        this.$message.error('标题和内容不能为空');\n        return;\n      }\n      try {\n        if (this.form.id) {\n          await updateAnnouncement(this.form.id, this.form);\n          this.$message.success('公告已更新');\n        } else {\n          await createAnnouncement(this.form);\n          this.$message.success('公告已发布');\n        }\n        this.editMode = false;\n        this.loadAnnouncements();\n      } catch (e) {\n        this.$message.error('操作失败');\n      }\n    },\n    async deleteAnnouncement(id) {\n      try {\n        await deleteAnnouncement(id);\n        this.$message.success('公告已删除');\n        this.loadAnnouncements();\n      } catch (e) {\n        this.$message.error('删除失败');\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["fetchAllAnnouncements", "createAnnouncement", "updateAnnouncement", "deleteAnnouncement", "name", "data", "announcements", "form", "id", "title", "content", "is_active", "editMode", "isMobile", "window", "innerWidth", "created", "loadAnnouncements", "methods", "formatDate", "dateStr", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "res", "Array", "isArray", "console", "log", "e", "error", "$message", "addAnnouncement", "editAnnouncement", "row", "cancelEdit", "submitForm", "success"], "sources": ["src/views/admin/AnnouncementManage.vue"], "sourcesContent": ["<template>\r\n  <div class=\"announcement-manage\">\r\n    <h2>公告管理</h2>\r\n    <el-form :model=\"form\" ref=\"formRef\" label-width=\"80px\" class=\"form-container\" v-if=\"editMode\">\r\n      <el-form-item label=\"标题\">\r\n        <el-input v-model=\"form.title\" maxlength=\"200\" show-word-limit />\r\n      </el-form-item>\r\n      <el-form-item label=\"内容\">\r\n        <el-input v-model=\"form.content\" type=\"textarea\" rows=\"3\" maxlength=\"1000\" show-word-limit />\r\n      </el-form-item>\r\n      <el-form-item label=\"有效\">\r\n        <el-switch v-model=\"form.is_active\" />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"submitForm\">{{ form.id ? '更新' : '发布' }}</el-button>\r\n        <el-button @click=\"cancelEdit\">取消</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-button type=\"primary\" @click=\"addAnnouncement\" v-if=\"!editMode\" style=\"margin-bottom: 16px;\">发布新公告</el-button>\r\n\r\n    <!-- 移动端卡片视图 -->\r\n    <div v-if=\"isMobile && announcements.length\" class=\"mobile-card-container\">\r\n      <div\r\n        v-for=\"announcement in announcements\"\r\n        :key=\"announcement.id\"\r\n        class=\"announcement-mobile-card\"\r\n      >\r\n        <div class=\"card-header\">\r\n          <div class=\"announcement-info\">\r\n            <div class=\"announcement-id\">ID: {{ announcement.id }}</div>\r\n            <div class=\"announcement-title\">{{ announcement.title }}</div>\r\n          </div>\r\n          <el-tag :type=\"announcement.is_active ? 'success' : 'info'\">\r\n            {{ announcement.is_active ? '是' : '否' }}\r\n          </el-tag>\r\n        </div>\r\n\r\n        <div class=\"card-content\">\r\n          <div class=\"info-row\">\r\n            <span class=\"label\">内容:</span>\r\n            <span class=\"value\">{{ announcement.content }}</span>\r\n          </div>\r\n          <div class=\"info-row\">\r\n            <span class=\"label\">发布时间:</span>\r\n            <span class=\"value\">{{ formatDate(announcement.created_at) }}</span>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"card-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            @click=\"editAnnouncement(announcement)\"\r\n          >\r\n            编辑\r\n          </el-button>\r\n          <el-button\r\n            type=\"danger\"\r\n            size=\"small\"\r\n            @click=\"deleteAnnouncement(announcement.id)\"\r\n          >\r\n            删除\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 桌面端表格视图 -->\r\n    <el-table v-else-if=\"!isMobile && announcements.length\" :data=\"announcements\" stripe style=\"width: 100%\">\r\n      <el-table-column prop=\"id\" label=\"ID\" width=\"60\" />\r\n      <el-table-column prop=\"title\" label=\"标题\" />\r\n      <el-table-column prop=\"content\" label=\"内容\" />\r\n      <el-table-column prop=\"created_at\" label=\"发布时间\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          {{ formatDate(scope.row.created_at) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column prop=\"is_active\" label=\"有效\" width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-tag :type=\"scope.row.is_active ? 'success' : 'info'\">\r\n            {{ scope.row.is_active ? '是' : '否' }}\r\n          </el-tag>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" width=\"180\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button size=\"mini\" @click=\"editAnnouncement(scope.row)\">编辑</el-button>\r\n          <el-button size=\"mini\" type=\"danger\" @click=\"deleteAnnouncement(scope.row.id)\">删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    <div v-else class=\"empty-tip\">暂无公告</div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  fetchAllAnnouncements,\r\n  createAnnouncement,\r\n  updateAnnouncement,\r\n  deleteAnnouncement\r\n} from '@/api/announcement'\r\n\r\nexport default {\r\n  name: 'AnnouncementManage',\r\n  data() {\r\n    return {\r\n      announcements: [],\r\n      form: {\r\n        id: null,\r\n        title: '',\r\n        content: '',\r\n        is_active: true\r\n      },\r\n      editMode: false,\r\n      // 响应式布局相关\r\n      isMobile: window.innerWidth <= 768\r\n    }\r\n  },\r\n  created() {\r\n    this.loadAnnouncements()\r\n  },\r\n  methods: {\r\n    // 格式化日期为YYYY-MM-DD HH:mm格式\r\n    formatDate(dateStr) {\r\n      if (!dateStr) return '';\r\n      const date = new Date(dateStr);\r\n      const year = date.getFullYear();\r\n      const month = String(date.getMonth() + 1).padStart(2, '0');\r\n      const day = String(date.getDate()).padStart(2, '0');\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n    },\r\n    async loadAnnouncements() {\r\n      try {\r\n        const res = await fetchAllAnnouncements()\r\n        this.announcements = Array.isArray(res) ? res : []\r\n        console.log('加载到所有公告数据:', this.announcements)\r\n      } catch (e) {\r\n        console.error('公告加载失败:', e)\r\n        this.$message.error('公告加载失败')\r\n      }\r\n    },\r\n    addAnnouncement() {\r\n      this.form = { id: null, title: '', content: '', is_active: true }\r\n      this.editMode = true\r\n    },\r\n    editAnnouncement(row) {\r\n      this.form = { ...row }\r\n      this.editMode = true\r\n    },\r\n    cancelEdit() {\r\n      this.editMode = false\r\n      this.form = { id: null, title: '', content: '', is_active: true }\r\n    },\r\n    async submitForm() {\r\n      if (!this.form.title || !this.form.content) {\r\n        this.$message.error('标题和内容不能为空')\r\n        return\r\n      }\r\n      try {\r\n        if (this.form.id) {\r\n          await updateAnnouncement(this.form.id, this.form)\r\n          this.$message.success('公告已更新')\r\n        } else {\r\n          await createAnnouncement(this.form)\r\n          this.$message.success('公告已发布')\r\n        }\r\n        this.editMode = false\r\n        this.loadAnnouncements()\r\n      } catch (e) {\r\n        this.$message.error('操作失败')\r\n      }\r\n    },\r\n    async deleteAnnouncement(id) {\r\n      try {\r\n        await deleteAnnouncement(id)\r\n        this.$message.success('公告已删除')\r\n        this.loadAnnouncements()\r\n      } catch (e) {\r\n        this.$message.error('删除失败')\r\n      }\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.announcement-manage {\r\n  max-width: 900px;\r\n  margin: 0 auto;\r\n  background: #fff;\r\n  padding: 24px;\r\n  border-radius: 6px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n.form-container {\r\n  margin-bottom: 24px;\r\n}\r\n.empty-tip {\r\n  text-align: center;\r\n  color: #999;\r\n  margin: 32px 0;\r\n  font-size: 16px;\r\n}\r\n</style>"], "mappings": "AAiGA,SACAA,qBAAA,EACAC,kBAAA,EACAC,kBAAA,EACAC,kBAAA,QACA;AAEA;EACAC,IAAA;EACAC,KAAA;IACA;MACAC,aAAA;MACAC,IAAA;QACAC,EAAA;QACAC,KAAA;QACAC,OAAA;QACAC,SAAA;MACA;MACAC,QAAA;MACA;MACAC,QAAA,EAAAC,MAAA,CAAAC,UAAA;IACA;EACA;EACAC,QAAA;IACA,KAAAC,iBAAA;EACA;EACAC,OAAA;IACA;IACAC,WAAAC,OAAA;MACA,KAAAA,OAAA;MACA,MAAAC,IAAA,OAAAC,IAAA,CAAAF,OAAA;MACA,MAAAG,IAAA,GAAAF,IAAA,CAAAG,WAAA;MACA,MAAAC,KAAA,GAAAC,MAAA,CAAAL,IAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,MAAAC,GAAA,GAAAH,MAAA,CAAAL,IAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,MAAAG,KAAA,GAAAL,MAAA,CAAAL,IAAA,CAAAW,QAAA,IAAAJ,QAAA;MACA,MAAAK,OAAA,GAAAP,MAAA,CAAAL,IAAA,CAAAa,UAAA,IAAAN,QAAA;MACA,UAAAL,IAAA,IAAAE,KAAA,IAAAI,GAAA,IAAAE,KAAA,IAAAE,OAAA;IACA;IACA,MAAAhB,kBAAA;MACA;QACA,MAAAkB,GAAA,SAAAnC,qBAAA;QACA,KAAAM,aAAA,GAAA8B,KAAA,CAAAC,OAAA,CAAAF,GAAA,IAAAA,GAAA;QACAG,OAAA,CAAAC,GAAA,oBAAAjC,aAAA;MACA,SAAAkC,CAAA;QACAF,OAAA,CAAAG,KAAA,YAAAD,CAAA;QACA,KAAAE,QAAA,CAAAD,KAAA;MACA;IACA;IACAE,gBAAA;MACA,KAAApC,IAAA;QAAAC,EAAA;QAAAC,KAAA;QAAAC,OAAA;QAAAC,SAAA;MAAA;MACA,KAAAC,QAAA;IACA;IACAgC,iBAAAC,GAAA;MACA,KAAAtC,IAAA;QAAA,GAAAsC;MAAA;MACA,KAAAjC,QAAA;IACA;IACAkC,WAAA;MACA,KAAAlC,QAAA;MACA,KAAAL,IAAA;QAAAC,EAAA;QAAAC,KAAA;QAAAC,OAAA;QAAAC,SAAA;MAAA;IACA;IACA,MAAAoC,WAAA;MACA,UAAAxC,IAAA,CAAAE,KAAA,UAAAF,IAAA,CAAAG,OAAA;QACA,KAAAgC,QAAA,CAAAD,KAAA;QACA;MACA;MACA;QACA,SAAAlC,IAAA,CAAAC,EAAA;UACA,MAAAN,kBAAA,MAAAK,IAAA,CAAAC,EAAA,OAAAD,IAAA;UACA,KAAAmC,QAAA,CAAAM,OAAA;QACA;UACA,MAAA/C,kBAAA,MAAAM,IAAA;UACA,KAAAmC,QAAA,CAAAM,OAAA;QACA;QACA,KAAApC,QAAA;QACA,KAAAK,iBAAA;MACA,SAAAuB,CAAA;QACA,KAAAE,QAAA,CAAAD,KAAA;MACA;IACA;IACA,MAAAtC,mBAAAK,EAAA;MACA;QACA,MAAAL,kBAAA,CAAAK,EAAA;QACA,KAAAkC,QAAA,CAAAM,OAAA;QACA,KAAA/B,iBAAA;MACA,SAAAuB,CAAA;QACA,KAAAE,QAAA,CAAAD,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}