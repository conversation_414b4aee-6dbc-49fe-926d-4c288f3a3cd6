<template>
  <div class="announcement-manage">
    <h2>公告管理</h2>
    <el-form :model="form" ref="formRef" label-width="80px" class="form-container" v-if="editMode">
      <el-form-item label="标题">
        <el-input v-model="form.title" maxlength="200" show-word-limit />
      </el-form-item>
      <el-form-item label="内容">
        <el-input v-model="form.content" type="textarea" rows="3" maxlength="1000" show-word-limit />
      </el-form-item>
      <el-form-item label="有效">
        <el-switch v-model="form.is_active" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm">{{ form.id ? '更新' : '发布' }}</el-button>
        <el-button @click="cancelEdit">取消</el-button>
      </el-form-item>
    </el-form>

    <el-button type="primary" @click="addAnnouncement" v-if="!editMode" style="margin-bottom: 16px;">发布新公告</el-button>

    <!-- 移动端卡片视图 -->
    <div v-if="isMobile && announcements.length" class="mobile-card-container">
      <div
        v-for="announcement in announcements"
        :key="announcement.id"
        class="announcement-mobile-card"
      >
        <div class="card-header">
          <div class="announcement-info">
            <div class="announcement-id">ID: {{ announcement.id }}</div>
            <div class="announcement-title">{{ announcement.title }}</div>
          </div>
          <el-tag :type="announcement.is_active ? 'success' : 'info'">
            {{ announcement.is_active ? '是' : '否' }}
          </el-tag>
        </div>

        <div class="card-content">
          <div class="info-row">
            <span class="label">内容:</span>
            <span class="value">{{ announcement.content }}</span>
          </div>
          <div class="info-row">
            <span class="label">发布时间:</span>
            <span class="value">{{ formatDate(announcement.created_at) }}</span>
          </div>
        </div>

        <div class="card-actions">
          <el-button
            type="primary"
            size="small"
            @click="editAnnouncement(announcement)"
          >
            编辑
          </el-button>
          <el-button
            type="danger"
            size="small"
            @click="deleteAnnouncement(announcement.id)"
          >
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 桌面端表格视图 -->
    <el-table v-else-if="!isMobile && announcements.length" :data="announcements" stripe style="width: 100%">
      <el-table-column prop="id" label="ID" width="60" />
      <el-table-column prop="title" label="标题" />
      <el-table-column prop="content" label="内容" />
      <el-table-column prop="created_at" label="发布时间" width="180">
        <template slot-scope="scope">
          {{ formatDate(scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column prop="is_active" label="有效" width="80">
        <template slot-scope="scope">
          <el-tag :type="scope.row.is_active ? 'success' : 'info'">
            {{ scope.row.is_active ? '是' : '否' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180">
        <template slot-scope="scope">
          <el-button size="mini" @click="editAnnouncement(scope.row)">编辑</el-button>
          <el-button size="mini" type="danger" @click="deleteAnnouncement(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-else class="empty-tip">暂无公告</div>
  </div>
</template>

<script>
import {
  fetchAllAnnouncements,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement
} from '@/api/announcement'

export default {
  name: 'AnnouncementManage',
  data() {
    return {
      announcements: [],
      form: {
        id: null,
        title: '',
        content: '',
        is_active: true
      },
      editMode: false,
      // 响应式布局相关
      isMobile: window.innerWidth <= 768
    }
  },
  created() {
    this.loadAnnouncements()
    // 添加窗口大小变化的监听器
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    // 移除窗口大小变化的监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    // 处理窗口大小变化
    handleResize() {
      this.isMobile = window.innerWidth <= 768
    },

    // 格式化日期为YYYY-MM-DD HH:mm格式
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    },
    async loadAnnouncements() {
      try {
        const res = await fetchAllAnnouncements()
        this.announcements = Array.isArray(res) ? res : []
        console.log('加载到所有公告数据:', this.announcements)
      } catch (e) {
        console.error('公告加载失败:', e)
        this.$message.error('公告加载失败')
      }
    },
    addAnnouncement() {
      this.form = { id: null, title: '', content: '', is_active: true }
      this.editMode = true
    },
    editAnnouncement(row) {
      this.form = { ...row }
      this.editMode = true
    },
    cancelEdit() {
      this.editMode = false
      this.form = { id: null, title: '', content: '', is_active: true }
    },
    async submitForm() {
      if (!this.form.title || !this.form.content) {
        this.$message.error('标题和内容不能为空')
        return
      }
      try {
        if (this.form.id) {
          await updateAnnouncement(this.form.id, this.form)
          this.$message.success('公告已更新')
        } else {
          await createAnnouncement(this.form)
          this.$message.success('公告已发布')
        }
        this.editMode = false
        this.loadAnnouncements()
      } catch (e) {
        this.$message.error('操作失败')
      }
    },
    async deleteAnnouncement(id) {
      try {
        await deleteAnnouncement(id)
        this.$message.success('公告已删除')
        this.loadAnnouncements()
      } catch (e) {
        this.$message.error('删除失败')
      }
    }
  }
}
</script>

<style scoped>
.announcement-manage {
  max-width: 900px;
  margin: 0 auto;
  background: #fff;
  padding: 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}
.form-container {
  margin-bottom: 24px;
}
/* 移动端卡片样式 */
.mobile-card-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.announcement-mobile-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 15px;
  border: 1px solid #e8e8e8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.announcement-info {
  flex: 1;
}

.announcement-id {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.announcement-title {
  font-weight: bold;
  font-size: 14px;
  color: #333;
}

.card-content {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
  align-items: flex-start;
}

.info-row .label {
  min-width: 70px;
  font-size: 13px;
  color: #666;
  margin-right: 10px;
  flex-shrink: 0;
}

.info-row .value {
  font-size: 13px;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.card-actions {
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid #f0f0f0;
}

.card-actions .el-button {
  margin: 0 5px;
}

.empty-tip {
  text-align: center;
  color: #999;
  margin: 32px 0;
  font-size: 16px;
}

@media (max-width: 768px) {
  .announcement-manage {
    padding: 15px;
    padding-top: 70px;
    padding-bottom: 100px;
  }
}
</style>