{"ast": null, "code": "import { mapGetters, mapActions } from 'vuex';\nimport { updatePageTitle } from '@/router/permission';\nexport default {\n  name: 'AdminLayout',\n  data() {\n    return {\n      mobileMenuOpen: false\n    };\n  },\n  computed: {\n    ...mapGetters(['currentUser', 'getLanguage']),\n    activeMenu() {\n      return this.$route.path;\n    },\n    currentLanguage() {\n      return this.getLanguage;\n    },\n    displayUsername() {\n      // 优先显示用户名，如果没有则显示'管理员'\n      return this.currentUser && this.currentUser.username ? this.currentUser.username : '管理员';\n    },\n    isSuperAdmin() {\n      return this.currentUser && this.currentUser.role === 'superadmin';\n    }\n  },\n  methods: {\n    ...mapActions(['logout', 'setLanguage']),\n    handleCommand(command) {\n      if (command === 'logout') {\n        this.handleLogout();\n      } else if (command === 'home') {\n        this.$router.push('/');\n      }\n    },\n    handleLogout() {\n      this.logout();\n      this.$message.success('退出登录成功');\n      this.$router.push('/admin/login');\n    },\n    handleLanguageChange(lang) {\n      this.setLanguage(lang);\n      this.$i18n.locale = lang;\n\n      // 更新页面标题\n      setTimeout(() => {\n        updatePageTitle();\n      }, 0);\n    },\n    handleMobileMenuSelect(key) {\n      this.$router.push(key);\n      this.mobileMenuOpen = false;\n    },\n    handleMobileCommand(command) {\n      if (command === 'logout') {\n        this.handleLogout();\n      } else if (command === 'home') {\n        this.$router.push('/');\n      }\n      this.mobileMenuOpen = false;\n    },\n    toggleMobileMenu() {\n      // 这里可以实现折叠菜单的逻辑，或 emit 事件给父组件\n      const menu = document.querySelector('.header-menu');\n      if (menu) {\n        menu.style.display = menu.style.display === 'none' ? '' : 'none';\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["mapGetters", "mapActions", "updatePageTitle", "name", "data", "mobileMenuOpen", "computed", "activeMenu", "$route", "path", "currentLanguage", "getLanguage", "displayUsername", "currentUser", "username", "isSuperAdmin", "role", "methods", "handleCommand", "command", "handleLogout", "$router", "push", "logout", "$message", "success", "handleLanguageChange", "lang", "setLanguage", "$i18n", "locale", "setTimeout", "handleMobileMenuSelect", "key", "handleMobileCommand", "toggleMobileMenu", "menu", "document", "querySelector", "style", "display"], "sources": ["src/views/admin/AdminLayout.vue"], "sourcesContent": ["<template>\n  <div class=\"admin-layout\">\n    <!-- 移动端菜单按钮 -->\n    <div class=\"admin-mobile-nav-toggle\" @click=\"mobileMenuOpen = true\">\n      <i class=\"el-icon-menu\"></i>\n    </div>\n    <!-- 移动端抽屉菜单和遮罩层 -->\n    <div v-if=\"mobileMenuOpen\" class=\"admin-mobile-nav-overlay\" @click=\"mobileMenuOpen = false\"></div>\n    <div v-if=\"mobileMenuOpen\" class=\"admin-mobile-nav-drawer\">\n      <div class=\"admin-mobile-nav-header\">\n        <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"admin-mobile-nav-logo\">\n        <h3 class=\"admin-mobile-nav-title\">管理控制台</h3>\n        <div class=\"admin-mobile-nav-close\" @click=\"mobileMenuOpen = false\">\n          <i class=\"el-icon-close\"></i>\n        </div>\n      </div>\n\n      <!-- 用户信息区域 -->\n      <div class=\"admin-mobile-user-info\">\n        <div class=\"user-avatar\">\n          <i class=\"el-icon-user\"></i>\n        </div>\n        <div class=\"user-details\">\n          <div class=\"user-name\">{{ displayUsername }}</div>\n          <div class=\"user-role\">管理员</div>\n        </div>\n      </div>\n\n      <el-menu\n        :default-active=\"activeMenu\"\n        class=\"admin-mobile-nav-list\"\n        background-color=\"#304156\"\n        text-color=\"#bfcbd9\"\n        active-text-color=\"#409EFF\"\n        @select=\"handleMobileMenuSelect\"\n      >\n        <el-menu-item index=\"/admin/dashboard\">\n          <i class=\"el-icon-s-home\"></i>\n          <span>控制台</span>\n        </el-menu-item>\n        <el-submenu index=\"equipment\">\n          <template slot=\"title\">\n            <i class=\"el-icon-s-grid\"></i>\n            <span>设备管理</span>\n          </template>\n          <el-menu-item index=\"/admin/equipment\">\n            <i class=\"el-icon-s-management\"></i>\n            <span>设备列表</span>\n          </el-menu-item>\n          <el-menu-item index=\"/admin/category\">\n            <i class=\"el-icon-collection-tag\"></i>\n            <span>设备类别</span>\n          </el-menu-item>\n        </el-submenu>\n        <el-menu-item index=\"/admin/reservation\">\n          <i class=\"el-icon-s-order\"></i>\n          <span>预定管理</span>\n        </el-menu-item>\n        <el-menu-item index=\"/admin/announcement\">\n          <i class=\"el-icon-message-solid\"></i>\n          <span>公告管理</span>\n        </el-menu-item>\n        <el-submenu index=\"email-mobile\">\n          <template slot=\"title\">\n            <i class=\"el-icon-message\"></i>\n            <span>邮件管理</span>\n          </template>\n          <el-menu-item index=\"/admin/email/settings\">\n            <i class=\"el-icon-setting\"></i>\n            <span>邮件设置</span>\n          </el-menu-item>\n          <el-menu-item index=\"/admin/email/templates\">\n            <i class=\"el-icon-document\"></i>\n            <span>邮件模板</span>\n          </el-menu-item>\n          <el-menu-item index=\"/admin/email/logs\">\n            <i class=\"el-icon-tickets\"></i>\n            <span>邮件日志</span>\n          </el-menu-item>\n        </el-submenu>\n        <el-menu-item index=\"/admin/db-viewer\">\n          <i class=\"el-icon-view\"></i>\n          <span>数据库表查看</span>\n        </el-menu-item>\n        <el-menu-item index=\"/admin/system-logs\">\n          <i class=\"el-icon-document\"></i>\n          <span>系统日志</span>\n        </el-menu-item>\n        <el-menu-item index=\"/admin/accounts\">\n          <i class=\"el-icon-user\"></i>\n          <span>账号管理</span>\n        </el-menu-item>\n      </el-menu>\n\n      <!-- 底部操作按钮 -->\n      <div class=\"admin-mobile-nav-footer\">\n        <div class=\"admin-mobile-nav-actions\">\n          <el-button\n            type=\"primary\"\n            plain\n            icon=\"el-icon-s-home\"\n            size=\"small\"\n            @click=\"handleMobileCommand('home')\"\n            class=\"mobile-action-btn\"\n          >\n            返回首页\n          </el-button>\n          <el-button\n            type=\"danger\"\n            plain\n            icon=\"el-icon-switch-button\"\n            size=\"small\"\n            @click=\"handleMobileCommand('logout')\"\n            class=\"mobile-action-btn\"\n          >\n            退出登录\n          </el-button>\n        </div>\n      </div>\n    </div>\n    <el-container class=\"admin-container\">\n      <el-header class=\"admin-header\">\n        <div class=\"header-left\">\n          <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"header-logo\" />\n          <span class=\"header-title\">管理控制台</span>\n        </div>\n\n        <div class=\"header-menu\">\n          <el-menu\n            :default-active=\"activeMenu\"\n            class=\"top-menu\"\n            mode=\"horizontal\"\n            background-color=\"#FFFFFF\"\n            text-color=\"#333333\"\n            active-text-color=\"#409EFF\"\n            router\n          >\n            <el-menu-item index=\"/admin/dashboard\">\n              <i class=\"el-icon-s-home\"></i>\n              <span>控制台</span>\n            </el-menu-item>\n\n            <el-submenu index=\"equipment\">\n              <template slot=\"title\">\n                <i class=\"el-icon-s-grid\"></i>\n                <span>设备管理</span>\n              </template>\n              <el-menu-item index=\"/admin/equipment\">\n                <i class=\"el-icon-s-management\"></i>\n                <span>设备列表</span>\n              </el-menu-item>\n              <el-menu-item index=\"/admin/category\">\n                <i class=\"el-icon-collection-tag\"></i>\n                <span>设备类别</span>\n              </el-menu-item>\n            </el-submenu>\n\n            <el-menu-item index=\"/admin/reservation\">\n              <i class=\"el-icon-s-order\"></i>\n              <span>预定管理</span>\n            </el-menu-item>\n\n            <el-menu-item index=\"/admin/announcement\">\n              <i class=\"el-icon-message-solid\"></i>\n              <span>公告管理</span>\n            </el-menu-item>\n\n            <el-submenu index=\"email\">\n              <template slot=\"title\">\n                <i class=\"el-icon-message\"></i>\n                <span>邮件管理</span>\n              </template>\n              <el-menu-item index=\"/admin/email/settings\">\n                <i class=\"el-icon-setting\"></i>\n                <span>邮件设置</span>\n              </el-menu-item>\n              <el-menu-item index=\"/admin/email/templates\">\n                <i class=\"el-icon-document\"></i>\n                <span>邮件模板</span>\n              </el-menu-item>\n              <el-menu-item index=\"/admin/email/logs\">\n                <i class=\"el-icon-tickets\"></i>\n                <span>邮件日志</span>\n              </el-menu-item>\n            </el-submenu>\n\n            <el-menu-item index=\"/admin/db-viewer\">\n              <i class=\"el-icon-view\"></i>\n              <span>数据库表查看</span>\n            </el-menu-item>\n\n            <el-menu-item index=\"/admin/system-logs\">\n              <i class=\"el-icon-document\"></i>\n              <span>系统日志</span>\n            </el-menu-item>\n\n            <el-menu-item index=\"/admin/accounts\">\n              <i class=\"el-icon-user\"></i>\n              <span>账号管理</span>\n            </el-menu-item>\n          </el-menu>\n        </div>\n\n        <div class=\"header-right\">\n          <div class=\"user-info\">\n            <i class=\"el-icon-user\"></i>\n            <span>{{ displayUsername }}</span>\n          </div>\n\n\n          <el-button type=\"primary\" plain icon=\"el-icon-s-home\" size=\"small\" @click=\"handleCommand('home')\" class=\"home-btn\">返回首页</el-button>\n          <el-button type=\"danger\" plain icon=\"el-icon-switch-button\" size=\"small\" @click=\"handleCommand('logout')\" class=\"logout-btn\">退出登录</el-button>\n        </div>\n      </el-header>\n\n      <el-main class=\"admin-main\">\n        <keep-alive>\n          <router-view></router-view>\n        </keep-alive>\n      </el-main>\n    </el-container>\n  </div>\n</template>\n\n<script>\nimport { mapGetters, mapActions } from 'vuex'\nimport { updatePageTitle } from '@/router/permission'\n\nexport default {\n  name: 'AdminLayout',\n\n  data() {\n    return {\n      mobileMenuOpen: false\n    }\n  },\n\n  computed: {\n    ...mapGetters(['currentUser', 'getLanguage']),\n\n    activeMenu() {\n      return this.$route.path\n    },\n\n    currentLanguage() {\n      return this.getLanguage\n    },\n\n    displayUsername() {\n      // 优先显示用户名，如果没有则显示'管理员'\n      return this.currentUser && this.currentUser.username ? this.currentUser.username : '管理员'\n    },\n\n    isSuperAdmin() {\n      return this.currentUser && this.currentUser.role === 'superadmin'\n    }\n  },\n\n  methods: {\n    ...mapActions(['logout', 'setLanguage']),\n\n    handleCommand(command) {\n      if (command === 'logout') {\n        this.handleLogout()\n      } else if (command === 'home') {\n        this.$router.push('/')\n      }\n    },\n\n    handleLogout() {\n      this.logout()\n      this.$message.success('退出登录成功')\n      this.$router.push('/admin/login')\n    },\n\n    handleLanguageChange(lang) {\n      this.setLanguage(lang)\n      this.$i18n.locale = lang\n\n      // 更新页面标题\n      setTimeout(() => {\n        updatePageTitle()\n      }, 0)\n    },\n\n    handleMobileMenuSelect(key) {\n      this.$router.push(key)\n      this.mobileMenuOpen = false\n    },\n\n    handleMobileCommand(command) {\n      if (command === 'logout') {\n        this.handleLogout()\n      } else if (command === 'home') {\n        this.$router.push('/')\n      }\n      this.mobileMenuOpen = false\n    },\n\n    toggleMobileMenu() {\n      // 这里可以实现折叠菜单的逻辑，或 emit 事件给父组件\n      const menu = document.querySelector('.header-menu')\n      if (menu) {\n        menu.style.display = (menu.style.display === 'none') ? '' : 'none'\n      }\n    }\n  }\n}\n</script>\n\n<style>\n.admin-layout {\n  height: 100vh;\n  width: 100vw;\n  overflow: hidden;\n  display: flex;\n  margin: 0;\n  padding: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  box-sizing: border-box;\n}\n\n.admin-container {\n  width: 100%;\n  height: 100%;\n  margin: 0;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n}\n\n.admin-header,\n.el-header {\n  background-color: #FFFFFF !important;\n  color: #333333 !important;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0;\n  height: 60px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  border-bottom: 1px solid #EBEEF5;\n}\n\n.header-left {\n  display: flex;\n  align-items: center;\n  padding: 0 20px;\n}\n\n.header-logo {\n  height: 32px;\n  margin-right: 10px;\n}\n\n.header-title {\n  font-size: 18px;\n  font-weight: bold;\n  color: #409EFF;\n  white-space: nowrap;\n}\n\n.header-menu {\n  flex: 1;\n  display: flex;\n  justify-content: center;\n}\n\n.top-menu {\n  background-color: #FFFFFF !important;\n  border-bottom: none;\n}\n\n.top-menu .el-menu-item {\n  height: 60px;\n  line-height: 60px;\n}\n\n.top-menu .el-submenu__title {\n  height: 60px;\n  line-height: 60px;\n}\n\n.header-right {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 0 20px;\n}\n\n.language-switcher {\n  display: flex;\n  align-items: center;\n  margin-right: 15px;\n}\n\n.lang-btn {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px 8px;\n  color: #606266;\n  font-size: 14px;\n}\n\n.lang-btn.active {\n  color: #409EFF;\n  font-weight: bold;\n}\n\n.divider {\n  color: #DCDFE6;\n  margin: 0 5px;\n  opacity: 0.7;\n}\n\n.user-info {\n  display: flex;\n  align-items: center;\n  color: #606266;\n  margin-right: 15px;\n  background-color: #F5F7FA;\n  padding: 5px 12px;\n  border-radius: 4px;\n  font-size: 14px;\n  border: 1px solid #DCDFE6;\n}\n\n.user-info i {\n  margin-right: 5px;\n}\n\n.header-right .el-button {\n  margin-left: 0;\n  white-space: nowrap;\n}\n\n.admin-main {\n  background-color: #f0f2f5;\n  padding: 20px;\n  overflow-y: auto;\n  height: calc(100vh - 60px);\n  width: 100%;\n  max-width: 100%;\n}\n\n@media (max-width: 768px) {\n  .admin-layout {\n    position: relative;\n    top: 0;\n    left: 0;\n  }\n\n  .admin-header,\n  .el-header {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    z-index: 1000;\n    width: 100%;\n    height: 60px;\n  }\n\n  .admin-main {\n    margin-top: 60px;\n    height: calc(100vh - 60px);\n    padding-top: 20px;\n  }\n\n  /* 完全隐藏移动端的顶部菜单栏 */\n  .admin-header,\n  .el-header {\n    display: none !important;\n  }\n\n  .admin-main {\n    margin-top: 0 !important;\n    height: 100vh !important;\n    padding-top: 20px;\n  }\n\n  .admin-mobile-nav-overlay {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100vw;\n    height: 100vh;\n    background: rgba(0,0,0,0.5);\n    z-index: 2999;\n  }\n  .admin-mobile-nav-drawer {\n    position: fixed;\n    top: 0;\n    right: 0;\n    width: 280px;\n    height: 100vh;\n    background: #304156;\n    z-index: 3000;\n    display: flex;\n    flex-direction: column;\n    transition: right 0.3s ease;\n    box-shadow: -2px 0 8px rgba(0,0,0,0.1);\n    overflow: hidden;\n  }\n  .admin-mobile-nav-header {\n    height: 60px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    padding: 0 20px;\n    background-color: #263445;\n    position: relative;\n  }\n\n  .admin-mobile-nav-logo {\n    width: 32px;\n    height: 32px;\n    margin-right: 10px;\n  }\n\n  .admin-mobile-nav-title {\n    margin: 0;\n    font-size: 16px;\n    font-weight: 600;\n    color: #fff;\n    flex: 1;\n  }\n\n  .admin-mobile-nav-close {\n    width: 32px;\n    height: 32px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    color: #bfcbd9;\n    border-radius: 4px;\n    transition: all 0.3s ease;\n  }\n\n  .admin-mobile-nav-close:hover {\n    background-color: #1f2d3d;\n    color: #fff;\n  }\n\n  .admin-mobile-user-info {\n    padding: 20px;\n    background-color: #263445;\n    border-bottom: 1px solid #1f2d3d;\n    display: flex;\n    align-items: center;\n  }\n\n  .user-avatar {\n    width: 48px;\n    height: 48px;\n    border-radius: 50%;\n    background-color: #409EFF;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-right: 15px;\n    color: #fff;\n    font-size: 20px;\n  }\n\n  .user-details {\n    flex: 1;\n  }\n\n  .user-name {\n    font-size: 16px;\n    font-weight: 600;\n    color: #fff;\n    margin-bottom: 4px;\n  }\n\n  .user-role {\n    font-size: 12px;\n    color: #bfcbd9;\n  }\n  .admin-mobile-nav-list {\n    flex: 1;\n    border-right: none;\n    background: #304156;\n    overflow-y: auto;\n    max-height: calc(100vh - 200px); /* 为header和footer留出空间 */\n  }\n  .admin-mobile-nav-footer {\n    padding: 15px 20px;\n    background-color: #263445;\n    border-top: 1px solid #1f2d3d;\n    flex-shrink: 0; /* 防止被压缩 */\n    margin-top: auto; /* 推到底部 */\n  }\n\n  .admin-mobile-nav-actions {\n    display: flex;\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .mobile-action-btn {\n    width: 100% !important;\n    margin: 0 !important;\n    padding: 12px 16px !important;\n    font-size: 14px !important;\n    border-radius: 6px !important;\n    font-weight: 500 !important;\n    transition: all 0.3s ease !important;\n  }\n\n  .mobile-action-btn.el-button--primary {\n    background-color: transparent !important;\n    border-color: #409EFF !important;\n    color: #409EFF !important;\n  }\n\n  .mobile-action-btn.el-button--primary:hover {\n    background-color: #409EFF !important;\n    color: #fff !important;\n  }\n\n  .mobile-action-btn.el-button--danger {\n    background-color: transparent !important;\n    border-color: #F56C6C !important;\n    color: #F56C6C !important;\n  }\n\n  .mobile-action-btn.el-button--danger:hover {\n    background-color: #F56C6C !important;\n    color: #fff !important;\n  }\n  .admin-mobile-nav-toggle {\n    position: fixed;\n    top: 50%;\n    right: 15px;\n    transform: translateY(-50%);\n    width: 50px;\n    height: 50px;\n    border-radius: 50%;\n    background-color: #409EFF;\n    color: #fff;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    cursor: pointer;\n    z-index: 3001;\n    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);\n    transition: all 0.3s ease;\n    font-size: 18px;\n  }\n\n  .admin-mobile-nav-toggle:hover {\n    background-color: #337ecc;\n    transform: translateY(-50%) scale(1.1);\n    box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);\n  }\n\n  .admin-mobile-nav-toggle:active {\n    transform: translateY(-50%) scale(0.95);\n  }\n}\n</style>\n"], "mappings": "AAiOA,SAAAA,UAAA,EAAAC,UAAA;AACA,SAAAC,eAAA;AAEA;EACAC,IAAA;EAEAC,KAAA;IACA;MACAC,cAAA;IACA;EACA;EAEAC,QAAA;IACA,GAAAN,UAAA;IAEAO,WAAA;MACA,YAAAC,MAAA,CAAAC,IAAA;IACA;IAEAC,gBAAA;MACA,YAAAC,WAAA;IACA;IAEAC,gBAAA;MACA;MACA,YAAAC,WAAA,SAAAA,WAAA,CAAAC,QAAA,QAAAD,WAAA,CAAAC,QAAA;IACA;IAEAC,aAAA;MACA,YAAAF,WAAA,SAAAA,WAAA,CAAAG,IAAA;IACA;EACA;EAEAC,OAAA;IACA,GAAAhB,UAAA;IAEAiB,cAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAC,YAAA;MACA,WAAAD,OAAA;QACA,KAAAE,OAAA,CAAAC,IAAA;MACA;IACA;IAEAF,aAAA;MACA,KAAAG,MAAA;MACA,KAAAC,QAAA,CAAAC,OAAA;MACA,KAAAJ,OAAA,CAAAC,IAAA;IACA;IAEAI,qBAAAC,IAAA;MACA,KAAAC,WAAA,CAAAD,IAAA;MACA,KAAAE,KAAA,CAAAC,MAAA,GAAAH,IAAA;;MAEA;MACAI,UAAA;QACA7B,eAAA;MACA;IACA;IAEA8B,uBAAAC,GAAA;MACA,KAAAZ,OAAA,CAAAC,IAAA,CAAAW,GAAA;MACA,KAAA5B,cAAA;IACA;IAEA6B,oBAAAf,OAAA;MACA,IAAAA,OAAA;QACA,KAAAC,YAAA;MACA,WAAAD,OAAA;QACA,KAAAE,OAAA,CAAAC,IAAA;MACA;MACA,KAAAjB,cAAA;IACA;IAEA8B,iBAAA;MACA;MACA,MAAAC,IAAA,GAAAC,QAAA,CAAAC,aAAA;MACA,IAAAF,IAAA;QACAA,IAAA,CAAAG,KAAA,CAAAC,OAAA,GAAAJ,IAAA,CAAAG,KAAA,CAAAC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}